const functions = require("firebase-functions");
const admin = require("firebase-admin");
admin.initializeApp();
// orders
exports.updateOrderCountOnCreate = functions.firestore
    .document("orders/{orderId}")
    .onCreate((snap, context) =>{
      const docRef = admin.firestore().doc("count/orders");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount++;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

exports.updateOrderCountOnDelete = functions.firestore
    .document("orders/{orderId}")
    .onDelete((snap, context) =>{
      const docRef = admin.firestore().doc("count/orders");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount--;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

// users
exports.updateUserCountOnCreate = functions.firestore
    .document("users/{userId}")
    .onCreate((snap, context) =>{
      const docRef = admin.firestore().doc("count/users");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount++;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

exports.updateUserCountOnDelete = functions.firestore
    .document("users/{userId}")
    .onDelete((snap, context) =>{
      const docRef = admin.firestore().doc("count/users");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount--;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

// clients
exports.updateClientCountOnCreate = functions.firestore
    .document("clients/{clientId}")
    .onCreate((snap, context) =>{
      const docRef = admin.firestore().doc("count/clients");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount++;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

exports.updateClientCountOnDelete = functions.firestore
    .document("clients/{clientId}")
    .onDelete((snap, context) =>{
      const docRef = admin.firestore().doc("count/clients");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount--;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

// products
exports.updateProductCountOnCreate = functions.firestore
    .document("products/{productId}")
    .onCreate((snap, context) =>{
      const docRef = admin.firestore().doc("count/products");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount++;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

exports.updateProductCountOnDelete = functions.firestore
    .document("products/{productId}")
    .onDelete((snap, context) =>{
      const docRef = admin.firestore().doc("count/products");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount--;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

// sales_routes
exports.updateSalesRouteCountOnCreate = functions.firestore
    .document("sales_routes/{salesRouteId}")
    .onCreate((snap, context) =>{
      const docRef = admin.firestore().doc("count/sales_routes");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount++;
          transaction.update(docRef, {value: newCount});
        });
      });
    });

exports.updateSalesRouteCountOnDelete = functions.firestore
    .document("sales_routes/{salesRouteId}")
    .onDelete((snap, context) =>{
      const docRef = admin.firestore().doc("count/sales_routes");
      return admin.firestore().runTransaction((transaction) =>{
        return transaction.get(docRef).then((doc) =>{
          let newCount = doc.data().value || 0;
          newCount--;
          transaction.update(docRef, {value: newCount});
        });
      });
    });
