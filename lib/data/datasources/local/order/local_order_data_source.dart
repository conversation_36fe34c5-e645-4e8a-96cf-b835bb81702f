import 'package:fl_app/models/order_model.dart';

abstract class LocalOrderDataSource {
  Future<void> initialize();
  Future<void> cacheOrders(List<OrderModel> orders);
  Future<void> saveOrder(OrderModel order);
  Future<OrderModel?> getCachedOrder(String id);
  List<OrderModel> getCachedOrders({String? routeId});
  Future<void> clearCache();
  Future<List<OrderModel>> getDeletedCachedOrders({String? routeId});
  Future<List<OrderModel>> getCachedDeliveryOrders();
  Future<void> removeOrdersByIds(List<String> ids);
}
