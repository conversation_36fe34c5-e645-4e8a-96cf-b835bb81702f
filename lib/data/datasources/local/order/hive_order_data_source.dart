import 'dart:developer';
import 'package:fl_app/data/datasources/local/order/local_order_data_source.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';

Map<String, OrderModel> _loadMemoryCacheIsolate(Map<dynamic, dynamic> boxData) {
  final memoryCache = <String, OrderModel>{};
  for (var entry in boxData.entries) {
    final id = entry.key as String;
    final data = entry.value as Map<dynamic, dynamic>;
    memoryCache[id] = OrderModel.fromCache(Map<String, dynamic>.from(data));
  }
  return memoryCache;
}

class HiveOrderDataSource implements LocalOrderDataSource {
  Box? box;

  Map<String, OrderModel> _memoryCache = {};

  @override
  Future<void> initialize() async {
    box ??= await Hive.openBox('orders');
    await _loadMemoryCache();
  }

  Future<void> _loadMemoryCache() async {
    if (box == null) return;

    log('Carregando pedidos em memória');
    final entries = box!.toMap().entries.toList();
    final chunkSize = (entries.length / 4).ceil(); // Dividindo em 4 partes

    final tasks = List.generate(4, (i) {
      final subList = entries.skip(i * chunkSize).take(chunkSize).toList();
      return compute(_loadMemoryCacheIsolate, Map.fromEntries(subList));
    });

    final results = await Future.wait(tasks);
    _memoryCache = results.fold({}, (prev, curr) => {...prev, ...curr});
    log('Pedidos carregados na memória: ${_memoryCache.length}');
  }

  @override
  Future<void> cacheOrders(List<OrderModel> orders) async {
    box ??= await Hive.openBox('orders');
    Map<String, Map<String, dynamic>> ordersMap = {};
    for (var order in orders) {
      ordersMap[order.id!] = order.toMap();
      _memoryCache[order.id!] = order; // Atualiza o cache em memória
    }
    await box!.putAll(ordersMap);
    log('Pedidos salvos em cache (Hive): ${orders.length}');
  }

  @override
  Future<void> saveOrder(OrderModel order) async {
    box ??= await Hive.openBox('orders');
    await box!.put(order.id, order.toMap());
    _memoryCache[order.id!] = order; // Atualiza o cache em memória
    log('Pedido salvo em cache (Hive): ${order.id}');
  }

  @override
  Future<OrderModel?> getCachedOrder(String id) async {
    if (_memoryCache.containsKey(id)) {
      return _memoryCache[id]; // Retorna do cache em memória
    }

    box ??= await Hive.openBox('orders');
    if (!box!.containsKey(id)) return null;
    final e = box!.get(id);
    if (e == null) return null;
    return OrderModel.fromCache(Map<String, dynamic>.from(e as Map));
  }

  @override
  List<OrderModel> getCachedOrders({String? routeId}) {
    var orders = _memoryCache.values.toList();

    if (routeId != null && routeId.isNotEmpty) {
      orders = orders.where((order) => order.routeId == routeId).toList();
    }

    orders = orders.where((order) => !order.isDeleted).toList();
    orders.sort((a, b) => b.date.compareTo(a.date));
    return orders;
  }

  @override
  Future<void> clearCache() async {
    box ??= await Hive.openBox('orders');
    _memoryCache.clear();
    await box!.clear();
    log('Cache de pedidos (Hive e memória) limpo');
  }

  @override
  Future<List<OrderModel>> getDeletedCachedOrders({String? routeId}) async {
    var orders = _memoryCache.values.where((o) => o.isDeleted).toList();

    if (routeId != null && routeId.isNotEmpty) {
      orders = orders.where((o) => o.routeId == routeId).toList();
    }

    orders.sort((a, b) => b.date.compareTo(a.date));
    return orders;
  }

  @override
  Future<List<OrderModel>> getCachedDeliveryOrders() async {
    return _memoryCache.values
        .where((order) =>
            order.toDelivery ||
            order.products.any((product) => product.quantityToDelivery > 0))
        .toList();
  }

  @override
  Future<void> removeOrdersByIds(List<String> ids) async {
    box ??= await Hive.openBox('orders');

    for (var id in ids) {
      _memoryCache.remove(id); // Remove do cache em memória
      await box!.delete(id); // Remove do Hive
    }

    log('Pedidos removidos do cache (Hive e memória): ${ids.length}');
  }
}
