// lib/data/datasources/local/hive_product_data_source.dart

import 'package:diacritic/diacritic.dart';
import 'package:fl_app/data/datasources/local/product/local_product_data_source.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:hive_flutter/hive_flutter.dart';

class HiveProductDataSource implements LocalProductDataSource {
  final Box box;

  HiveProductDataSource({required this.box});

  @override
  Future<void> cacheProducts(List<ProductModel> products) async {
    for (var product in products) {
      await box.put(product.id, product.toMap());
    }
  }

  @override
  Future<void> saveProduct(ProductModel newProduct) async {
    await box.put(newProduct.id, newProduct.toMap());
  }

  @override
  Future<List<ProductModel>> getCachedProducts() async {
    List<ProductModel> products = box.values
        .map((e) => ProductModel.fromMap(e))
        .where((product) => !product.isDeleted)
        .toList();

    products = sortProductsList(products);
    return products;
  }

  List<ProductModel> sortProductsList(List<ProductModel> products) {
    products.sort((a, b) {
      if ((a.isFavorite && b.isFavorite) || (!a.isFavorite && !b.isFavorite)) {
        return removeDiacritics(a.nome)
            .toLowerCase()
            .compareTo(removeDiacritics(b.nome).toLowerCase());
      }
      // Se 'a' é favorito e 'b' não é, 'a' deve vir primeiro
      if (a.isFavorite && !b.isFavorite) {
        return -1;
      }
      // Se 'b' é favorito e 'a' não é, 'b' deve vir primeiro
      if (!a.isFavorite && b.isFavorite) {
        return 1;
      }
      return 0;
    });

    return products;
  }

  @override
  Future<List<ProductModel>> getCachedDeletedProducts() async {
    List<ProductModel> deletedProducts = box.values
        .map((e) => ProductModel.fromMap(e))
        .where((product) => product.isDeleted)
        .toList();

    deletedProducts = sortProductsList(deletedProducts);
    return deletedProducts;
  }

  @override
  Future<ProductModel?> getCachedProduct(String id) async {
    if (!box.containsKey(id)) return null;
    return ProductModel.fromMap(box.get(id));
  }

  @override
  Future<void> clearCache() async {
    await box.clear();
  }
}
