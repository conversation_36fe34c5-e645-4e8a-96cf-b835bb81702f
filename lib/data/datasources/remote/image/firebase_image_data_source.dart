import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:uuid/uuid.dart';
import './remote_image_data_source.dart';

class FirebaseImageDataSource implements RemoteImageDataSource {
  final FirebaseStorage firebaseStorage;
  final ConnectionService connectionService;

  FirebaseImageDataSource(
      {FirebaseStorage? firebaseStorage, required this.connectionService})
      : firebaseStorage = firebaseStorage ?? FirebaseStorage.instance;

  @override
  Future<String?> uploadImage(
      String? imagePath, String cloudPathTemplate) async {
    try {
      if (imagePath == null || imagePath.isEmpty) return null;

      final hasConnection = await connectionService.checkConnection();
      if (!hasConnection) return null;

      try {
        final cloudPath = cloudPathTemplate.replaceAll(
          '{uniqueId}',
          const Uuid().v4().substring(0, 10),
        );
        final ref = firebaseStorage.ref().child(cloudPath);
        final uploadTask = ref.putFile(File(imagePath));
        final snapshot = await uploadTask.whenComplete(() => null);
        return await snapshot.ref.getDownloadURL();
      } catch (e) {
        throw Exception('Erro ao fazer upload da imagem: $e');
      }
    } catch (e) {
      throw Exception('Erro ao fazer upload da imagem: $e');
    }
  }

  @override
  Future<void> deleteImage(String path) async {
    try {
      final ref = firebaseStorage.refFromURL(path);
      await ref.delete();
    } catch (e) {
      throw Exception('Erro ao deletar a imagem: $e');
    }
  }
}
