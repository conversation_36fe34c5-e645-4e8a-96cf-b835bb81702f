import 'package:fl_app/models/product_model.dart';

abstract class RemoteProductDataSource {
  Future<List<ProductModel>> fetchProducts(String userId);
  Future<List<ProductModel>> fetchAllProducts();
  String addProduct(ProductModel product);
  Future<void> updateProduct(ProductModel product);
  Future<void> updateReceivedBy(ProductModel product);
  Future<void> deleteProduct(String productId);
  Future<ProductModel> getProductById(String id);
}
