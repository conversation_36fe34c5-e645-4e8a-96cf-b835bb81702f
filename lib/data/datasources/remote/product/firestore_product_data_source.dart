import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/data/datasources/remote/product/remote_product_data_source.dart';
import 'package:fl_app/models/product_model.dart';

class FirestoreProductDataSource implements RemoteProductDataSource {
  final FirebaseFirestore firestore;

  FirestoreProductDataSource({required this.firestore});

  CollectionReference get collection => firestore.collection('products');

  @override
  String addProduct(ProductModel product) {
    final newProductRef = collection.doc();
    newProductRef.set(product.toDocument());
    return newProductRef.id;
  }

  @override
  Future<void> deleteProduct(String productId) async {
    await firestore.collection('products').doc(productId).delete();
  }

  @override
  Future<ProductModel> getProductById(String id) async {
    final doc = await firestore.collection('products').doc(id).get();
    if (doc.exists) {
      return ProductModel.fromDocument(doc.data()!);
    } else {
      throw Exception('Produto não encontrado');
    }
  }

  @override
  Future<List<ProductModel>> fetchProducts(String userId) async {
    final querySnapshot = await firestore
        .collection('products')
        .where('toReceivedBy', arrayContains: userId)
        .get();

    return querySnapshot.docs.map((doc) {
      var product = ProductModel.fromDocument(doc.data());
      product.id = doc.id;
      return product;
    }).toList();
  }

  @override
  Future<List<ProductModel>> fetchAllProducts() async {
    final querySnapshot = await firestore.collection('products').get();

    return querySnapshot.docs.map((doc) {
      var product = ProductModel.fromDocument(doc.data());
      product.id = doc.id;
      return product;
    }).toList();
  }

  @override
  Future<void> updateProduct(ProductModel product) async {
    await firestore
        .collection('products')
        .doc(product.id)
        .update(product.toDocument());
  }

  @override
  Future<void> updateReceivedBy(ProductModel product) async {
    await collection
        .doc(product.id)
        .update({'toReceivedBy': product.toReceivedBy});
  }
}
