import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/order_model.dart';
import 'remote_order_data_source.dart';

class FirestoreOrderDataSource implements RemoteOrderDataSource {
  final FirebaseFirestore firestore;

  FirestoreOrderDataSource({required this.firestore});

  CollectionReference get collection => firestore.collection('orders');

  @override
  String addOrder(OrderModel order) {
    final newOrderRef = collection.doc();
    newOrderRef.set(order.toDocument());
    return newOrderRef.id;
  }

  @override
  Future<void> updateOrder(OrderModel order) {
    return collection.doc(order.id).update(order.toDocument());
  }

  @override
  Future<void> updateClientInfo(OrderModel order) {
    return collection.doc(order.id).update(order.toDocumentClientInfo());
  }

  @override
  Future<void> updatePayments(OrderModel order) {
    return collection.doc(order.id).update(order.toDocumentPayments());
  }

  @override
  Future<void> updateDayMarking(OrderModel order) {
    return collection.doc(order.id).update(order.toDocumentDayMarking());
  }

  @override
  Future<void> updateProducts(OrderModel order) {
    return collection.doc(order.id).update(order.toDocumentProducts());
  }

  @override
  Future<void> updateReceivedBy(OrderModel order) {
    return collection.doc(order.id).update(order.toDocumentReceivedBy());
  }

  @override
  Future<List<OrderModel>> fetchOrdersToReceive(String userId) async {
    final querySnapshot =
        await collection.where('toReceivedBy', arrayContains: userId).get();

    return querySnapshot.docs.map((doc) {
      var order = OrderModel.fromDocument(doc);
      order.id = doc.id;
      return order;
    }).toList();
  }

  @override
  Future<List<OrderModel>> fetchAllOrdersForCache(
      DateTime sixMonthsAgo, DateTime fourMonthsAgo, int batchSize) async {
    List<OrderModel> allOrders = [];
    Query query = collection
        .where(
          Filter.or(
            Filter.and(
              Filter("isPaid", isEqualTo: false),
              Filter("isJoined", isEqualTo: false),
              Filter("isDeleted", isEqualTo: false),
            ),
            Filter.and(
              Filter("isPaid", isEqualTo: true),
              Filter("lastModified", isGreaterThan: sixMonthsAgo),
            ),
            Filter.and(
              Filter("isDeleted", isEqualTo: true),
              Filter("lastModified", isGreaterThan: fourMonthsAgo),
            ),
            Filter.and(
              Filter("isJoined", isEqualTo: true),
              Filter("lastModified", isGreaterThan: sixMonthsAgo),
            ),
          ),
        )
        .orderBy('lastModified')
        .limit(batchSize);

    DocumentSnapshot? lastDocument;

    while (true) {
      Query currentQuery = query;
      if (lastDocument != null) {
        currentQuery = currentQuery.startAfterDocument(lastDocument);
      }

      QuerySnapshot querySnapshot = await currentQuery.get();
      if (querySnapshot.docs.isEmpty) break;

      for (var doc in querySnapshot.docs) {
        var data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        var order = OrderModel.fromDocumentMap(data);
        allOrders.add(order);
      }

      lastDocument = querySnapshot.docs.last;
      if (querySnapshot.docs.length < batchSize) break;
    }

    return allOrders;
  }

  @override
  Future<List<OrderModel>> fetchOrdersByRoutes(List<String> routesIds) async {
    final querySnapshot = await collection
        .where(
          Filter.or(
            Filter("routeSaleId", whereIn: routesIds),
            Filter("routeId", whereIn: routesIds),
          ),
        )
        .get();

    return querySnapshot.docs.map((doc) {
      var order = OrderModel.fromDocument(doc);
      order.id = doc.id;
      return order;
    }).toList();
  }
}
