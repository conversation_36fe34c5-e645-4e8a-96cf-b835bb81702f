import 'package:fl_app/models/order_model.dart';

abstract class RemoteOrderDataSource {
  String addOrder(OrderModel order);
  Future<void> updateOrder(OrderModel order);
  Future<void> updateClientInfo(OrderModel order);
  Future<void> updatePayments(OrderModel order);
  Future<void> updateDayMarking(OrderModel order);
  Future<void> updateProducts(OrderModel order);
  Future<void> updateReceivedBy(OrderModel order);
  Future<List<OrderModel>> fetchOrdersToReceive(String userId);
  Future<List<OrderModel>> fetchAllOrdersForCache(
      DateTime sixMonthsAgo, DateTime fourMonthsAgo, int batchSize);
  Future<List<OrderModel>> fetchOrdersByRoutes(List<String> routesIds);
}
