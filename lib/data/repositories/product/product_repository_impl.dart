import 'dart:async';
import 'dart:developer';

import 'package:fl_app/data/datasources/local/product/local_product_data_source.dart';
import 'package:fl_app/data/datasources/remote/image/remote_image_data_source.dart';
import 'package:fl_app/data/datasources/remote/product/remote_product_data_source.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:get/get.dart';
import 'package:uuid/uuid.dart';

import 'product_repository.dart';

class ProductRepositoryImpl implements ProductRepository {
  final RemoteProductDataSource remoteDataSource;
  final LocalProductDataSource localDataSource;
  final UserRepository userRepository;
  final ConnectionService connectionService;
  final RemoteImageDataSource imageDataSource;
  final OfflineSyncRepositoryImpl offlineSyncRepository;

  ProductRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.userRepository,
    required this.connectionService,
    required this.imageDataSource,
    required this.offlineSyncRepository,
  }) {
    init();
  }

  UserModel? user;

  Future<void> init() async {
    user = await userRepository.getUserAuthenticated();
  }

  @override
  Future<ProductModel> addProduct(ProductModel product,
      {String? imagePath}) async {
    try {
      if (imagePath != null && imagePath.isNotEmpty) {
        final imageUrl = await imageDataSource.uploadImage(
          imagePath,
          '/products/${product.nome}-{uniqueId}.jpg',
        );
        product.imagem = imageUrl;
      }

      List<VariationModel> updatedVariations = [];
      for (var variation in product.variations) {
        if (variation.localPath != null && variation.localPath!.isNotEmpty) {
          // Faz upload da imagem da variação
          final variationImageUrl = await imageDataSource.uploadImage(
            variation.localPath!,
            '/products/${product.nome}/${variation.name}-${const Uuid().v4().substring(0, 10)}.jpg',
          );

          // Atualiza a variação com a imageUrl e limpa o localPath
          variation.imageUrl = variationImageUrl ?? variation.imageUrl;
          variation.localPath = null;
          updatedVariations.add(variation);
        } else {
          // Variação sem imagem nova ou já tem imageUrl
          updatedVariations.add(variation);
        }
      }

      product.variations = updatedVariations;

      final newProduct = ProductModel(
        nome: product.nome,
        imagem: product.imagem,
        valor: product.valor,
        temComissao: product.temComissao,
        lastModified: DateTime.now(),
        toReceivedBy: await userRepository.getOtherUsersIds(),
        variations: product.variations,
      );

      newProduct.id = remoteDataSource.addProduct(newProduct);
      await localDataSource.saveProduct(newProduct);
      offlineSyncRepository.addProductToSync(newProduct);
      return product;
    } catch (e) {
      log('Erro ao adicionar produto: $e');
      throw Exception('Erro ao adicionar produto $e');
    }
  }

  @override
  Future<ProductModel> deleteProduct(ProductModel product) async {
    try {
      final updatedProduct = product.copyWith(
        isDeleted: true,
        deletedById: user!.id,
        deletedByName: user!.name,
        lastModified: DateTime.now(),
        toReceivedBy: await userRepository.getOtherUsersIds(),
      );

      return await updateProduct(updatedProduct);
    } catch (e) {
      log('Erro ao excluir produto: $e');
      throw Exception('Erro ao excluir produto $e');
    }
  }

  Future<void> receiveProductsAndSave(List<ProductModel> products) async {
    for (var product in products) {
      product.toReceivedBy =
          product.toReceivedBy.where((element) => element != user!.id).toList();
      remoteDataSource.updateReceivedBy(product);
    }
    await localDataSource.cacheProducts(products);
  }

  @override
  Future<ProductModel?> getProduct(String id) async {
    return localDataSource.getCachedProduct(id);
  }

  @override
  Future<List<ProductModel>> getProducts() async {
    try {
      if ((await localDataSource.getCachedProducts()).isNotEmpty) {
        log('Buscando produtos em cache e verificando atualizações');
        final remoteProducts = await remoteDataSource.fetchProducts(user!.id);
        if (remoteProducts.isNotEmpty) {
          await receiveProductsAndSave(remoteProducts);
        }
      } else {
        log('Buscando produtos no Firestore');
        await updateCache();
      }
      return localDataSource.getCachedProducts();
    } catch (e) {
      if (await Get.find<UserRepository>().isLogged()) {
        log('Erro ao obter produtos: $e');
        throw Exception('Erro ao obter produtos $e');
      }
      log('Erro ao obter produtos: $e');
      return localDataSource.getCachedProducts();
    }
  }

  @override
  Future<void> updateCache() async {
    log('Atualizando cache de produtos (Firestore)');
    final remoteProducts = await remoteDataSource.fetchAllProducts();
    await localDataSource.clearCache();
    await localDataSource.cacheProducts(remoteProducts);
  }

  @override
  Future<ProductModel> updateProduct(ProductModel product,
      {String? imagePath}) async {
    try {
      if (imagePath != null && imagePath.isNotEmpty) {
        final newImageUrl = await imageDataSource.uploadImage(
          imagePath,
          '/products/${product.nome}-{uniqueId}.jpg',
        );

        if (newImageUrl != null) {
          if (product.imagem != null && product.imagem!.isNotEmpty) {
            await imageDataSource.deleteImage(product.imagem!);
          }
          product.imagem = newImageUrl;
        }
      }

      List<VariationModel> updatedVariations = [];
      for (var variation in product.variations) {
        if (variation.localPath != null && variation.localPath!.isNotEmpty) {
          // Faz upload da imagem da variação
          final variationImageUrl = await imageDataSource.uploadImage(
            variation.localPath!,
            '/products/${product.nome}/${variation.name}-${const Uuid().v4().substring(0, 10)}.jpg',
          );

          // Atualiza a variação com a imageUrl e limpa o localPath
          variation.imageUrl = variationImageUrl ?? variation.imageUrl;
          variation.localPath = null;
          updatedVariations.add(variation);
        } else {
          // Variação sem imagem nova ou já tem imageUrl
          updatedVariations.add(variation);
        }
      }

      product.variations = updatedVariations;

      final updatedProduct = product.copyWith(
        lastModified: DateTime.now(),
        toReceivedBy: await userRepository.getOtherUsersIds(),
      );

      remoteDataSource.updateProduct(updatedProduct);
      await localDataSource.saveProduct(updatedProduct);
      offlineSyncRepository.addProductToSync(updatedProduct);
      return updatedProduct;
    } catch (e) {
      log('Erro ao atualizar produto: $e');
      throw Exception('Erro ao atualizar produto $e');
    }
  }

  @override
  Future<List<ProductModel>> getProductsFromCache(
      {bool withDeleted = false}) async {
    getProducts();
    if (withDeleted) {
      var products = await localDataSource.getCachedProducts();
      var deletedProducts = await localDataSource.getCachedDeletedProducts();
      return [...products, ...deletedProducts];
    }
    return localDataSource.getCachedProducts();
  }

  @override
  Future<List<ProductModel>> getDeletedProductsFromCache() async {
    getProducts();
    return localDataSource.getCachedDeletedProducts();
  }

  @override
  Future<void> syncProduct(ProductModel productModel) async {
    try {
      var productCache =
          await localDataSource.getCachedProduct(productModel.id!);
      if (productCache == null) {
        await localDataSource.saveProduct(productModel);
      } else {
        if (productCache.lastModified.isBefore(productModel.lastModified)) {
          await localDataSource.saveProduct(productModel);
        }
      }
    } catch (e) {
      log('Erro ao sincronizar produto: $e');
      throw Exception('Erro ao sincronizar produto $e');
    }
  }

  @override
  Future<ProductModel> restoreProduct(ProductModel product) async {
    try {
      final restoredProduct = product.copyWith(
        isDeleted: false,
        deletedById: null,
        deletedByName: null,
        lastModified: DateTime.now(),
        toReceivedBy: await userRepository.getOtherUsersIds(),
      );

      return await updateProduct(restoredProduct);
    } catch (e) {
      log('Erro ao restaurar produto: $e');
      throw Exception('Erro ao restaurar produto $e');
    }
  }
}
