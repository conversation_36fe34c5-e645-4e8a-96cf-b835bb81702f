import 'package:fl_app/models/product_model.dart';

abstract class ProductRepository {
  Future<List<ProductModel>> getProducts();

  Future<List<ProductModel>> getProductsFromCache({bool withDeleted = false});
  Future<List<ProductModel>> getDeletedProductsFromCache();

  Future<ProductModel?> getProduct(String id);

  Future<ProductModel> addProduct(ProductModel product, {String? imagePath});
  Future<ProductModel> updateProduct(ProductModel product, {String? imagePath});

  Future<ProductModel> deleteProduct(ProductModel product);

  Future<void> updateCache();

  Future<void> syncProduct(ProductModel productModel);

  Future<ProductModel> restoreProduct(ProductModel product);
}
