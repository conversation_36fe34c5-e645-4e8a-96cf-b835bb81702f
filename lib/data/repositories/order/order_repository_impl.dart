import 'dart:async';
import 'dart:developer';

import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/datasources/local/order/local_order_data_source.dart';
import 'package:fl_app/data/datasources/remote/order/remote_order_data_source.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import './order_repository.dart';

class OrderRepositoryImpl implements OrderRepository {
  final RemoteOrderDataSource remoteDataSource;
  final LocalOrderDataSource localDataSource;
  final UserRepository userRepository;
  final SalesRoutesRepository salesRoutesRepository;
  final OfflineSyncRepositoryImpl offlineSyncRepositoryImpl;

  UserModel? user;

  OrderRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
    required this.userRepository,
    required this.salesRoutesRepository,
    required this.offlineSyncRepositoryImpl,
  });

  @override
  Future<void> initialize() async {
    user = await userRepository.getUserAuthenticated();
    await localDataSource.initialize();
    getOrders();
  }

  @override
  Future<OrderModel> addOrder(OrderModel order) async {
    try {
      user ??= await Get.find<UserService>().getUserAuthenticated();
      var usersId = await userRepository.getOtherUsersIds();

      order.toReceivedBy = [...usersId];

      final id = remoteDataSource.addOrder(order);
      order.id = id;
      localDataSource.saveOrder(order);
      offlineSyncRepositoryImpl.addOrderToSync(order);
      return order;
    } catch (e) {
      log('Erro ao adicionar pedido: $e');
      throw Exception('Erro ao adicionar pedido $e');
    }
  }

  @override
  Future<void> deleteOrder(OrderModel order) async {
    try {
      order.isDeleted = true;
      order.deletedBy = user!.name;
      order.deletedById = user!.id;
      order.deletedAt = DateTime.now();
      order.lastModified = DateTime.now();
      await updateOrder(order);
    } catch (e) {
      log('Erro ao excluir pedido: $e');
      throw Exception('Erro ao excluir pedido $e');
    }
  }

  @override
  Future<OrderModel> restoreOrder(OrderModel order) async {
    try {
      order.isDeleted = false;
      order.deletedBy = null;
      order.deletedById = null;
      order.deletedAt = null;
      order.lastModified = DateTime.now();
      return await updateOrder(order);
    } catch (e) {
      log('Erro ao restaurar pedido: $e');
      throw Exception('Erro ao restaurar pedido $e');
    }
  }

  @override
  Future<OrderModel> getOrder(String id) async {
    var order = await localDataSource.getCachedOrder(id);
    if (order == null) {
      throw Exception('Pedido não encontrado');
    }
    return order;
  }

  @override
  Future<List<OrderModel>> getOrders() async {
    try {
      user ??= await Get.find<UserService>().getUserAuthenticated();
      var orders = localDataSource.getCachedOrders();

      if (orders.isNotEmpty) {
        // Verificar se há pedidos a receber do Firestore
        log('Verificando pedidos a receber do Firestore');
        final updatedOrders =
            await remoteDataSource.fetchOrdersToReceive(user!.id);
        if (updatedOrders.isNotEmpty) {
          // Atualiza localmente o campo toReceivedBy dos pedidos
          log('Pedidos a receber: ${updatedOrders.length}');

          await receiveOrdersAndSave(updatedOrders);
        }
      } else {
        await updateCache();
      }
      return localDataSource.getCachedOrders();
    } catch (e, s) {
      log('Erro ao obter pedidos: $e, $s');
      throw Exception('Erro ao obter pedidos $e, $s');
    }
  }

  Future<void> receiveOrdersAndSave(List<OrderModel> orders) async {
    log('Iniciando processamento de ${orders.length} pedidos para recebimento');

    // Definir o tamanho do lote para processar
    const int batchSize = 50;
    int totalProcessed = 0;

    // Processar em lotes
    for (int i = 0; i < orders.length; i += batchSize) {
      // Calcular o final do lote atual (ou fim da lista)
      int end = (i + batchSize < orders.length) ? i + batchSize : orders.length;
      List<OrderModel> batch = orders.sublist(i, end);

      log('Processando lote ${(i ~/ batchSize) + 1}: ${batch.length} pedidos (${i + 1} a $end de ${orders.length})');

      // Lista para armazenar os pedidos processados neste lote
      List<OrderModel> processedBatch = [];

      // Processar cada pedido do lote
      for (final order in batch) {
        try {
          order.toReceivedBy = order.toReceivedBy
              .where((element) => element != user!.id)
              .toList();
          remoteDataSource.updateReceivedBy(order);
          processedBatch.add(order);
          totalProcessed++;
        } catch (e, s) {
          log('Erro ao atualizar received by pedido ${order.id} no Firestore: $e, $s');
          // Continua tentando os próximos pedidos, mesmo que um falhe
        }
      }

      // Salvar o lote processado antes de prosseguir para o próximo
      if (processedBatch.isNotEmpty) {
        await localDataSource.cacheOrders(processedBatch);
        log('Salvos ${processedBatch.length} pedidos do lote ${(i ~/ batchSize) + 1}');
      }

      // Pequena pausa para liberar a thread e reduzir pressão sobre a memória
      await Future.delayed(const Duration(milliseconds: 10));
    }

    log('Finalizado processamento: $totalProcessed pedidos processados de ${orders.length} totais');
  }

  @override
  Future<OrderModel> updateOrder(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.lastModified = DateTime.now();

    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updateOrder(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  Future<OrderModel> updateClientInfo(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updateClientInfo(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  Future<OrderModel> updatePayments(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updatePayments(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  Future<OrderModel> markAsDelivered(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.toDelivery = false;
    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updateOrder(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  Future<OrderModel> markAsJoined(OrderModel order,
      {String? orderJoinedId}) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.isPaid = false;
    order.isJoined = true;
    order.joinedAt = DateTime.now();
    order.joinedBy = user!.name;
    order.joinedById = user!.id;
    order.joinedInOrderId = orderJoinedId;

    for (var e in order.dayMarkingItems) {
      e.active = false;
    }

    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updatePayments(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    if (orderJoinedId != null) {
      updateJoinedOrder(orderJoinedId, order.getTotalPending());
    }
    return order;
  }

  Future<OrderModel> updateJoinedOrder(String id, double remaining) async {
    var order = await getOrder(id);
    order.remaining += remaining;
    return updateOrder(order);
  }

  Future<OrderModel> revertJoinedOrder(String id, double remaining) async {
    var order = await getOrder(id);
    order.remaining -= remaining;
    return updateOrder(order);
  }

  @override
  Future<OrderModel> markAsUnjoined(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    var oldOrderJoinedId = order.joinedInOrderId;

    order.isJoined = false;
    order.joinedAt = null;
    order.joinedBy = null;
    order.joinedById = null;
    order.joinedInOrderId = null;

    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updatePayments(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    if (oldOrderJoinedId != null) {
      revertJoinedOrder(oldOrderJoinedId, order.getTotalPending());
    }
    return order;
  }

  @override
  Future<OrderModel> markAsPaid(
      OrderModel order, PaymentMethod paymentMethod, DateTime date) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.isPaid = true;
    double pending = order.getTotalPending();
    if (pending > 0) {
      order.payments.add(Payment(
        date: date,
        value: pending,
        paymentMethod: paymentMethod,
        userId: user!.id,
        userName: user!.name,
      ));
    }

    order.isJoined = false;
    for (var e in order.dayMarkingItems) {
      e.active = false;
    }

    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updatePayments(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  Future<OrderModel> markAsUnpaid(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.isPaid = false;
    if (order.payments.isNotEmpty && order.payments.last.userId != null) {
      order.payments.removeLast();
    }

    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updatePayments(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  @override
  Future<OrderModel> updateDayMarking(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updateDayMarking(order);
    localDataSource.saveOrder(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  Future<OrderModel> markProductsAsDelivered(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    for (var element in order.products) {
      element.quantityToDelivery = 0;
    }

    order.lastModified = DateTime.now();
    var usersId = await userRepository.getOtherUsersIds();
    order.toReceivedBy = [...usersId];

    remoteDataSource.updateProducts(order);
    localDataSource.saveOrder(order);
    return order;
  }

  Future<OrderModel> updateOrderReceivedBy(OrderModel order) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();
    order.toReceivedBy =
        order.toReceivedBy.where((element) => element != user!.id).toList();

    localDataSource.saveOrder(order);
    remoteDataSource.updateReceivedBy(order);
    offlineSyncRepositoryImpl.addOrderToSync(order);
    return order;
  }

  @override
  Future<void> updateCache() async {
    log('Atualizando cache de pedidos (Firestore)');
    DateTime now = DateTime.now();
    DateTime sixMonthsAgo = now.subtract(const Duration(days: 180));
    DateTime fourMonthsAgo = now.subtract(const Duration(days: 120));
    int batchSize = 3000;

    var allOrders = await remoteDataSource.fetchAllOrdersForCache(
        sixMonthsAgo, fourMonthsAgo, batchSize);

    await localDataSource.clearCache();
    await localDataSource.cacheOrders(allOrders);
    log('Pedidos atualizados em cache: ${allOrders.length}');
  }

  @override
  Future<void> updateCacheByRoutes(List<String> routesIds) async {
    log('Atualizando cache de pedidos (Firestore) nas rotas: $routesIds');
    var tempOrders = await remoteDataSource.fetchOrdersByRoutes(routesIds);
    await localDataSource.cacheOrders(tempOrders);
  }

  @override
  Future<void> resetCache() async {
    await localDataSource.clearCache();
    log('Cache de pedidos resetado');
  }

  @override
  Future<void> syncOrder(OrderModel orderModel) async {
    try {
      var localOrder = await localDataSource.getCachedOrder(orderModel.id!);
      if (localOrder == null) {
        await localDataSource.saveOrder(orderModel);
      } else {
        if (localOrder.lastModified.isBefore(orderModel.lastModified)) {
          await localDataSource.saveOrder(orderModel);
        }
      }
    } catch (e) {
      log('Erro ao sincronizar pedido: $e');
      throw Exception('Erro ao sincronizar pedido $e');
    }
  }

  @override
  Future<List<OrderModel>> getIncorrectOrders(String name) async {
    var orders = await getOrders();
    return orders
        .where((order) => order.products.any(
            (product) => product.name == name && product.temComissao == true))
        .toList();
  }

  @override
  Future<void> removeComissionFromOrders(
      List<OrderModel> list, String name) async {
    for (var order in list) {
      bool atualizar = false;
      for (var product in order.products) {
        if (product.name == name && product.temComissao == true) {
          product.temComissao = false;
          atualizar = true;
        }
      }
      if (atualizar) {
        log('Atualizando pedido: ${order.id}');
        await updateOrder(order);
      }
    }
  }

  @override
  void removeOldPaidOrJoinedOrdersFromCache() async {
    final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
    var all = localDataSource.getCachedOrders();
    final oldOrders = all
        .where((order) =>
            (order.isPaid && order.lastModified.isBefore(sixMonthsAgo)) ||
            (order.isJoined && order.lastModified.isBefore(sixMonthsAgo)))
        .toList();

    await localDataSource
        .removeOrdersByIds(oldOrders.map((e) => e.id!).toList());
    log('Removidos pedidos pagos/juntados há mais de 6 meses: ${oldOrders.length}');
  }

  @override
  void removeOldDeletedOrdersFromCache() async {
    final fourMonthsAgo = DateTime.now().subtract(const Duration(days: 120));
    var all = localDataSource.getCachedOrders();
    final oldDeletedOrders = all
        .where((order) =>
            (order.isDeleted &&
                order.deletedAt != null &&
                order.deletedAt!.isBefore(fourMonthsAgo) &&
                order.lastModified.isBefore(fourMonthsAgo)) ||
            (order.isDeleted &&
                order.deletedAt == null &&
                order.lastModified.isBefore(fourMonthsAgo)))
        .toList();

    await localDataSource
        .removeOrdersByIds(oldDeletedOrders.map((e) => e.id!).toList());
    log('Pedidos deletados há mais de 4 meses removidos do cache: ${oldDeletedOrders.length}');
  }

  @override
  void removeOldJoinedOrdersFromCache() async {
    final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
    var all = localDataSource.getCachedOrders();
    final oldJoinedOrders = all
        .where((order) =>
            order.isJoined &&
            order.joinedAt != null &&
            order.joinedAt!.isBefore(sixMonthsAgo) &&
            order.lastModified.isBefore(sixMonthsAgo))
        .toList();

    await localDataSource
        .removeOrdersByIds(oldJoinedOrders.map((e) => e.id!).toList());
    log('Pedidos juntados há mais de 6 meses removidos do cache: ${oldJoinedOrders.length}');
  }

  @override
  @override
  Future<List<OrderModel>> getOrdersFromCache({
    String? routeId,
    DateTime? initialDate,
    DateTime? finalDate,
    bool? showToDelivery,
    bool? showPaidOrders,
    bool ascending = true,
    String? sellerId,
  }) async {
    // Fetch all cached orders from your local data source.
    // If your localDataSource already filters by routeId, you may not need
    // the manual routeId check below. Adjust accordingly.
    var orders = localDataSource.getCachedOrders(routeId: routeId);

    // If routeId is not null or empty, filter by routeId:
    if (routeId != null && routeId.isNotEmpty) {
      orders = orders.where((order) => order.routeId == routeId).toList();
    }

    // If initialDate is passed, include only those after it:
    if (initialDate != null) {
      orders =
          orders.where((order) => order.date.isAfter(initialDate)).toList();
    }

    // If finalDate is passed, include only those before it:
    if (finalDate != null) {
      orders = orders.where((order) => order.date.isBefore(finalDate)).toList();
    }

    // If showToDelivery is passed (true/false), filter accordingly:
    if (showToDelivery != null) {
      orders = orders
          .where((order) =>
              order.toDelivery == showToDelivery ||
              order.products.any((product) => product.quantityToDelivery > 0))
          .toList();
    }

    // If showPaidOrders is passed (true/false), filter accordingly:
    if (showPaidOrders != null) {
      orders = orders
          .where((order) =>
              (order.isPaid == showPaidOrders && order.isJoined == false))
          .toList();
    }

    // If sellerId is passed, filter by userId == sellerId:
    if (sellerId != null && sellerId.isNotEmpty) {
      orders = orders.where((order) => order.userId == sellerId).toList();
    }

    // Sort by date ascending or descending based on the ascending parameter:
    orders.sort((a, b) {
      if (ascending) {
        return a.date.compareTo(b.date);
      } else {
        return b.date.compareTo(a.date);
      }
    });

    getOrders();
    return orders;
  }

  @override
  Future<List<OrderModel>> getDeletedOrdersFromCache({String? routeId}) {
    return localDataSource.getDeletedCachedOrders(routeId: routeId);
  }

  @override
  Future<List<OrderModel>> getOrdersDelivery() async {
    var orders = await getOrdersFromCache();
    return orders
        .where((order) =>
            order.toDelivery ||
            order.products.any((product) => product.quantityToDelivery > 0))
        .toList();
  }

  bool _isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  @override
  Future<List<OrderModel>> getTodaySaleOrders(String rotaId) async {
    final cachedOrders = await getOrdersFromCache(ascending: false);
    return cachedOrders
        .where((order) =>
            _isSameDate(order.date, DateTime.now()) &&
            (order.routeId == rotaId || order.routeSaleId == rotaId))
        .toList();
  }

  @override
  Future<Map<DateTime, List<OrderModel>>> getOrdersFromRouteByDate(
      String routeId) async {
    List<OrderModel> orders = await getOrdersFromCache(routeId: routeId);
    Map<DateTime, List<OrderModel>> ordersByDate = {};
    for (var order in orders) {
      DateTime date =
          DateTime(order.date.year, order.date.month, order.date.day);
      if (ordersByDate.containsKey(date)) {
        ordersByDate[date]!.add(order);
      } else {
        ordersByDate[date] = [order];
      }
    }

    ordersByDate.forEach((key, value) {
      value.sort((a, b) => a.date.compareTo(b.date));
    });

    return ordersByDate;
  }

  @override
  Future<List<Map<String, Map<ClientModel, List<OrderModel>>>>>
      getOrdersByStreet(String routeId) async {
    final clientService = Get.find<ClientService>();
    final geoService = Get.find<GeolocationService>();

    Map<ClientModel, List<OrderModel>> clientsOrders =
        await clientService.getClientsOrders([routeId]);

    // remove pedidos de hoje, pagos e marcados para hoje
    for (var client in clientsOrders.keys) {
      clientsOrders[client]!.removeWhere((order) =>
          _isSameDate(order.date, DateTime.now()) ||
          order.isPaid ||
          order.dayMarkingItems.any((element) =>
              element.active &&
              (_isSameDate(element.createdAt, DateTime.now()) ||
                  (element.dayToVisit?.isAfter(DateTime.now()) != null &&
                      element.dayToVisit!.isAfter(DateTime.now())))));
    }

    List<String> streets = [];
    for (var client in clientsOrders.keys) {
      if (!streets.contains(client.address.trim())) {
        streets.add(client.address.trim());
      }
    }

    List<Map<String, Map<ClientModel, List<OrderModel>>>> ordersSortedByStreet =
        [];

    for (var street in streets) {
      Map<String, Map<ClientModel, List<OrderModel>>> streetOrders = {};
      Map<ClientModel, List<OrderModel>> clientsOrdersByStreet = {};
      for (var client in clientsOrders.keys) {
        if (client.address.trim() == street) {
          clientsOrdersByStreet[client] = clientsOrders[client]!;
        }
      }
      streetOrders[street] = clientsOrdersByStreet;
      ordersSortedByStreet.add(streetOrders);
    }

    // remove clientes sem pedidos
    for (var street in ordersSortedByStreet) {
      street.forEach((key, value) {
        value.removeWhere((key, value) => value.isEmpty);
      });
    }

    // remove ruas sem pedidos
    ordersSortedByStreet.removeWhere((element) => element.values.first.isEmpty);

    final location = await geoService.getCurrentLocation();

    if (location != null) {
      ordersSortedByStreet.sort((a, b) {
        ClientModel clientA = a.values.first.keys.firstWhere(
            (element) => element.latitude != null && element.longitude != null,
            orElse: () => a.values.first.keys.first);
        ClientModel clientB = b.values.first.keys.firstWhere(
            (element) => element.latitude != null && element.longitude != null,
            orElse: () => b.values.first.keys.first);

        if (clientA.latitude == null || clientA.longitude == null) {
          return 1;
        }
        if (clientB.latitude == null || clientB.longitude == null) {
          return -1;
        }

        final distanceA = geoService.calculateDistance(
            LatLng(clientA.latitude!, clientA.longitude!),
            LatLng(location.latitude, location.longitude));
        final distanceB = geoService.calculateDistance(
            LatLng(clientB.latitude!, clientB.longitude!),
            LatLng(location.latitude, location.longitude));
        return distanceA.compareTo(distanceB);
      });
    }

    return ordersSortedByStreet;
  }

  @override
  Future<Map<SaleRouteModel, List<OrderModel>>>
      getOrdersDeliveryGroupedByRoute() async {
    final ordersDelivery = await getOrdersDelivery();
    final salesRoutesRepository = Get.find<SalesRoutesRepository>();
    final routes = salesRoutesRepository.getSalesRoutesFromCache();
    Map<SaleRouteModel, List<OrderModel>> ordersByRoute = {};

    for (var route in routes) {
      final ordersRoute =
          ordersDelivery.where((order) => order.routeId == route.id).toList();
      if (ordersRoute.isNotEmpty) {
        ordersByRoute[route] = ordersRoute;
      }
    }

    return ordersByRoute;
  }

  @override
  Future<List<OrderModel>> getMarkedOrders() async {
    final orders = await getOrdersFromCache();
    return orders
        .where((order) =>
            order.dayMarkingItems.any((element) => element.active) &&
            !order.isPaid)
        .toList();
  }

  @override
  Future<List<OrderModel>> getPendingOrders() async {
    final orders = await getOrdersFromCache();
    return orders
        .where((order) =>
            (order.dayMarkingItems.any((element) => element.active) ||
                order.date.isBefore(
                    DateTime.now().subtract(const Duration(days: 30)))) &&
            !order.isPaid &&
            !order.isJoined)
        .toList();
  }

  @override
  Future<List<OrderModel>> getMarkedToTodayOrders(
      List<String> cobradorRoutes) async {
    final orders = await getOrdersFromCache();
    return orders
        .where((order) =>
            cobradorRoutes.contains(order.routeId!) &&
            order.dayMarkingItems.any((element) => element.active) &&
            order.dayMarkingItems.last.dayToVisit != null &&
            _isSameDate(order.dayMarkingItems.last.dayToVisit!, DateTime.now()))
        .toList();
  }

  @override
  Future<List<OrderModel>> getOrdersReceived(
      {required List<String> routes, required DateTime date}) async {
    bool allRoutes = routes.isEmpty;
    final now = date;
    final orders = await getOrdersFromCache();
    return orders
        .where((order) =>
            (routes.contains(order.routeId!) || allRoutes) &&
            (order.payments.any((p) => _isSameDate(p.date, now))))
        .toList();
  }

  @override
  Future<List<OrderModel>> getPendingOrdersToCobradorFixo(
      List<String> cobradorRoutes, String routeId) async {
    final geolocationService = Get.find<GeolocationService>();
    final location = await geolocationService.getCurrentLocation();
    final markedOrders = await getPendingOrders();
    DateTime now = DateTime.now();
    now = DateTime(now.year, now.month, now.day);

    var markedTodayTemp = markedOrders
        .where((order) =>
            (!order.dayMarkingItems.any((element) => element.active) &&
                order.date.isBefore(now) &&
                cobradorRoutes.contains(order.routeId!)) ||
            (cobradorRoutes.contains(order.routeId!) &&
                (((!order.dayMarkingItems.last.otherMonth &&
                        order.dayMarkingItems.last.dayToVisit != null &&
                        order.dayMarkingItems.last.createdAt.isBefore(
                            now.subtract(const Duration(days: 30))))) ||
                    (order.dayMarkingItems.last.otherMonth &&
                        order.dayMarkingItems.last.createdAt.isBefore(
                            now.subtract(const Duration(days: 30)))) ||
                    (order.dayMarkingItems.last.dayToVisit != null &&
                        (DateTimeHelper.isSameDay(
                                order.dayMarkingItems.last.dayToVisit!, now) ||
                            order.dayMarkingItems.last.dayToVisit!
                                .isBefore(now))) ||
                    order.dayMarkingItems.last.after)))
        .toList();

    if (routeId.isNotEmpty) {
      markedTodayTemp = markedTodayTemp
          .where((element) => element.routeId == routeId)
          .toList();
    }

    markedTodayTemp.sort((a, b) {
      if ((a.clientLongitude == null || a.clientLatitude == null) &&
          (b.clientLongitude == null || b.clientLatitude == null)) {
        return 0;
      } else if ((a.clientLongitude == null || a.clientLatitude == null) &&
          (b.clientLongitude != null && b.clientLatitude != null)) {
        return -1;
      } else if ((a.clientLongitude != null && a.clientLatitude != null) &&
          (b.clientLongitude == null || b.clientLatitude == null)) {
        return 1;
      }

      final distanceA = a.distanceTo(location);
      final distanceB = b.distanceTo(location);
      if (distanceA == null || distanceB == null) {
        return -1;
      }
      return distanceA.compareTo(distanceB);
    });

    return markedTodayTemp;
  }

  @override
  Future<void> updateInfoFromClientOrders(ClientModel client) async {
    final clientService = Get.find<ClientService>();
    final salesRoutesRepository = Get.find<SalesRoutesRepository>();

    var ordersClient = await clientService.getOrdersFromClient(client.id!);
    for (var order in ordersClient) {
      if (!order.isPaid &&
          !order.isJoined &&
          (order.clientName != client.name ||
              order.clientPhone != client.phoneNumber ||
              order.clientAddress != client.address ||
              order.clientNumber != client.number ||
              order.clientLocalDescription != client.localDescription ||
              order.clientLatitude != client.latitude ||
              order.clientLongitude != client.longitude ||
              order.routeId != client.routeId)) {
        order.clientName = client.name;
        order.clientPhone = client.phoneNumber;
        order.clientAddress = client.address;
        order.clientLocalDescription = client.localDescription;
        order.clientNumber = client.number;
        order.clientLatitude = client.latitude;
        order.clientLongitude = client.longitude;
        order.routeId = client.routeId;
        order.routeName =
            salesRoutesRepository.getSaleRouteName(client.routeId);
        await updateClientInfo(order);
        log('Atualizado informações do cliente do pedido ${order.id}');
      }
    }
  }

  @override
  Future<List<OrderModel>> getRouteSalesLast4Months(String routeId) async {
    final now = DateTime.now();
    // Primeiro dia do mês atual
    final thisMonthStart = DateTime(now.year, now.month, 1);

    final fourMonthsAgo = thisMonthStart.subtract(const Duration(days: 120));

    // Busca no cache de fourMonthsAgo até "hoje", mas já iremos filtrar abaixo
    List<OrderModel> orders = await getOrdersFromCache(
      routeId: routeId,
      initialDate: fourMonthsAgo,
      finalDate: now, // ou thisMonthStart se quiser parar em 31/dez
      ascending: true,
    );

    // Exclui quaisquer pedidos que sejam do mês atual ou que não tenham routeSaleId
    orders = orders.where((o) {
      if (o.routeSaleId == null || o.routeSaleId! != routeId) {
        return false;
      }
      return true;
    }).toList();

    return orders;
  }
}
