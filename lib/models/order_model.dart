import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/day_making_item.dart';
import 'package:fl_app/models/order_product_model.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

class OrderModel {
  String? id;
  DateTime date;

  String clientId;
  String clientName;
  String clientAddress;
  String clientLocalDescription;
  String? clientNumber;
  String? clientPhone;
  num? clientLatitude;
  num? clientLongitude;

  List<String> toReceivedBy;

  String? routeId;
  String? routeSaleId; // id da rota que está vendendo no momento
  String? routeName;

  List<OrderProductModel> products;

  double remaining;

  List<Payment> payments;

  bool isJoined = false;
  DateTime? joinedAt;
  String? joinedBy;
  String? joinedById;
  String? joinedInOrderId;
  bool isPaid;

  bool toDelivery;

  String userId;
  String sellerName;

  DateTime lastModified;

  bool isDeleted;
  String? deletedBy;
  String? deletedById;
  DateTime? deletedAt;

  List<DayMarkingItem> dayMarkingItems;

  OrderModel({
    this.id,
    required this.date,
    required this.clientId,
    required this.clientName,
    required this.clientAddress,
    required this.clientLocalDescription,
    this.clientNumber,
    this.clientPhone,
    this.clientLatitude,
    this.clientLongitude,
    this.routeId,
    this.routeName,
    required this.products,
    required this.remaining,
    required this.payments,
    required this.isPaid,
    this.isJoined = false,
    this.joinedAt,
    this.joinedBy,
    this.joinedById,
    this.joinedInOrderId,
    this.toDelivery = false,
    required this.userId,
    required this.lastModified,
    this.isDeleted = false,
    this.deletedBy,
    this.deletedById,
    this.deletedAt,
    required this.sellerName,
    required this.dayMarkingItems,
    required this.routeSaleId,
    this.toReceivedBy = const <String>[],
  });

  bool get isToday =>
      date.day == DateTime.now().day &&
      date.month == DateTime.now().month &&
      date.year == DateTime.now().year;

  final DateTime referenceDate = DateTime(2025, 4, 17);

  bool get isOlder => date.isBefore(referenceDate);

  DayMarkingItem? get activeDayMarkingItem {
    return dayMarkingItems.firstWhereOrNull(
      (element) => element.active,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'date': date.toIso8601String(),
      'clientId': clientId,
      'clientName': clientName,
      'clientAddress': clientAddress,
      'clientLocalDescription': clientLocalDescription,
      'clientNumber': clientNumber,
      'clientPhone': clientPhone,
      'clientLatitude': clientLatitude,
      'clientLongitude': clientLongitude,
      'routeId': routeId,
      'routeName': routeName,
      'products': products.map((product) => product.toMap()).toList(),
      'remaining': remaining,
      'payments': payments.map((x) => x.toMap()).toList(),
      'isPaid': isPaid,
      'toDelivery': toDelivery,
      'userId': userId,
      'sellerName': sellerName,
      'lastModified': lastModified.toIso8601String(),
      'isDeleted': isDeleted,
      'deletedBy': deletedBy,
      'deletedById': deletedById,
      'deletedAt': deletedAt?.toIso8601String(),
      'dayMarkingItems': dayMarkingItems.map((x) => x.toMap()).toList(),
      'toReceivedBy': toReceivedBy,
      'routeSaleId': routeSaleId,
      'isJoined': isJoined,
      'joinedAt': joinedAt?.toIso8601String(),
      'joinedBy': joinedBy,
      'joinedById': joinedById,
      'joinedInOrderId': joinedInOrderId,
    };
  }

  factory OrderModel.fromMap(Map<dynamic, dynamic> map) {
    var payments = List<Map<dynamic, dynamic>>.from(map['payments'] as List);
    var paymentsMap = <Map<String, dynamic>>[];

    for (var payment in payments) {
      var keys = payment.keys.toList();
      var values = payment.values.toList();
      var paymentMap = <String, dynamic>{};
      for (var i = 0; i < keys.length; i++) {
        paymentMap[keys[i] as String] = values[i];
      }
      paymentsMap.add(paymentMap);
    }

    return OrderModel(
        id: map['id'] != null ? map['id'] as String : null,
        date: DateTime.parse(map['date']),
        clientId: map['clientId'] as String,
        clientName: map['clientName'] as String,
        clientAddress: map['clientAddress'] as String,
        clientLocalDescription: map['clientLocalDescription'] as String,
        clientNumber: map['clientNumber'] as String?,
        clientPhone: map['clientPhone'] as String?,
        clientLatitude: map['clientLatitude'] as num?,
        clientLongitude: map['clientLongitude'] as num?,
        routeId: map['routeId'] as String?,
        routeName: map['routeName'] as String?,
        products: (map['products'] as List<dynamic>)
            .map((productMap) => OrderProductModel.fromMap(
                Map<String, dynamic>.from(productMap)))
            .toList(),
        remaining: map['remaining'] as double,
        payments: map['payments'] != null
            ? paymentsMap.map((x) => Payment.fromMap(x)).toList()
            : <Payment>[],
        isPaid: map['isPaid'] as bool,
        toDelivery: map['toDelivery'] as bool? ?? false,
        userId: map['userId'] as String,
        lastModified: DateTime.parse(map['lastModified'] as String),
        isDeleted: map['isDeleted'] as bool? ?? false,
        deletedBy: map['deletedBy'] as String?,
        deletedById: map['deletedById'] as String?,
        deletedAt: map['deletedAt'] != null
            ? DateTime.parse(map['deletedAt'] as String)
            : null,
        sellerName: map['sellerName'] as String,
        dayMarkingItems: List<DayMarkingItem>.from(
          (map['dayMarkingItems'] as List)
              .map((x) => DayMarkingItem.fromMap(x)),
        ),
        toReceivedBy: List<String>.from(map['toReceivedBy'] ?? <String>[]),
        routeSaleId: map['routeSaleId'] as String?,
        isJoined: map['isJoined'] as bool? ?? false,
        joinedAt: map['joinedAt'] != null
            ? DateTime.parse(map['joinedAt'] as String)
            : null,
        joinedBy: map['joinedBy'] as String?,
        joinedById: map['joinedById'] as String?,
        joinedInOrderId: map['joinedInOrderId'] as String?);
  }

  factory OrderModel.fromDocument(DocumentSnapshot doc) {
    return OrderModel(
      id: doc.id,
      date: (doc['date'] as Timestamp).toDate(),
      clientId: doc['clientId'] as String,
      clientName: doc['clientName'] as String,
      clientAddress: doc['clientAddress'] as String,
      clientLocalDescription: doc['clientLocalDescription'] as String,
      clientNumber: doc['clientNumber'] as String?,
      clientPhone: doc['clientPhone'] as String?,
      clientLatitude: doc['clientLatitude'] as num?,
      clientLongitude: doc['clientLongitude'] as num?,
      routeId: doc['routeId'] as String?,
      routeName: doc['routeName'] as String?,
      products: (doc['products'] as List<dynamic>)
          .map((productMap) =>
              OrderProductModel.fromMap(Map<String, dynamic>.from(productMap)))
          .toList(),
      remaining: doc['remaining'] as double,
      payments: List<Payment>.from(
          (doc['payments'] as List).map((x) => Payment.fromDocument(x))),
      isPaid: doc['isPaid'] as bool,
      toDelivery: (doc.data() as Map<String, dynamic>).containsKey('toDelivery')
          ? doc['toDelivery'] as bool
          : false,
      userId: doc['userId'] as String,
      lastModified: (doc['lastModified'] as Timestamp).toDate(),
      isDeleted:
          (doc.data() as Map<String, dynamic>)['isDeleted'] as bool? ?? false,
      deletedBy: (doc.data() as Map<String, dynamic>).containsKey('deletedBy')
          ? doc['deletedBy'] as String?
          : null,
      deletedById:
          (doc.data() as Map<String, dynamic>).containsKey('deletedById') &&
                  doc['deletedById'] != null
              ? doc['deletedById'] as String
              : null,
      deletedAt:
          (doc.data() as Map<String, dynamic>).containsKey('deletedAt') &&
                  doc['deletedAt'] != null
              ? doc['deletedAt'].toDate()
              : null,
      sellerName: doc['sellerName'] as String,
      dayMarkingItems:
          (doc.data() as Map<String, dynamic>).containsKey('dayMarkingItems')
              ? List.from((doc['dayMarkingItems'] as List)
                  .map((x) => DayMarkingItem.fromMap(x)))
              : <DayMarkingItem>[],
      toReceivedBy: List<String>.from(
          (doc.data() as Map<String, dynamic>).containsKey('toReceivedBy')
              ? (doc['toReceivedBy'] as List)
              : <String>[]),
      routeSaleId:
          (doc.data() as Map<String, dynamic>).containsKey('routeSaleId')
              ? doc['routeSaleId'] as String?
              : null,
      isJoined: (doc.data() as Map<String, dynamic>).containsKey('isJoined') &&
              doc['isJoined'] != null
          ? doc['isJoined'] as bool
          : false,
      joinedAt: (doc.data() as Map<String, dynamic>).containsKey('joinedAt') &&
              doc['joinedAt'] != null
          ? doc['joinedAt'].toDate()
          : null,
      joinedBy: (doc.data() as Map<String, dynamic>).containsKey('joinedBy') &&
              doc['joinedBy'] != null
          ? doc['joinedBy'] as String
          : null,
      joinedById:
          (doc.data() as Map<String, dynamic>).containsKey('joinedById') &&
                  doc['joinedById'] != null
              ? doc['joinedById'] as String
              : null,
      joinedInOrderId:
          (doc.data() as Map<String, dynamic>).containsKey('joinedInOrderId') &&
                  doc['joinedInOrderId'] != null
              ? doc['joinedInOrderId'] as String
              : null,
    );
  }

  factory OrderModel.fromDocumentMap(Map<String, dynamic> doc) {
    return OrderModel(
      id: doc['id'] as String,
      date: (doc['date'] as Timestamp).toDate(),
      clientId: doc['clientId'] as String,
      clientName: doc['clientName'] as String,
      clientAddress: doc['clientAddress'] as String,
      clientLocalDescription: doc['clientLocalDescription'] as String,
      clientNumber: doc['clientNumber'] as String?,
      clientPhone: doc['clientPhone'] as String?,
      clientLatitude: doc['clientLatitude'] as num?,
      clientLongitude: doc['clientLongitude'] as num?,
      routeId: doc['routeId'] as String?,
      routeName: doc['routeName'] as String?,
      products: (doc['products'] as List<dynamic>)
          .map((productMap) =>
              OrderProductModel.fromMap(Map<String, dynamic>.from(productMap)))
          .toList(),
      remaining: doc['remaining'] as double,
      payments: List<Payment>.from(
          (doc['payments'] as List).map((x) => Payment.fromDocument(x))),
      isPaid: doc['isPaid'] as bool,
      toDelivery:
          (doc).containsKey('toDelivery') ? doc['toDelivery'] as bool : false,
      userId: doc['userId'] as String,
      lastModified: (doc['lastModified'] as Timestamp).toDate(),
      isDeleted: (doc)['isDeleted'] as bool? ?? false,
      deletedBy:
          (doc).containsKey('deletedBy') ? doc['deletedBy'] as String? : null,
      deletedById:
          (doc).containsKey('deletedById') && doc['deletedById'] != null
              ? doc['deletedById'] as String
              : null,
      deletedAt: (doc).containsKey('deletedAt') && doc['deletedAt'] != null
          ? doc['deletedAt'].toDate()
          : null,
      sellerName: doc['sellerName'] as String,
      dayMarkingItems: (doc).containsKey('dayMarkingItems')
          ? List.from((doc['dayMarkingItems'] as List)
              .map((x) => DayMarkingItem.fromMap(x)))
          : <DayMarkingItem>[],
      toReceivedBy: List<String>.from((doc).containsKey('toReceivedBy')
          ? (doc['toReceivedBy'] as List)
          : <String>[]),
      routeSaleId: (doc).containsKey('routeSaleId')
          ? doc['routeSaleId'] as String?
          : null,
      isJoined: (doc).containsKey('isJoined') && doc['isJoined'] != null
          ? doc['isJoined'] as bool
          : false,
      joinedAt: (doc).containsKey('joinedAt') && doc['joinedAt'] != null
          ? doc['joinedAt'].toDate()
          : null,
      joinedBy: (doc).containsKey('joinedBy') && doc['joinedBy'] != null
          ? doc['joinedBy'] as String
          : null,
      joinedById: (doc).containsKey('joinedById') && doc['joinedById'] != null
          ? doc['joinedById'] as String
          : null,
      joinedInOrderId:
          (doc).containsKey('joinedInOrderId') && doc['joinedInOrderId'] != null
              ? doc['joinedInOrderId'] as String
              : null,
    );
  }

  factory OrderModel.fromCache(Map<dynamic, dynamic> e) {
    var order = OrderModel(
      id: '',
      date: DateTime.now(),
      clientId: '',
      clientName: '',
      clientAddress: '',
      clientLocalDescription: '',
      clientNumber: '',
      routeId: '',
      routeName: '',
      products: [],
      remaining: 0.0,
      payments: [],
      isPaid: false,
      isDeleted: false,
      deletedBy: '',
      deletedById: '',
      deletedAt: DateTime.now(),
      userId: '',
      sellerName: '',
      routeSaleId: null,
      lastModified: DateTime.now(),
      dayMarkingItems: [],
      toReceivedBy: [],
    );
    order.id = e['id'];
    order.date = DateTime.parse(e['date']);
    order.clientId = e['clientId'];
    order.clientName = e['clientName'];
    order.clientAddress = e['clientAddress'];
    order.clientLocalDescription = e['clientLocalDescription'];
    order.clientLatitude = e['clientLatitude'];
    order.clientLongitude = e['clientLongitude'];
    order.clientNumber = e['clientNumber'];
    order.clientPhone = e['clientPhone'];
    order.routeId = e['routeId'];
    order.routeName = e['routeName'];
    order.remaining = e['remaining'];
    order.isPaid = e['isPaid'];
    order.isJoined = e['isJoined'] ?? false;
    order.joinedAt = e['joinedAt'] != null && e['joinedAt'] != ''
        ? DateTime.parse(e['joinedAt'])
        : null;
    order.joinedBy = e['joinedBy'];
    order.joinedById = e['joinedById'];
    order.joinedInOrderId = e['joinedInOrderId'];
    order.isDeleted = e['isDeleted'];
    order.deletedBy = e['deletedBy'];
    order.deletedById = e['deletedById'];
    order.deletedAt = e['deletedAt'] != null && e['deletedAt'] != ''
        ? DateTime.parse(e['deletedAt'])
        : null;
    final products = <Map<String, dynamic>>[];
    for (var product in e['products']) {
      products.add({
        'productId': product['productId'],
        'productBaseName': product['productBaseName'],
        'name': product['name'],
        'price': product['price'],
        'quantity': product['quantity'],
        'quantityToDelivery': product['quantityToDelivery'] ?? 0,
        'temComissao': product['temComissao'],
        'total': product['total'],
        'customValue': product['customValue'] ?? 0,
        'deliveryNote': product['deliveryNote'] ?? '',
        'variationId': product['variationId'],
      });
    }
    order.products =
        products.map((product) => OrderProductModel.fromMap(product)).toList();
    final payments = <Payment>[];
    for (var payment in e['payments']) {
      payments.add(
        Payment(
          date: DateTime.parse(payment['date']),
          value: payment['value'],
          paymentMethod: payment['paymentMethod'] != null
              ? Payment.getPaymentMethod(payment['paymentMethod'])
              : null,
          userId: payment['userId'],
          userName: payment['userName'],
        ),
      );
    }
    order.payments = payments;
    order.userId = e['userId'];
    order.sellerName = e['sellerName'] ?? '';
    order.toDelivery = e['toDelivery'] ?? false;
    order.lastModified = DateTime.parse(e['lastModified']);

    final dayMarkingItems = <DayMarkingItem>[];
    if (e['dayMarkingItems'] != null) {
      for (var item in e['dayMarkingItems']) {
        dayMarkingItems.add(DayMarkingItem.fromMap(item));
      }
    }
    order.dayMarkingItems = dayMarkingItems;
    if (e['toReceivedBy'] != null) {
      order.toReceivedBy = List<String>.from(e['toReceivedBy']);
    }
    if (e['routeSaleId'] != null) {
      order.routeSaleId = e['routeSaleId'];
    }

    return order;
  }

  String toJson() => json.encode(toMap());

  String get fullAddress {
    String fullAddress = clientAddress;
    if (clientNumber != null) {
      fullAddress += ', $clientNumber';
    }
    fullAddress += ', $clientLocalDescription';
    return fullAddress;
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'date': date,
      'clientId': clientId,
      'clientName': clientName,
      'clientAddress': clientAddress,
      'clientLocalDescription': clientLocalDescription,
      'clientNumber': clientNumber,
      'clientPhone': clientPhone,
      'clientLatitude': clientLatitude,
      'clientLongitude': clientLongitude,
      'routeId': routeId,
      'routeName': routeName,
      'products': products.map((product) => product.toMap()).toList(),
      'remaining': remaining,
      'payments': payments.map((x) => x.toDocument()).toList(),
      'isPaid': isPaid,
      'toDelivery': toDelivery,
      'userId': userId,
      'lastModified': Timestamp.fromDate(lastModified),
      'isDeleted': isDeleted,
      'deletedBy': deletedBy,
      'deletedById': deletedById,
      'deletedAt': deletedAt,
      'sellerName': sellerName,
      'dayMarkingItems': dayMarkingItems.map((x) => x.toMap()).toList(),
      'toReceivedBy': toReceivedBy,
      'routeSaleId': routeSaleId,
      'isJoined': isJoined,
      'joinedAt': joinedAt,
      'joinedBy': joinedBy,
      'joinedById': joinedById,
      'joinedInOrderId': joinedInOrderId,
    };
  }

  Map<String, dynamic> toDocumentClientInfo() {
    return <String, dynamic>{
      'clientName': clientName,
      'clientAddress': clientAddress,
      'clientLocalDescription': clientLocalDescription,
      'clientNumber': clientNumber,
      'clientPhone': clientPhone,
      'clientLatitude': clientLatitude,
      'clientLongitude': clientLongitude,
      'toReceivedBy': toReceivedBy,
      'routeId': routeId,
      'routeName': routeName,
      'lastModified': Timestamp.fromDate(lastModified),
    };
  }

  Map<String, dynamic> toDocumentPayments() {
    return <String, dynamic>{
      'payments': payments.map((x) => x.toDocument()).toList(),
      'isPaid': isPaid,
      'isJoined': isJoined,
      'joinedAt': joinedAt,
      'joinedBy': joinedBy,
      'joinedById': joinedById,
      'joinedInOrderId': joinedInOrderId,
      'dayMarkingItems': dayMarkingItems.map((x) => x.toMap()).toList(),
      'toReceivedBy': toReceivedBy,
      'lastModified': Timestamp.fromDate(lastModified),
    };
  }

  Map<String, dynamic> toDocumentDayMarking() {
    return <String, dynamic>{
      'dayMarkingItems': dayMarkingItems.map((x) => x.toMap()).toList(),
      'toReceivedBy': toReceivedBy,
      'lastModified': Timestamp.fromDate(lastModified),
    };
  }

  Map<String, dynamic> toDocumentProducts() {
    return <String, dynamic>{
      'products': products.map((product) => product.toMap()).toList(),
      'lastModified': Timestamp.fromDate(lastModified),
      'toReceivedBy': toReceivedBy,
    };
  }

  Map<String, dynamic> toDocumentReceivedBy() {
    return <String, dynamic>{
      'toReceivedBy': toReceivedBy,
    };
  }

  factory OrderModel.fromJson(String source) =>
      OrderModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'OrderModel(id: $id, date: $date, clientId: $clientId, clientName: $clientName, clientAddress: $clientAddress, clientLocalDescription: $clientLocalDescription, clientNumber: $clientNumber, routeId: $routeId, routeName: $routeName, products: $products, total: ${calculateTotal()}, remaining: $remaining, payments: $payments, isPaid: $isPaid, userId: $userId, lastModified: $lastModified, isDeleted: $isDeleted, sellerName: $sellerName, dayMarkingItems: $dayMarkingItems, toReceivedBy: $toReceivedBy)';
  }

  double? distanceTo(Position? location) {
    if (location == null) {
      return null;
    }
    if (clientLatitude == null || clientLongitude == null) {
      return null;
    }
    return Geolocator.distanceBetween(
      location.latitude,
      location.longitude,
      clientLatitude!.toDouble(),
      clientLongitude!.toDouble(),
    );
  }

  Future<double?> distanceToHere() async {
    if (clientLatitude == null || clientLongitude == null) {
      return null;
    }
    var location = await Get.find<GeolocationService>().getCurrentLocation();
    if (location == null) {
      return null;
    }
    return Geolocator.distanceBetween(
      location.latitude,
      location.longitude,
      clientLatitude!.toDouble(),
      clientLongitude!.toDouble(),
    );
  }

  bool get isMarked => dayMarkingItems.any((element) => element.active);

  double getTotalPending() {
    return calculateTotal() -
        payments.fold(
            0.0, (previousValue, payment) => previousValue + payment.value);
  }

  double calculateTotal() {
    return products.fold(
            0.0, (previousValue, product) => previousValue + product.total) +
        remaining;
  }
}

enum PaymentMethod {
  dinheiro,
  cartao,
  pix,
}

class Payment {
  DateTime date;
  double value;
  PaymentMethod? paymentMethod;
  String? userId;
  String? userName;

  Payment({
    required this.date,
    required this.value,
    required this.paymentMethod,
    required this.userId,
    required this.userName,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'date': date.toIso8601String(),
      'value': value,
      'paymentMethod': paymentMethod?.toString().split('.').last,
      'userId': userId,
      'userName': userName,
    };
  }

  static PaymentMethod? getPaymentMethod(String value) {
    if (value == 'dinheiro') {
      return PaymentMethod.dinheiro;
    } else if (value == 'cartao') {
      return PaymentMethod.cartao;
    } else if (value == 'pix') {
      return PaymentMethod.pix;
    }
    return null;
  }

  factory Payment.fromMap(Map<dynamic, dynamic> map) {
    return Payment(
      date: DateTime.parse(map['date']),
      value: map['value'] as double,
      paymentMethod:
          map.containsKey('paymentMethod') && map['paymentMethod'] != null
              ? getPaymentMethod(map['paymentMethod'] as String)
              : null,
      userId: map.containsKey('userId') &&
              map['userId'] != null &&
              map['userId'] != ''
          ? map['userId'] as String
          : null,
      userName: map.containsKey('userName') &&
              map['userName'] != null &&
              map['userName'] != ''
          ? map['userName'] as String
          : null,
    );
  }

  factory Payment.fromDocument(Map<String, dynamic> data) {
    return Payment(
      date: data['date'] is String
          ? DateTime.parse(data['date'] as String)
          : (data['date'] as Timestamp).toDate(),
      value: data['value'] as double,
      paymentMethod: data.containsKey('paymentMethod') &&
              data['paymentMethod'] != null &&
              data['paymentMethod'] != ''
          ? getPaymentMethod(data['paymentMethod'] as String)
          : null,
      userId: data.containsKey('userId') &&
              data['userId'] != null &&
              data['userId'] != ''
          ? data['userId'] as String
          : null,
      userName: data.containsKey('userName') &&
              data['userName'] != null &&
              data['userName'] != ''
          ? data['userName'] as String
          : null,
    );
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'date': date,
      'value': value,
      'paymentMethod': paymentMethod.toString().split('.').last,
      'userId': userId,
      'userName': userName,
    };
  }

  @override
  String toString() {
    return 'Payment(date: $date, value: $value, paymentMethod: $paymentMethod, userId: $userId, userName: $userName)';
  }
}
