class OrderProductModel {
  String productId;
  String? productBaseName;
  String name;
  double price;
  int quantity;
  double customValue;
  int quantityToDelivery;
  double total;
  bool temComissao;
  String deliveryNote;
  String? variationId;

  OrderProductModel({
    required this.productId,
    required this.productBaseName,
    required this.name,
    required this.price,
    required this.quantity,
    this.customValue = 0.0,
    this.quantityToDelivery = 0,
    required this.total,
    this.temComissao = false,
    this.deliveryNote = '',
    this.variationId,
  });

  factory OrderProductModel.fromMap(Map<String, dynamic> map) {
    return OrderProductModel(
      productId: map['productId'],
      productBaseName: map['productBaseName'],
      name: map['name'],
      price: (map['price'] as num).toDouble(),
      quantity: map['quantity'],
      customValue: (map['customValue'] ?? 0.0).toDouble(),
      quantityToDelivery: map['quantityToDelivery'] ?? 0,
      total: (map['total'] as num).toDouble(),
      temComissao: map['temComissao'] ?? false,
      deliveryNote: map['deliveryNote'] ?? '',
      variationId: map['variationId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productBaseName': productBaseName,
      'name': name,
      'price': price,
      'quantity': quantity,
      'customValue': customValue,
      'quantityToDelivery': quantityToDelivery,
      'total': total,
      'temComissao': temComissao,
      'deliveryNote': deliveryNote,
      'variationId': variationId
    };
  }
}
