import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/day_making_item.dart';

class OrderBillingModel {
  String? id;
  String orderId;
  Timestamp lastModified;
  List<DayMarkingItem> dayMarkingItems;

  OrderBillingModel({
    this.id,
    required this.orderId,
    required this.lastModified,
    required this.dayMarkingItems,
  });

  OrderBillingModel copyWith({
    String? id,
    String? orderId,
    List<DayMarkingItem>? dayMarkingItems,
  }) {
    return OrderBillingModel(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      lastModified: lastModified,
      dayMarkingItems: dayMarkingItems ?? this.dayMarkingItems,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'orderId': orderId,
      'lastModified': lastModified.toDate().toIso8601String(),
      'dayMarkingItems': dayMarkingItems.map((x) => x.toMap()).toList(),
    };
  }

  Map<String, dynamic> toDocument() {
    return {
      'orderId': orderId,
      'lastModified': lastModified,
      'dayMarkingItems': dayMarkingItems.map((x) => x.toMap()).toList(),
    };
  }

  factory OrderBillingModel.fromMap(Map<String, dynamic> map) {
    return OrderBillingModel(
      id: map['id'],
      orderId: map['orderId'],
      lastModified: map['lastModified'] as Timestamp,
      dayMarkingItems: List<DayMarkingItem>.from(
          map['dayMarkingItems']?.map((x) => DayMarkingItem.fromMap(x))),
    );
  }

  factory OrderBillingModel.fromDocument(DocumentSnapshot doc) {
    return OrderBillingModel(
      id: doc.id,
      orderId: doc['orderId'],
      lastModified: doc['lastModified'] as Timestamp,
      dayMarkingItems: List<DayMarkingItem>.from(
          (doc['dayMarkingItems'] as List)
              .map((x) => DayMarkingItem.fromMap(x))),
    );
  }

  String toJson() => json.encode(toMap());

  factory OrderBillingModel.fromJson(String source) =>
      OrderBillingModel.fromMap(json.decode(source));

  @override
  String toString() =>
      'OrderBillingModel(id: $id, orderId: $orderId, dayMarkingItems: $dayMarkingItems, lastModified: $lastModified)';
}
