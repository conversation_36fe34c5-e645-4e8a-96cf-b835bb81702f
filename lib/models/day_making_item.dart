import 'dart:convert';

class DayMarkingItem {
  DateTime? dayToVisit;
  bool after;
  bool otherMonth;
  bool vaiFazerPix;
  bool vaiLigar;
  DateTime createdAt;
  String createdBy;
  String observation;
  bool active;

  DayMarkingItem({
    this.dayToVisit,
    required this.after,
    required this.otherMonth,
    required this.createdAt,
    required this.observation,
    required this.active,
    required this.vaiFazerPix,
    required this.vaiLigar,
    required this.createdBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'dayToVisit': dayToVisit?.toIso8601String(),
      'after': after,
      'otherMonth': otherMonth,
      'createdAt': createdAt.toIso8601String(),
      'observation': observation,
      'active': active,
      'vaiFazerPix': vaiFazerPix,
      'vaiLigar': vaiLigar,
      'createdBy': createdBy,
    };
  }

  factory DayMarkingItem.fromMap(Map<dynamic, dynamic> map) {
    return DayMarkingItem(
      dayToVisit:
          map['dayToVisit'] != null ? DateTime.parse(map['dayToVisit']) : null,
      after: map['after'],
      otherMonth: map['otherMonth'],
      createdAt: DateTime.parse(map['createdAt']),
      observation: map['observation'],
      active: map['active'],
      vaiFazerPix: map['vaiFazerPix'] ?? false,
      vaiLigar: map['vaiLigar'] ?? false,
      createdBy: map['createdBy'] ?? '',
    );
  }

  String toJson() => json.encode(toMap());

  factory DayMarkingItem.fromJson(String source) =>
      DayMarkingItem.fromMap(json.decode(source));
}
