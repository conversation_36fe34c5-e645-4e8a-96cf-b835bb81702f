// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

class SaleRouteModel {
  String? id;
  String name;
  int salesDay;
  Timestamp lastModified;
  bool isDeleted;

  SaleRouteModel({
    this.id,
    required this.name,
    required this.salesDay,
    required this.lastModified,
    this.isDeleted = false,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'salesDay': salesDay,
      'lastModified': lastModified.toDate().toIso8601String(),
      'isDeleted': isDeleted,
    };
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'name': name,
      'salesDay': salesDay,
      'lastModified': lastModified,
      'isDeleted': isDeleted,
    };
  }

  factory SaleRouteModel.fromMap(Map<String, dynamic> map) {
    return SaleRouteModel(
      id: map['id'] as String?,
      name: map['name'] as String,
      salesDay: map['salesDay'] as int,
      lastModified: map['lastModified'] as Timestamp,
      isDeleted: map['isDeleted'] as bool? ?? false,
    );
  }

  String toJson() => json.encode(toMap());

  factory SaleRouteModel.fromJson(String source) =>
      SaleRouteModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'SaleRouteModel(id: $id, name: $name, salesDay: $salesDay, lastModified: $lastModified, isDeleted: $isDeleted)';
  }
}
