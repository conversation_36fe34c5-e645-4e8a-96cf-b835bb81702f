import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

class SalaryAdvance {
  String? id;
  String userId;
  double amount;
  DateTime date;
  String description;

  SalaryAdvance({
    this.id,
    required this.userId,
    required this.amount,
    required this.date,
    required this.description,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'userId': userId,
      'amount': amount,
      'date': date,
      'description': description,
    };
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'userId': userId,
      'amount': amount,
      'date': date,
      'description': description,
    };
  }

  String toJson() => json.encode(toMap());

  factory SalaryAdvance.fromMap(Map<dynamic, dynamic> map) {
    return SalaryAdvance(
      id: map['id'] != null ? map['id'] as String : null,
      userId: map['userId'] as String,
      amount: map['amount'] as double,
      date: map['date'] as DateTime,
      description: map['description'] as String,
    );
  }

  //fromJson factory constructor
  factory SalaryAdvance.fromJson(String source) =>
      SalaryAdvance.fromMap(json.decode(source) as Map<String, dynamic>);

  //toString method
  @override
  String toString() {
    return 'SalaryAdvance(id: $id, userId: $userId, amount: $amount, date: $date, description: $description)';
  }

  factory SalaryAdvance.fromDocument(Map<String, dynamic> data) {
    return SalaryAdvance(
      id: data['id'] as String,
      userId: data['userId'] as String,
      amount: data['amount'] as double,
      date: (data['date'] as Timestamp).toDate(),
      description: data['description'] as String,
    );
  }
}
