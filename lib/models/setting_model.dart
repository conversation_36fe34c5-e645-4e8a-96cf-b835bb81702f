// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

class SettingModel {
  String? id;
  String name;
  String value;

  Timestamp lastModified;

  SettingModel({
    this.id,
    required this.name,
    required this.value,
    required this.lastModified,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'value': value,
      'lastModified': lastModified.toDate().toIso8601String(),
    };
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'name': name,
      'value': value,
      'lastModified': lastModified,
    };
  }

  factory SettingModel.fromMap(Map<String, dynamic> map) {
    return SettingModel(
      id: map['id'] as String?,
      name: map['name'] as String,
      value: map['value'] as String,
      lastModified: map['lastModified'] as Timestamp,
    );
  }

  String toJson() => json.encode(toMap());

  factory SettingModel.fromJson(String source) =>
      SettingModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'SettingModel(id: $id, name: $name, value: $value, lastModified: $lastModified)';
  }
}
