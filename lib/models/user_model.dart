// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  String id;
  String name;
  String email;
  String? image;
  bool admin;
  bool cobrador;
  bool cobradorTemporario;
  bool cobradorFixo;
  bool authorized;
  bool userTest;
  List<String> authorizedBillingRoutes;
  String phoneModel;

  Timestamp lastModified;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.image,
    this.admin = false,
    this.cobrador = false,
    this.cobradorTemporario = false,
    this.cobradorFixo = false,
    this.authorized = false,
    this.userTest = false,
    this.authorizedBillingRoutes = const [],
    required this.lastModified,
    this.phoneModel = '',
  });

  DocumentReference get firestoreRef =>
      FirebaseFirestore.instance.doc('users/$id');

  Future<bool> getAdmin() async {
    var data = (await firestoreRef.get()).data();
    if (data == null) {
      return false;
    }
    admin = (data as Map<String, dynamic>)['admin'] as bool;
    return admin;
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'email': email,
      'image': image,
      'admin': admin,
      'cobrador': cobrador,
      'cobradorTemporario': cobradorTemporario,
      'cobradorFixo': cobradorFixo,
      'userTest': userTest,
      'authorized': authorized,
      'lastModified': lastModified.toDate().toIso8601String(),
      'authorizedBillingRoutes': authorizedBillingRoutes,
      'phoneModel': phoneModel,
    };
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'email': email,
      'image': image,
      'admin': admin,
      'cobrador': cobrador,
      'cobradorTemporario': cobradorTemporario,
      'cobradorFixo': cobradorFixo,
      'userTest': userTest,
      'authorized': authorized,
      'lastModified': lastModified,
      'authorizedBillingRoutes': authorizedBillingRoutes,
      'phoneModel': phoneModel,
    };
  }

  factory UserModel.fromMap(Map<dynamic, dynamic> map) {
    return UserModel(
      id: map['id'] as String,
      name: map['name'] as String,
      email: map['email'] as String,
      image: map['image'] != null ? map['image'] as String : null,
      admin: map['admin'] as bool,
      lastModified: map['lastModified'] as Timestamp,
      cobrador: map['cobrador'] != null ? map['cobrador'] as bool : false,
      cobradorTemporario: map['cobradorTemporario'] != null
          ? map['cobradorTemporario'] as bool
          : false,
      cobradorFixo:
          map['cobradorFixo'] != null ? map['cobradorFixo'] as bool : false,
      userTest: map['userTest'] != null ? map['userTest'] as bool : false,
      authorized: map['authorized'] != null ? map['authorized'] as bool : false,
      authorizedBillingRoutes: map['authorizedBillingRoutes'] != null
          ? List.from(map['authorizedBillingRoutes'])
          : <String>[],
      phoneModel: map['phoneModel'] != null ? map['phoneModel'] as String : '',
    );
  }

  String toJson() => json.encode(toMap());

  factory UserModel.fromJson(String source) =>
      UserModel.fromMap(json.decode(source) as Map<String, dynamic>);
}
