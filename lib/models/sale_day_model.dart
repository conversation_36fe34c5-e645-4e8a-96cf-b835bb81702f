class SaleDayModel {
  DateTime date;
  String routeId;
  String routeName;
  double total;
  int productsTotal;
  int avistaTotal;
  String observation;

  SaleDayModel({
    required this.date,
    required this.routeId,
    required this.routeName,
    required this.total,
    required this.productsTotal,
    required this.avistaTotal,
    this.observation = '',
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'date': date.toIso8601String(),
      'routeId': routeId,
      'routeName': routeName,
      'total': total,
      'productsTotal': productsTotal,
      'avistaTotal': avistaTotal,
      'observation': observation,
    };
  }

  factory SaleDayModel.fromMap(Map<dynamic, dynamic> map) {
    return SaleDayModel(
      date: DateTime.parse(map['date'] as String),
      routeId: map['routeId'] as String,
      routeName: map['routeName'] as String,
      total: map['total'] as double,
      productsTotal: map['productsTotal'] as int,
      avistaTotal: map['avistaTotal'] as int,
      observation: map['observation'] as String? ?? '',
    );
  }

  @override
  String toString() {
    return 'SaleDayModel(date: $date, routeId: $routeId, routeName: $routeName, total: $total, productsTotal: $productsTotal, avistaTotal: $avistaTotal)';
  }
}
