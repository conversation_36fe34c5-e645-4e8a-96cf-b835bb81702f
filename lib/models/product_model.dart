// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';

class ProductModel {
  String? id;
  String nome;
  String? imagem;

  num valor;
  bool temComissao;
  DateTime lastModified;
  bool isDeleted;
  String? deletedById; // ID do usuário que deletou
  String? deletedByName; // Nome do usuário que deletou
  bool isFavorite;

  List<String> toReceivedBy;

  List<VariationModel> variations;

  ProductModel({
    this.id,
    required this.nome,
    this.imagem,
    required this.temComissao,
    required this.lastModified,
    this.isDeleted = false,
    required this.valor,
    this.isFavorite = false,
    this.deletedById,
    this.deletedByName,
    this.toReceivedBy = const [],
    this.variations = const [],
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'nome': nome,
      'imagem': imagem,
      'valor': valor,
      'temComissao': temComissao,
      'lastModified': lastModified.toIso8601String(),
      'isDeleted': isDeleted,
      'deletedById': deletedById,
      'deletedByName': deletedByName,
      'isFavorite': isFavorite,
      'toReceivedBy': toReceivedBy,
      'variations': variations.map((v) => v.toMap()).toList(),
    };
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'nome': nome,
      'imagem': imagem,
      'valor': valor,
      'temComissao': temComissao,
      'lastModified': Timestamp.fromDate(lastModified),
      'isDeleted': isDeleted,
      'deletedById': deletedById,
      'deletedByName': deletedByName,
      'isFavorite': isFavorite,
      'toReceivedBy': toReceivedBy,
      'variations': variations.map((v) => v.toMap()).toList(),
    };
  }

  factory ProductModel.fromMap(Map<dynamic, dynamic> map) {
    return ProductModel(
      id: map['id'] != null ? map['id'] as String : null,
      nome: map['nome'] as String,
      imagem: map['imagem'] as String?,
      valor: map['valor'] as num,
      temComissao: map['temComissao'] as bool,
      lastModified: DateTime.parse(map['lastModified'] as String),
      isDeleted: map['isDeleted'] as bool? ?? false,
      deletedById:
          map.containsKey('deletedById') ? map['deletedById'] as String? : null,
      deletedByName: map.containsKey('deletedByName')
          ? map['deletedByName'] as String?
          : null,
      isFavorite: map['isFavorite'] as bool? ?? false,
      toReceivedBy: List<String>.from(map['toReceivedBy'] ?? <String>[]),
      variations: map.containsKey('variations') && map['variations'] != null
          ? List<VariationModel>.from(
              map['variations'].map((x) => VariationModel.fromMap(x)))
          : <VariationModel>[],
    );
  }

  factory ProductModel.fromDocument(Map<dynamic, dynamic> map) {
    return ProductModel(
      id: map['id'] != null ? map['id'] as String : null,
      nome: map['nome'] as String,
      imagem: map['imagem'] as String?,
      valor: map['valor'] as num,
      temComissao: map['temComissao'] as bool,
      lastModified: (map['lastModified'] as Timestamp).toDate(),
      isDeleted: map['isDeleted'] as bool? ?? false,
      deletedById:
          map.containsKey('deletedById') ? map['deletedById'] as String? : null,
      deletedByName: map.containsKey('deletedByName')
          ? map['deletedByName'] as String?
          : null,
      isFavorite: map['isFavorite'] as bool? ?? false,
      toReceivedBy: List<String>.from(map['toReceivedBy'] ?? <String>[]),
      variations: map.containsKey('variations') && map['variations'] != null
          ? List<VariationModel>.from(
              map['variations'].map((x) => VariationModel.fromMap(x)))
          : <VariationModel>[],
    );
  }

  ProductModel copyWith({
    String? id,
    String? nome,
    String? imagem,
    num? valor,
    bool? temComissao,
    DateTime? lastModified,
    bool? isDeleted,
    String? deletedById,
    String? deletedByName,
    bool? isFavorite,
    List<String>? toReceivedBy,
    List<VariationModel>? variations,
  }) {
    return ProductModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      imagem: imagem ?? this.imagem,
      valor: valor ?? this.valor,
      temComissao: temComissao ?? this.temComissao,
      lastModified: lastModified ?? this.lastModified,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedById: deletedById ?? this.deletedById,
      deletedByName: deletedByName ?? this.deletedByName,
      isFavorite: isFavorite ?? this.isFavorite,
      toReceivedBy: toReceivedBy ?? this.toReceivedBy,
      variations: variations ?? this.variations,
    );
  }

  String toJson() => json.encode(toMap());

  factory ProductModel.fromJson(String source) =>
      ProductModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

class VariationModel {
  String id;
  String name;
  num price;
  String? imageUrl;
  String? localPath;
  ProductModel? product;

  VariationModel({
    required this.id,
    required this.name,
    required this.price,
    this.imageUrl,
    this.localPath,
    this.product,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'imageUrl': imageUrl,
      'localPath': localPath,
    };
  }

  factory VariationModel.fromMap(Map<dynamic, dynamic> map) {
    return VariationModel(
      id: map['id'] as String,
      name: map['name'] as String,
      price: map['price'] as num,
      imageUrl: map['imageUrl'] as String?,
      localPath: map['localPath'] as String?,
    );
  }

  //copy with
  VariationModel copyWith({
    String? id,
    String? name,
    num? price,
    String? imageUrl,
    String? localPath,
    product,
  }) {
    return VariationModel(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      localPath: localPath ?? this.localPath,
      product: product ?? this.product,
    );
  }
}
