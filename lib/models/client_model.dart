// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';

class ClientModel {
  String? id;

  String name;
  String address;
  String localDescription;
  String? number;
  double? latitude;
  double? longitude;
  String? phoneNumber;
  Timestamp lastModified;
  String routeId;
  bool isDeleted;

  List<String> toReceivedBy;

  ClientModel({
    this.id,
    required this.name,
    required this.address,
    required this.localDescription,
    this.number,
    this.latitude,
    this.longitude,
    this.phoneNumber,
    required this.lastModified,
    required this.routeId,
    this.isDeleted = false,
    this.toReceivedBy = const <String>[],
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'address': address,
      'localDescription': localDescription,
      'number': number,
      'latitude': latitude,
      'longitude': longitude,
      'phoneNumber': phoneNumber,
      'lastModified': lastModified.toDate().toIso8601String(),
      'routeId': routeId,
      'isDeleted': isDeleted,
      'toReceivedBy': toReceivedBy,
    };
  }

  Map<String, dynamic> toDocument() {
    return <String, dynamic>{
      'name': name,
      'address': address,
      'localDescription': localDescription,
      'number': number,
      'latitude': latitude,
      'longitude': longitude,
      'phoneNumber': phoneNumber, // New field
      'lastModified': lastModified,
      'routeId': routeId,
      'isDeleted': isDeleted,
      'toReceivedBy': toReceivedBy,
    };
  }

  factory ClientModel.fromMap(Map<dynamic, dynamic> map) {
    return ClientModel(
      id: map['id'] != null ? map['id'] as String : null,
      name: map['name'] as String,
      address: map['address'] as String,
      localDescription: map['localDescription'] as String,
      number: map['number'] as String?,
      latitude: map['latitude'] != null ? map['latitude'] as double : null,
      longitude: map['longitude'] != null ? map['longitude'] as double : null,
      phoneNumber: map['phoneNumber'] as String?, // New field
      lastModified:
          Timestamp.fromDate(DateTime.parse(map['lastModified'] as String)),
      routeId: map['routeId'] as String,
      isDeleted: map['isDeleted'] as bool? ?? false,
      toReceivedBy: List<String>.from(map['toReceivedBy'] ?? <String>[]),
    );
  }

  factory ClientModel.fromCache(Map<dynamic, dynamic> e) {
    var client = ClientModel(
      id: '',
      name: '',
      address: '',
      latitude: 0,
      longitude: 0,
      number: '',
      localDescription: '',
      lastModified: Timestamp.now(),
      routeId: '',
      toReceivedBy: [],
    );
    client.id = e['id'];
    client.name = e['name'];
    client.address = e['address'];
    client.number = e['number'];
    client.latitude = e['latitude'];
    client.longitude = e['longitude'];
    client.routeId = e['routeId'];
    client.isDeleted = e['isDeleted'];
    client.localDescription = e['localDescription'];
    client.lastModified = Timestamp.fromDate(DateTime.parse(e['lastModified']));
    if (e['toReceivedBy'] != null) {
      client.toReceivedBy = List<String>.from(e['toReceivedBy']);
    }
    return client;
  }

  factory ClientModel.fromDocument(Map<String, dynamic> map) {
    return ClientModel(
      id: map['id'] != null ? map['id'] as String : null,
      name: map['name'] as String,
      address: map['address'] as String,
      localDescription: map['localDescription'] as String,
      number: map['number'] as String?,
      latitude: map['latitude'] != null ? map['latitude'] as double : null,
      longitude: map['longitude'] != null ? map['longitude'] as double : null,
      phoneNumber: map['phoneNumber'] as String?, // New field
      lastModified: map['lastModified'] as Timestamp,
      routeId: map['routeId'] as String,
      isDeleted: map['isDeleted'] as bool? ?? false,
      toReceivedBy: List<String>.from(map['toReceivedBy'] ?? <String>[]),
    );
  }

  String toJson() => json.encode(toMap());

  factory ClientModel.fromJson(String source) =>
      ClientModel.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return '\nClientModel(id: $id, name: $name, address: $address, localDescription: $localDescription, number: $number, latitude: $latitude, longitude: $longitude, phoneNumber: $phoneNumber, lastModified: $lastModified, routeId: $routeId)';
  }

  String get fullAddress {
    String fullAddress = address;
    if (number != null) {
      fullAddress += ', $number';
    }
    fullAddress += ', $localDescription';
    return fullAddress;
  }

  double? distanceTo(Position? location) {
    if (location == null) {
      return null;
    }
    if (latitude == null || longitude == null) {
      return null;
    }
    return Geolocator.distanceBetween(
      location.latitude,
      location.longitude,
      latitude!,
      longitude!,
    );
  }
}
