// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/product_model.dart';

class ShoppingCart {
  String id = UniqueKey().toString();
  var items = <CartItem>[].obs;
  Rxn<ClientModel> client = Rxn<ClientModel>();
  var restante = 0.0.obs;
  var toDelivery = false.obs;

  ShoppingCart();

  void addItem(CartItem item) {
    items.add(item);
  }

  void removeItem(CartItem item) {
    items.remove(item);
  }

  void clear() {
    items.clear();
  }

  double getTotalCart() {
    double total = 0;
    for (var item in items) {
      if (item.customValue > 0) {
        total += item.customValue * item.quantity;
      } else {
        if (item.selectedVariation != null) {
          total += item.selectedVariation!.price * item.quantity;
        } else {
          total += item.product.valor * item.quantity;
        }
      }
    }
    return total;
  }

  void incrementProduct(ProductModel product) {
    final index = items.indexWhere(
      (item) => item.product.id == product.id && item.selectedVariation == null,
    );
    if (index >= 0) {
      items[index] = items[index].copyWith(quantity: items[index].quantity + 1);
    } else {
      items.add(CartItem(product: product, quantity: 1));
    }
  }

  void decrementProduct(ProductModel product) {
    final index = items.indexWhere(
      (item) => item.product.id == product.id,
    );
    if (index >= 0) {
      if (items[index].quantity > 1) {
        items[index] =
            items[index].copyWith(quantity: items[index].quantity - 1);
      }
    }
  }

  void removeProduct(ProductModel product) {
    final index = items.indexWhere(
      (item) => item.product.id == product.id && item.selectedVariation == null,
    );
    if (index >= 0) {
      items.removeAt(index);
    }
  }

  static Future<ShoppingCart> fromOrder(OrderModel orderModel,
      ClientService clientService, ProductRepository productRepository) async {
    ShoppingCart cart = ShoppingCart();
    cart.toDelivery(orderModel.toDelivery);
    cart.client(await clientService.getClient(orderModel.clientId));
    cart.restante(orderModel.remaining);
    for (var product in orderModel.products) {
      var tempProduct = await productRepository.getProduct(product.productId);
      if (tempProduct == null) {
        continue;
      }
      VariationModel? selectedVariation;
      if (product.variationId != null) {
        selectedVariation = tempProduct.variations.firstWhereOrNull(
          (element) => element.id == product.variationId,
        );
        if (selectedVariation != null) {
          selectedVariation.product = tempProduct;
        }
      }
      var price = selectedVariation?.price ?? tempProduct.valor;
      bool isDifferentPrice = false;
      if (selectedVariation != null &&
          selectedVariation.price != product.price) {
        isDifferentPrice = true;
      } else {
        isDifferentPrice = price != product.price;
      }

      cart.addItem(
        CartItem(
          product: tempProduct,
          quantity: product.quantity,
          customValue: product.customValue != 0
              ? product.customValue
              : isDifferentPrice
                  ? product.price
                  : 0,
          quantityToDelivery: product.quantityToDelivery,
          deliveryNote: product.deliveryNote,
          selectedVariation: selectedVariation,
        ),
      );
    }
    return cart;
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'items': items.map((e) => e.toMap()).toList(),
      'client': client.value?.id,
      'restante': restante.value,
      'toDelivery': toDelivery.value,
    };
  }

  static Future<ShoppingCart> fromMap(Map<dynamic, dynamic> map) async {
    var clientId = map['client'] as String?;
    ClientModel? client;
    if (clientId != null) {
      client = await Get.find<ClientService>().getClient(clientId);
    }
    return ShoppingCart()
      ..id = map['id'] as String
      ..items = (map['items'] as List<dynamic>)
          .map((e) => CartItem.fromMap(e as Map<dynamic, dynamic>))
          .toList()
          .obs
      ..client = Rxn<ClientModel>(client)
      ..restante = (map['restante'] as double).obs
      ..toDelivery = (map['toDelivery'] as bool).obs;
  }

  void incrementVariation(VariationModel variation) {
    if (variation.product!.id! == variation.id) {
      incrementProduct(variation.product!);

      return;
    }
    final index = items.indexWhere(
      (item) => item.selectedVariation?.id == variation.id,
    );
    if (index >= 0) {
      items[index] = items[index].copyWith(quantity: items[index].quantity + 1);
    } else {
      items.add(CartItem(
        product: variation.product!,
        quantity: 1,
        selectedVariation: variation,
      ));
    }
  }

  void decrementVariation(VariationModel variation) {
    if (variation.product!.id! == variation.id) {
      decrementProduct(variation.product!);
      return;
    }
    final index = items.indexWhere(
      (item) => item.selectedVariation?.id == variation.id,
    );
    if (index >= 0) {
      if (items[index].quantity > 1) {
        items[index] =
            items[index].copyWith(quantity: items[index].quantity - 1);
      }
    }
  }

  void removeVariation(VariationModel variation) {
    if (variation.product!.id! == variation.id) {
      removeProduct(variation.product!);
      return;
    }
    final index = items.indexWhere(
      (item) => item.selectedVariation?.id == variation.id,
    );
    if (index >= 0) {
      items.removeAt(index);
    }
  }
}

class CartItem {
  ProductModel product;
  int quantity;
  num customValue;
  int quantityToDelivery;
  String deliveryNote;

  VariationModel? selectedVariation;

  CartItem({
    required this.product,
    required this.quantity,
    this.customValue = 0,
    this.quantityToDelivery = 0,
    this.deliveryNote = '',
    this.selectedVariation,
  });

  void increment() {
    quantity++;
  }

  void decrement() {
    quantity--;
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'product': product.toMap(),
      'quantity': quantity,
      'customValue': customValue,
      'quantityToDelivery': quantityToDelivery,
      'deliveryNote': deliveryNote,
      'selectedVariation': selectedVariation?.toMap(),
    };
  }

  factory CartItem.fromMap(Map<dynamic, dynamic> map) {
    Map productMap = map['product'];
    return CartItem(
      product: ProductModel.fromMap(productMap),
      quantity: map['quantity'] as int,
      customValue: map['customValue'] as num,
      quantityToDelivery: map['quantityToDelivery'] as int,
      deliveryNote: map['deliveryNote'] as String,
      selectedVariation: map['selectedVariation'] != null
          ? VariationModel.fromMap(
              map['selectedVariation'] as Map<dynamic, dynamic>)
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory CartItem.fromJson(String source) =>
      CartItem.fromMap(json.decode(source) as Map<String, dynamic>);

  CartItem copyWith({
    ProductModel? product,
    int? quantity,
    num? customValue,
    int? quantityToDelivery,
    String? deliveryNote,
    VariationModel? selectedVariation,
  }) {
    return CartItem(
      product: product ?? this.product,
      quantity: quantity ?? this.quantity,
      customValue: customValue ?? this.customValue,
      quantityToDelivery: quantityToDelivery ?? this.quantityToDelivery,
      deliveryNote: deliveryNote ?? this.deliveryNote,
      selectedVariation: selectedVariation ?? this.selectedVariation,
    );
  }

  num getPrice() {
    if (customValue > 0) {
      return customValue;
    }
    if (selectedVariation != null) {
      return selectedVariation!.price;
    }
    return product.valor;
  }

  num getOriginalPrice() {
    if (selectedVariation != null) {
      return selectedVariation!.price;
    }
    return product.valor;
  }
}
