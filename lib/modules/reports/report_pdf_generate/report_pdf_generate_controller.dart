import 'dart:io';

import 'package:fl_app/application/helpers/generate_pdf_report.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/modules/reports/reports_by_saler/reports_by_saler_controller.dart';
import 'package:fl_app/modules/reports/reports_geral/reports_geral_controller.dart';
import 'package:get/get.dart';

class ReportPdfGenerateController extends GetxController {
  //final ExportDelegate exportDelegate = ExportDelegate();

  @override
  void onInit() {
    super.onInit();
    init();
  }

  DateTime initialDate = Get.arguments['initialDate'] as DateTime;
  DateTime finalDate = Get.arguments['finalDate'] as DateTime;

  void init() async {
    generate();
  }

  void generate() async {
    ReportsGeralController reportsGeralController =
        ReportsGeralController(orderRepository: Get.find());

    ReportsGeralData reportsGeralData = await reportsGeralController
        .getReportsGeralData(initialDate, finalDate);

    ReportsBySalerController reportsBySalerController =
        ReportsBySalerController(
            orderRepository: Get.find(), userService: Get.find());

    List<ReportsBySalerData> reportsBySalerData = await reportsBySalerController
        .getReportsBySalerData(initialDate, finalDate);

    File pdf = await GeneratePdfReport.generatePdfReport(
      initialDate: initialDate,
      finalDate: finalDate,
      orderRepository: Get.find<OrderRepository>(),
      reportsGeralData: reportsGeralData,
      reportsBySalerData: reportsBySalerData,
      //exportDelegate: exportDelegate,
    );

    reportsGeralController.dispose();
    Get.back(result: pdf);
  }
}
