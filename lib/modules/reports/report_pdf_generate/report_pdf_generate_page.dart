import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './report_pdf_generate_controller.dart';

class ReportPdfGeneratePage extends GetView<ReportPdfGenerateController> {
  const ReportPdfGeneratePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: const SingleChildScrollView(
        child: Column(
          children: [
            // ExportFrame(
            //   frameId: 'test',
            //   exportDelegate: controller.exportDelegate,
            //   child: Container(
            //     width: 100,
            //     height: 100,
            //     color: Colors.red,
            //     child: const Text('Hello World',
            //         style: TextStyle(fontSize: 20, color: Colors.white)),
            //   ),
            // ),
            // ElevatedButton(
            //   onPressed: controller.generate,
            //   child: const Text("GENERATE"),
            // ),
          ],
        ),
      ),
    );
  }
}
