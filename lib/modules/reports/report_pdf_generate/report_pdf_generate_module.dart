import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/reports/report_pdf_generate/report_pdf_generate_bindings.dart';
import 'package:fl_app/modules/reports/report_pdf_generate/report_pdf_generate_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ReportPdfGenerateModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/report-pdf-generate',
      page: () => const ReportPdfGeneratePage(),
      binding: ReportPdfGenerateBindings(),
    ),
  ];
}
