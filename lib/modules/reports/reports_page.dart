import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './reports_controller.dart';

class ReportsPage extends GetView<ReportsController> {
  const ReportsPage({super.key});

  Widget reportItem(
    String title,
    IconData icon,
    void Function() onTap, {
    Color? backgroundColor,
    Color? textColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.indigo[50],
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: backgroundColor ?? Colors.transparent,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 30,
              color: textColor,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Relatorios',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Get.theme.colorScheme.onSecondary,
          ),
        ),
        backgroundColor: Get.theme.colorScheme.secondary,
        foregroundColor: Get.theme.colorScheme.onSecondary,
      ),
      body: SingleChildScrollView(
          child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          children: [
            const SizedBox(height: 8),
            Obx(
              () => Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Inicio da venda:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.indigo[50],
                                  foregroundColor: Colors.indigo,
                                  textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                onPressed: () {
                                  controller.selectInitialDate(context);
                                },
                                child: Text(
                                  DateTimeHelper.getFormattedDate(
                                      controller.initialDate.value),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Fim da venda:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.indigo[50],
                                  foregroundColor: Colors.indigo,
                                  textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                                onPressed: () {
                                  controller.selectFinalDate(context);
                                },
                                child: Text(
                                  DateTimeHelper.getFormattedDate(
                                      controller.finalDate.value),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            //button -1 month
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.decrementMonth,
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                      backgroundColor: Get.theme.colorScheme.primary,
                    ),
                    child: const Text('Mês Anterior'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.incrementMonth,
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                      backgroundColor: Get.theme.colorScheme.primary,
                    ),
                    child: const Text('Próximo Mês'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Divider(),
            reportItem(
              'Vendas Geral',
              FontAwesomeIcons.chartBar,
              controller.goToVendasGeral,
              backgroundColor: Colors.green[50],
              textColor: Colors.green[700],
            ),
            const SizedBox(height: 8),
            reportItem(
              'Vendas por Vendedor',
              FontAwesomeIcons.idBadge,
              controller.goToVendasPorVendedor,
              backgroundColor: Colors.indigo[50],
              textColor: Colors.indigo[700],
            ),
            const SizedBox(height: 8),
            reportItem(
              'GERAR PDF',
              FontAwesomeIcons.filePdf,
              controller.generatePdf,
              backgroundColor: Colors.red[50],
              textColor: Colors.red[700],
            ),
          ],
        ),
      )),
    );
  }
}
