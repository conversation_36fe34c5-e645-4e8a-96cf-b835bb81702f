import 'dart:io';

import 'package:fl_app/application/ui/loader/loader_mixin.dart';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';

class ReportsController extends GetxController with LoaderMixin {
  final SettingsService _settingsService;

  ReportsController({required SettingsService settingsService})
      : _settingsService = settingsService;

  Rx<DateTime> initialDate =
      DateTime.now().subtract(const Duration(days: 30)).obs;
  Rx<DateTime> finalDate = DateTime.now().obs;

  int get initialFinalDay => _settingsService.getInitialFinalDay();

  final loading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loaderListener(loading);
    var now = DateTime.now();
    if (now.day > initialFinalDay) {
      initialDate.value = DateTime(now.year, now.month, initialFinalDay);
      finalDate.value = DateTime(now.year, now.month + 1, initialFinalDay - 1);
    } else {
      initialDate.value = DateTime(now.year, now.month - 1, initialFinalDay);
      finalDate.value = DateTime(now.year, now.month, initialFinalDay - 1);
    }
  }

  void selectInitialDate(BuildContext context) {
    showDatePicker(
      context: context,
      initialDate: initialDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    ).then((value) {
      if (value != null) {
        initialDate.value = value;
      }
    });
  }

  void selectFinalDate(BuildContext context) {
    showDatePicker(
      context: context,
      initialDate: finalDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    ).then((value) {
      if (value != null) {
        finalDate.value = value;
      }
    });
  }

  void decrementMonth() {
    //decrementa um mês, verificando se volta o ano
    if (initialDate.value.month == 1) {
      initialDate.value = DateTime(initialDate.value.year - 1, 12,
          initialDate.value.day); //volta um ano e vai para dezembro
    } else {
      initialDate.value = DateTime(initialDate.value.year,
          initialDate.value.month - 1, initialDate.value.day); //volta um mês
    }
    if (finalDate.value.month == 1) {
      finalDate.value = DateTime(finalDate.value.year - 1, 12,
          finalDate.value.day); //volta um ano e vai para dezembro
    } else {
      finalDate.value = DateTime(finalDate.value.year,
          finalDate.value.month - 1, finalDate.value.day); //volta um mês
    }
  }

  void incrementMonth() {
    //incrementa um mês, verificando se passa o ano
    if (initialDate.value.month == 12) {
      initialDate.value = DateTime(initialDate.value.year + 1, 1,
          initialDate.value.day); //avança um ano e vai para janeiro
    } else {
      initialDate.value = DateTime(initialDate.value.year,
          initialDate.value.month + 1, initialDate.value.day); //avança um mês
    }
    if (finalDate.value.month == 12) {
      finalDate.value = DateTime(finalDate.value.year + 1, 1,
          finalDate.value.day); //avança um ano e vai para janeiro
    } else {
      finalDate.value = DateTime(finalDate.value.year,
          finalDate.value.month + 1, finalDate.value.day); //avança um mês
    }
  }

  void goToVendasPorVendedor() {
    Get.toNamed('/reports_by_saler', arguments: {
      'initialDate': initialDate.value,
      'finalDate': finalDate.value,
    });
  }

  void goToVendasGeral() {
    Get.toNamed('/reports_geral', arguments: {
      'initialDate': initialDate.value,
      'finalDate': finalDate.value,
    });
  }

  void generatePdf() async {
    loading(true);
    try {
      var pdf = await Get.toNamed('/report-pdf-generate', arguments: {
        'initialDate': initialDate.value,
        'finalDate': finalDate.value,
      });

      if (pdf != null) {
        File? newfile;
        newfile = await savePdfToDevice(pdf!);
        loading(false);
        Get.toNamed('/report-pdf-view', arguments: newfile ?? pdf);
        // await Share.shareXFiles(
        //   [
        //     XFile.fromData(newfile!.readAsBytesSync(),
        //         name: 'relatorio.pdf',
        //         path: newfile.path,
        //         mimeType: 'application/pdf')
        //   ],
        //   text: 'Relatório de vendas',
        // );
      }
      loading(false);
    } catch (e) {
      loading(false);
      Get.snackbar(
        'Erro',
        'Erro ao gerar PDF + $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<File?> savePdfToDevice(File pdfFile) async {
    try {
      final String dir = (await getExternalStorageDirectory())!.path;
      final String path =
          '$dir/report-${DateTime.now().millisecondsSinceEpoch}.pdf';
      final File file = File(path);
      await file.writeAsBytes(await pdfFile.readAsBytes());
      return file;
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
        title: 'Erro',
        message: 'Erro ao salvar PDF- $e',
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ));
    }
    return null;
  }
}
