import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/valor_recebido_tile.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './reports_geral_controller.dart';

class ReportsGeralPage extends GetView<ReportsGeralController> {
  const ReportsGeralPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('Vendas Geral'),
        backgroundColor: Colors.indigo,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Obx(
            () => Column(
              children: [
                if (!controller.byDay.value)
                  Row(
                    children: [
                      Expanded(
                        child: RichText(
                          text: TextSpan(
                            text: 'De ',
                            style: TextStyle(
                              fontSize: 16,
                              color: Get.theme.textTheme.bodyMedium!.color,
                            ),
                            children: <TextSpan>[
                              TextSpan(
                                text: DateTimeHelper.getFormattedDate(
                                    controller.initialDate!),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Get.theme.textTheme.bodyMedium!.color,
                                ),
                              ),
                              TextSpan(
                                text: ' até ',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Get.theme.textTheme.bodyMedium!.color,
                                ),
                              ),
                              TextSpan(
                                text: DateTimeHelper.getFormattedDate(
                                    controller.finalDate!),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Get.theme.textTheme.bodyMedium!.color,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () {
                          controller.setByDay();
                        },
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          foregroundColor: Get.isDarkMode
                              ? Colors.indigo[100]
                              : Colors.indigo,
                        ),
                        child: const Text('Ver por dia'),
                      ),
                    ],
                  ),
                if (controller.byDay.value)
                  Row(
                    children: [
                      ElevatedButton(
                        onPressed: () {
                          //select date
                          showDatePicker(
                            context: context,
                            initialDate: controller.selectedDay.value,
                            firstDate: controller.initialDate!,
                            lastDate: controller.finalDate!,
                          ).then((value) {
                            if (value != null) {
                              controller.setSelectedDay(value);
                            }
                          });
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.indigo,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          DateTimeHelper.getFormattedDate(
                              controller.selectedDay.value),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          controller.setByDay();
                        },
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          foregroundColor: Get.isDarkMode
                              ? Colors.indigo[100]
                              : Colors.indigo,
                        ),
                        child: const Text('Ver por período'),
                      ),
                    ],
                  ),
                const Divider(),
                Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'P.V',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${controller.pv.value}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Vendas',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${controller.quantityVendas.value}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Valor Vendido',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        //formatar valor no padrão brasileiro R$ 1.000,00
                        'R\$ ${controller.valorVendido.value.toStringAsFixed(2).replaceAll('.', ',')}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Valor Restante',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        //formatar valor no padrão brasileiro R$ 1.000,00
                        'R\$ ${controller.valorRestante.value.toStringAsFixed(2).replaceAll('.', ',')}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Valor Total',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        //formatar valor no padrão brasileiro R$ 1.000,00
                        'R\$ ${controller.valorTotal.value.toStringAsFixed(2).replaceAll('.', ',')}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  () => ValorRecebidoTile(
                    receivedValue: controller.valorRecebido.value,
                    receivedValueDinheiro:
                        controller.valorRecebidoDinheiro.value,
                    receivedValueCartao: controller.valorRecebidoCartao.value,
                    receivedValuePix: controller.valorRecebidoPix.value,
                    title: 'Recebido',
                  ),
                ),
                const SizedBox(height: 8),
                Obx(
                  () => Table(
                    columnWidths: const {
                      0: FlexColumnWidth(3),
                      1: FlexColumnWidth(1),
                      2: FlexColumnWidth(1),
                    },
                    children: [
                      TableRow(
                        children: const [
                          Text('Produtos Vendidos',
                              textAlign: TextAlign.center,
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          Text('P.V',
                              textAlign: TextAlign.center,
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          Text('A.V',
                              textAlign: TextAlign.center,
                              style: TextStyle(fontWeight: FontWeight.bold)),
                        ],
                        decoration: BoxDecoration(
                          color: Get.isDarkMode
                              ? Colors.grey[800]
                              : Colors.grey[200],
                          border: const Border.symmetric(
                            horizontal: BorderSide(color: Colors.black),
                          ),
                        ),
                      ),
                      ...controller.productsSold.map((e) => TableRow(
                            children: [
                              Text(
                                e['productName'],
                                style: TextStyle(
                                  color: e['temComissao'] == true
                                      ? Get.isDarkMode
                                          ? Colors.blue[600]
                                          : Colors.blue[800]
                                      : Get.isDarkMode
                                          ? Colors.grey[100]
                                          : Colors.black,
                                  fontWeight: e['temComissao'] == true
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                                ),
                              ),
                              Text(
                                e['quantity_prazo'].toString(),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: e['temComissao'] == true
                                      ? e['quantity_prazo'] != 0
                                          ? Get.isDarkMode
                                              ? Colors.blue[600]
                                              : Colors.blue[800]
                                          : Colors.grey[100]
                                      : e['quantity_prazo'] != 0
                                          ? Get.isDarkMode
                                              ? Colors.grey[100]
                                              : Colors.black
                                          : Colors.grey[100],
                                  fontWeight: e['temComissao'] == true
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                                ),
                              ),
                              Text(
                                e['quantity_avista'].toString(),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: e['temComissao'] == true
                                      ? e['quantity_avista'] != 0
                                          ? Get.isDarkMode
                                              ? Colors.blue[600]
                                              : Colors.blue[800]
                                          : Colors.grey[200]
                                      : e['quantity_avista'] != 0
                                          ? Get.isDarkMode
                                              ? Colors.grey[100]
                                              : Colors.black
                                          : Colors.grey[200],
                                  fontWeight: e['temComissao'] == true
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                                ),
                              ),
                            ],
                            decoration: BoxDecoration(
                              border: Border.symmetric(
                                horizontal: BorderSide(
                                    color: Get.isDarkMode
                                        ? Colors.grey[800]!
                                        : Colors.grey[200]!),
                              ),
                            ),
                          ))
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                const Text('Vendas por rota',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    )),
                Obx(() => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ...controller.salesByRoutes.map((e) => Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[500]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Rota ${e.key.name}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  // e.value = salesByRoutes.add(MapEntry(route, {
                                  //   'orders': ordersRoute,
                                  //   'quantityVendas': ordersRoute.length,
                                  //   'productsSold': productsSoldInRoute,
                                  //   'valorTotal': valorTotalNaRota,
                                  //   'valorRestante': valorRestanteNaRota,
                                  //   'valorVendido': valorVendidoNaRota,
                                  //   'productsSoldPrazo': productsSoldPrazo,
                                  //   'productsSoldAvista': productsSoldAvista,
                                  //   'sallerSales': sallerSales,
                                  // }));

                                  // Text(
                                  //     'Vendas: ${e.value['quantityVendas']}'),
                                  // Text(
                                  //     'Valor Vendido: R\$ ${e.value['valorVendido'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                  // Text(
                                  //     'Valor Restante: R\$ ${e.value['valorRestante'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                  // Text(
                                  //     'Valor Total: R\$ ${e.value['valorTotal'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                  // Row(
                                  //   children: [
                                  //     Text(
                                  //         'P.V: ${e.value['productsSoldPrazo']}'),
                                  //     Text(
                                  //         'A.V: ${e.value['productsSoldAvista']}'),
                                  //   ],
                                  // ),

                                  Table(
                                    children: [
                                      // Text(
                                      //     'Vendas: ${e.value['quantityVendas']}'),
                                      // Text(
                                      //     'Valor Vendido: R\$ ${e.value['valorVendido'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                      // Text(
                                      //     'Valor Restante: R\$ ${e.value['valorRestante'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                      // Text(
                                      //     'Valor Total: R\$ ${e.value['valorTotal'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                      // Row(
                                      //   children: [
                                      //     Text(
                                      //         'P.V: ${e.value['productsSoldPrazo']}'),
                                      //     Text(
                                      //         'A.V: ${e.value['productsSoldAvista']}'),
                                      //   ],
                                      // ),
                                      TableRow(
                                        children: [
                                          const Text('Vendas:'),
                                          Text('${e.value['quantityVendas']}'),
                                        ],
                                      ),
                                      TableRow(
                                        children: [
                                          const Text('Valor Vendido:'),
                                          Text(
                                              'R\$ ${e.value['valorVendido'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                        ],
                                      ),
                                      TableRow(
                                        children: [
                                          const Text('Valor Restante:'),
                                          Text(
                                              'R\$ ${e.value['valorRestante'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                        ],
                                      ),
                                      TableRow(
                                        children: [
                                          const Text('Valor Total:'),
                                          Text(
                                              'R\$ ${e.value['valorTotal'].toStringAsFixed(2).replaceAll('.', ',')}'),
                                        ],
                                      ),
                                      TableRow(
                                        children: [
                                          const Text('P.V:'),
                                          Text(
                                              '${e.value['productsSoldPrazo']}'),
                                        ],
                                      ),
                                      TableRow(
                                        children: [
                                          const Text('A.V:'),
                                          Text(
                                              '${e.value['productsSoldAvista']}'),
                                        ],
                                      ),
                                    ],
                                  ),

                                  const Text('Produtos Vendidos por vendedor:'),
                                  ...e.value['sallerSales'].entries
                                      .map(
                                          (e) => Text('- ${e.key}: ${e.value}'))
                                      .toList(),
                                ],
                              ),
                            ))
                      ],
                    )),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
