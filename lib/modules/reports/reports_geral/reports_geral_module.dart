import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/reports/reports_geral/reports_geral_bindings.dart';
import 'package:fl_app/modules/reports/reports_geral/reports_geral_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ReportsGeralModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/reports_geral',
      page: () => const ReportsGeralPage(),
      binding: ReportsGeralBindings(),
    ),
  ];
}
