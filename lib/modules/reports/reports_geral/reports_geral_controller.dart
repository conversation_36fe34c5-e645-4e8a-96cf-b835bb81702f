import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';

import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:get/get.dart';

class ReportsGeralController extends GetxController {
  final OrderRepository _orderRepository;

  ReportsGeralController({
    required OrderRepository orderRepository,
  }) : _orderRepository = orderRepository;

  DateTime? initialDate = Get.arguments?['initialDate'];
  DateTime? finalDate = Get.arguments?['finalDate'];

  Rx<DateTime> selectedDay = DateTime.now().obs;

  RxList<OrderModel> orders = <OrderModel>[].obs;
  RxInt pv = 0.obs;
  RxInt quantityVendas = 0.obs;
  RxDouble valorVendido = 0.0.obs;
  RxDouble valorRestante = 0.0.obs;
  RxDouble valorTotal = 0.0.obs;
  RxDouble valorRecebido = 0.0.obs;
  RxDouble valorRecebidoCartao = 0.0.obs;
  RxDouble valorRecebidoDinheiro = 0.0.obs;
  RxDouble valorRecebidoPix = 0.0.obs;

  RxList<Map<String, dynamic>> productsSold = <Map<String, dynamic>>[].obs;

  RxList<MapEntry<SaleRouteModel, Map<String, dynamic>>> routes =
      <MapEntry<SaleRouteModel, Map<String, dynamic>>>[].obs;

  RxList<MapEntry<SaleRouteModel, Map<String, dynamic>>> salesByRoutes =
      <MapEntry<SaleRouteModel, Map<String, dynamic>>>[].obs;

  RxBool byDay = false.obs;

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    refreshOrders();
  }

  Future<void> refreshOrders() async {
    var ordersTemp = await _orderRepository.getOrdersFromCache();
    if (byDay.value) {
      ordersTemp.removeWhere((element) =>
          !DateTimeHelper.isSameDay(element.date, selectedDay.value));
    } else {
      ordersTemp.removeWhere((element) =>
          element.date.isBefore(initialDate!) ||
          element.date.isAfter(finalDate!.add(const Duration(days: 1))));
    }

    orders.assignAll(ordersTemp);
    //calcular produtos vendidos
    pv.value = orders.fold<int>(
        0,
        (previousValue, element) =>
            previousValue +
            element.products.fold<int>(
                0,
                (previousValue, element) =>
                    element.temComissao && (element.customValue == 0)
                        ? previousValue + (element.quantity)
                        : previousValue));
    //calcular quantidade de vendas
    quantityVendas.value = orders.length;

    //calcular produtos vendidos
    productsSold.clear();
    for (var order in orders) {
      for (var product in order.products) {
        var isAvista = product.customValue != 0 && product.quantity != 0;
        if (productsSold.isEmpty) {
          productsSold.add({
            'productName': product.name,
            'quantity_avista': isAvista ? product.quantity : 0,
            'quantity_prazo': isAvista ? 0 : product.quantity,
            'temComissao': product.temComissao,
          });
        } else {
          bool found = false;
          for (var productSold in productsSold) {
            if (productSold['productName'] == product.name) {
              int qAvista = productSold['quantity_avista'] as int;
              int qPrazo = productSold['quantity_prazo'] as int;

              if (isAvista) {
                productSold['quantity_avista'] = qAvista + (product.quantity);
              } else {
                productSold['quantity_prazo'] = qPrazo + (product.quantity);
              }
              found = true;
              break;
            }
          }
          if (!found) {
            productsSold.add({
              'productName': product.name,
              'quantity_avista': isAvista ? product.quantity : 0,
              'quantity_prazo': isAvista ? 0 : product.quantity,
              'temComissao': product.temComissao,
            });
          }
        }
      }
    }
    //sort products by quantity and name
    productsSold.sort((a, b) {
      int qA = a['quantity_prazo'] as int;
      int qB = b['quantity_prazo'] as int;
      if (qA == qB) {
        return (a['productName'] as String).compareTo(b['productName']);
      } else {
        return qB.compareTo(qA);
      }
    });
    productsSold.refresh();

    //calcular valor total
    valorTotal.value = orders.fold<double>(0,
        (previousValue, element) => previousValue + element.calculateTotal());
    //calcular valor restante
    valorRestante.value = orders.fold<double>(
        0, (previousValue, element) => previousValue + element.remaining);
    //calcular valor vendido
    valorVendido.value = valorTotal.value - valorRestante.value;
    //calcular valor recebido
    var ordersReceivedTemp = await _orderRepository.getOrdersFromCache();
    ordersReceivedTemp = ordersReceivedTemp
        .where((element) => !element.isDeleted && element.payments.isNotEmpty)
        .toList();
    ordersReceivedTemp = ordersReceivedTemp.where((element) {
      var payments = element.payments;
      payments = payments.where((element) {
        var isBetween = element.date.isAfter(initialDate!) &&
            element.date.isBefore(finalDate!.add(const Duration(days: 1)));
        return isBetween;
      }).toList();
      return payments.isNotEmpty;
    }).toList();

    valorRecebido.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              var isBetween = element.date.isAfter(initialDate!) &&
                  element.date
                      .isBefore(finalDate!.add(const Duration(days: 1)));
              return isBetween;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    //calcular valor recebido em dinheiro
    valorRecebidoDinheiro.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              var isBetween = element.date.isAfter(initialDate!) &&
                  element.date
                      .isBefore(finalDate!.add(const Duration(days: 1)));
              return isBetween &&
                  element.paymentMethod == PaymentMethod.dinheiro;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    //calcular valor recebido em cartão
    valorRecebidoCartao.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              var isBetween = element.date.isAfter(initialDate!) &&
                  element.date
                      .isBefore(finalDate!.add(const Duration(days: 1)));
              return isBetween && element.paymentMethod == PaymentMethod.cartao;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    //calcular valor recebido em pix
    valorRecebidoPix.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              var isBetween = element.date.isAfter(initialDate!) &&
                  element.date
                      .isBefore(finalDate!.add(const Duration(days: 1)));
              return isBetween && element.paymentMethod == PaymentMethod.pix;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    var routes = await Get.find<SalesRoutesService>().getSalesRoutesFromCache();

    for (var route in routes) {
      var ordersRoute = orders.toList();
      ordersRoute.removeWhere((element) =>
          element.routeId != route.id && element.isDeleted == false);
      var valorTotal = orders.fold<double>(0,
          (previousValue, element) => previousValue + element.calculateTotal());
      var valorRestante = orders.fold<double>(
          0, (previousValue, element) => previousValue + element.remaining);
      var valorVendido = valorTotal - valorRestante;

      var valorVendidoNaRota = 0.0;
      var valorRestanteNaRota = 0.0;
      var valorTotalNaRota = 0.0;

      num productsSoldPrazo = 0;
      num productsSoldAvista = 0;

      Map<String, int> sallerSales = await loadSallerSales(ordersRoute);

      for (var order in ordersRoute) {
        for (var product in order.products) {
          if (product.temComissao == true && product.customValue == 0) {
            productsSoldPrazo += product.quantity;
          } else if (product.temComissao == true && product.customValue != 0) {
            productsSoldAvista += product.quantity;
          }
        }

        valorTotalNaRota += order.calculateTotal();
        valorRestanteNaRota += order.remaining;
        valorVendidoNaRota += order.calculateTotal() - order.remaining;
      }

      //calcular produtos vendidos na rota
      List<Map<String, dynamic>> productsSoldInRoute = <Map<String, dynamic>>[];
      for (var order in ordersRoute) {
        for (var product in order.products) {
          var isAvista = product.customValue != 0 && product.quantity != 0;
          if (productsSoldInRoute.isEmpty) {
            productsSoldInRoute.add({
              'productName': product.name,
              'quantity_avista': isAvista ? product.quantity : 0,
              'quantity_prazo': isAvista ? 0 : product.quantity,
              'temComissao': product.temComissao,
            });
          } else {
            bool found = false;
            for (var productSold in productsSoldInRoute) {
              if (productSold['productName'] == product.name) {
                int qAvista = productSold['quantity_avista'] as int;
                int qPrazo = productSold['quantity_prazo'] as int;

                if (isAvista) {
                  productSold['quantity_avista'] = qAvista + (product.quantity);
                } else {
                  productSold['quantity_prazo'] = qPrazo + (product.quantity);
                }
                found = true;
                break;
              }
            }
            if (!found) {
              productsSoldInRoute.add({
                'productName': product.name,
                'quantity_avista': isAvista ? product.quantity : 0,
                'quantity_prazo': isAvista ? 0 : product.quantity,
                'temComissao': product.temComissao,
              });
            }
          }
        }
      }
      if (ordersRoute.isNotEmpty) {
        salesByRoutes.add(MapEntry(route, {
          'orders': ordersRoute,
          'quantityVendas': ordersRoute.length,
          'productsSold': productsSoldInRoute,
          'valorTotal': valorTotalNaRota,
          'valorRestante': valorRestanteNaRota,
          'valorVendido': valorVendidoNaRota,
          'productsSoldPrazo': productsSoldPrazo,
          'productsSoldAvista': productsSoldAvista,
          'sallerSales': sallerSales,
        }));
      }
    }
  }

  Future<Map<String, int>> loadSallerSales(List<OrderModel> orders) async {
    List<String> sellerNameList = [];
    final ordersTemp = orders;
    for (var order in ordersTemp) {
      if (sellerNameList.isEmpty) {
        sellerNameList.add(order.sellerName);
      } else {
        bool found = false;
        for (var userName in sellerNameList) {
          if (userName == order.sellerName) {
            found = true;
            break;
          }
        }
        if (!found) {
          sellerNameList.add(order.sellerName);
        }
      }
    }

    Map<String, int> sellerSalesMap = {};

    for (var sellerName in sellerNameList) {
      int total = 0;
      for (var order in ordersTemp) {
        if (order.sellerName == sellerName) {
          for (var product in order.products) {
            if (product.temComissao == true && (product.customValue == 0)) {
              total += product.quantity;
            }
          }
        }
      }
      sellerSalesMap[sellerName] = total;
    }
    //sort seller sales by quantity
    sellerSalesMap = Map.fromEntries(sellerSalesMap.entries.toList()
      ..sort((e1, e2) => e2.value.compareTo(e1.value)));

    //remove sallerSales with 0 sales
    sellerSalesMap.removeWhere((key, value) => value == 0);
    return sellerSalesMap;
  }

  void setByDay() {
    byDay.value = !byDay.value;
    selectedDay.value = initialDate!;
    refreshOrders();
  }

  void setSelectedDay(DateTime value) {
    selectedDay.value = value;
    refreshOrders();
  }

  Future<ReportsGeralData> getReportsGeralData(
      DateTime initialDate, DateTime finalDate) async {
    this.initialDate = initialDate;
    this.finalDate = finalDate;
    await refreshOrders();
    return ReportsGeralData(
      orders: orders.toList(),
      pv: pv.value,
      quantityVendas: quantityVendas.value,
      valorVendido: valorVendido.value,
      valorRestante: valorRestante.value,
      valorTotal: valorTotal.value,
      productsSold: productsSold.toList(),
      salesByRoutes: salesByRoutes.toList(),
    );
  }
}

class ReportsGeralData {
  final List<OrderModel> orders;
  final int pv;
  final int quantityVendas;
  final double valorVendido;
  final double valorRestante;
  final double valorTotal;
  final List<Map<String, dynamic>> productsSold;
  final List<MapEntry<SaleRouteModel, Map<String, dynamic>>> salesByRoutes;

  ReportsGeralData({
    required this.orders,
    required this.pv,
    required this.quantityVendas,
    required this.valorVendido,
    required this.valorRestante,
    required this.valorTotal,
    required this.productsSold,
    required this.salesByRoutes,
  });
}
