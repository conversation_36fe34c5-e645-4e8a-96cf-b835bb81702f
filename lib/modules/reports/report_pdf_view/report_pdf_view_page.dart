import 'package:easy_pdf_viewer/easy_pdf_viewer.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import './report_pdf_view_controller.dart';

class ReportPdfViewPage extends GetView<ReportPdfViewController> {
  const ReportPdfViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () async {
              await Share.shareXFiles(
                [
                  XFile.fromData(controller.pdf.readAsBytesSync(),
                      name: 'relatorio.pdf',
                      path: controller.pdf.path,
                      mimeType: 'application/pdf')
                ],
                text: 'Relatório de vendas',
              );
            },
          )
        ],
      ),
      body: Obx(() {
        if (controller.doc.value == null) {
          return const Center(
              child: CircularProgressIndicator(
            strokeCap: StrokeCap.round,
          ));
        }
        return PDFViewer(document: controller.doc.value!);
      }),
    );
  }
}
