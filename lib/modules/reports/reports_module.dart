import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/reports/report_pdf_generate/report_pdf_generate_module.dart';
import 'package:fl_app/modules/reports/report_pdf_view/report_pdf_view_module.dart';
import 'package:fl_app/modules/reports/reports_bindings.dart';
import 'package:fl_app/modules/reports/reports_by_saler/reports_by_saler_module.dart';
import 'package:fl_app/modules/reports/reports_geral/reports_geral_module.dart';
import 'package:fl_app/modules/reports/reports_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ReportsModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/reports',
      page: () => const ReportsPage(),
      binding: ReportsBindings(),
    ),
    ...ReportsBySalerModule().routers,
    ...ReportsGeralModule().routers,
    ...ReportPdfViewModule().routers,
    ...ReportPdfGenerateModule().routers,
  ];
}
