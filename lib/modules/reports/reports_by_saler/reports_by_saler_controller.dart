import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

class ReportsBySalerController extends GetxController {
  final UserService _userService;
  final OrderRepository _orderRepository;

  ReportsBySalerController({
    required UserService userService,
    required OrderRepository orderRepository,
  })  : _userService = userService,
        _orderRepository = orderRepository;

  RxList<UserModel> salers = <UserModel>[].obs;
  Rxn<UserModel> selectedSaler = Rxn<UserModel>();

  Rx<DateTime> selectedDay = DateTime.now().obs;

  DateTime? initialDate = Get.arguments['initialDate'];
  DateTime? finalDate = Get.arguments['finalDate'];

  RxList<OrderModel> orders = <OrderModel>[].obs;

  RxInt pv = 0.obs;
  RxInt quantityVendas = 0.obs;
  RxList<Map<String, dynamic>> productsSold = <Map<String, dynamic>>[].obs;

  RxDouble valorRecebido = 0.0.obs;
  RxDouble valorRecebidoCartao = 0.0.obs;
  RxDouble valorRecebidoDinheiro = 0.0.obs;
  RxDouble valorRecebidoPix = 0.0.obs;

  // Listas separadas para cada tipo de pagamento
  RxList<OrderModel> ordersCartao = <OrderModel>[].obs;
  RxList<OrderModel> ordersPix = <OrderModel>[].obs;
  RxList<OrderModel> ordersDinheiro = <OrderModel>[].obs;

  RxBool byDay = false.obs;

  static const String _lastSelectedSalerKey = 'last_selected_saler_id';

  @override
  void onInit() {
    init();
    super.onInit();
  }

  Future<void> init() async {
    salers.assignAll(await _userService.getUsersFromCache());
    await _loadLastSelectedSaler();

    // Sempre iniciar no modo "por dia" com a data atual
    byDay.value = true;
    selectedDay.value = DateTime.now();

    refreshOrders();
  }

  Future<void> _loadLastSelectedSaler() async {
    try {
      final box = await Hive.openBox('app_settings');
      final lastSalerId = box.get(_lastSelectedSalerKey);

      if (lastSalerId != null && salers.isNotEmpty) {
        // Tentar encontrar o vendedor pelo ID salvo
        final savedSaler =
            salers.firstWhereOrNull((saler) => saler.id == lastSalerId);

        if (savedSaler != null) {
          selectedSaler.value = savedSaler;
          return;
        }
      }

      // Se não encontrou o vendedor salvo ou não há ID salvo, usar o primeiro
      if (salers.isNotEmpty) {
        selectedSaler.value = salers.first;
      }
    } catch (e) {
      // Em caso de erro, usar o primeiro vendedor disponível
      if (salers.isNotEmpty) {
        selectedSaler.value = salers.first;
      }
    }
  }

  Future<void> _saveLastSelectedSaler(String salerId) async {
    try {
      final box = await Hive.openBox('app_settings');
      await box.put(_lastSelectedSalerKey, salerId);
    } catch (e) {
      // Ignorar erros de salvamento
    }
  }

  Future<void> refreshOrders() async {
    var ordersTemp = await _orderRepository.getOrdersFromCache(
      sellerId: selectedSaler.value!.id,
    );
    if (byDay.value) {
      ordersTemp.removeWhere((element) =>
          !DateTimeHelper.isSameDay(element.date, selectedDay.value));
    } else {
      ordersTemp.removeWhere((element) =>
          element.date.isBefore(initialDate!) ||
          element.date.isAfter(finalDate!.add(const Duration(days: 1))));
    }
    orders.assignAll(ordersTemp);
    //calcular produtos vendidos
    pv.value = orders.fold<int>(
        0,
        (previousValue, element) =>
            previousValue +
            element.products.fold<int>(
                0,
                (previousValue, element) =>
                    element.temComissao && (element.customValue == 0)
                        ? previousValue + (element.quantity)
                        : previousValue));

    //calcular valor recebido
    var ordersReceivedTemp = await _orderRepository.getOrdersFromCache();
    ordersReceivedTemp = ordersReceivedTemp
        .where((element) => !element.isDeleted && element.payments.isNotEmpty)
        .toList();
    ordersReceivedTemp = ordersReceivedTemp.where((element) {
      var payments = element.payments;
      payments = payments.where((element) {
        bool isBetween;
        if (byDay.value) {
          isBetween = DateTimeHelper.isSameDay(element.date, selectedDay.value);
        } else {
          isBetween = element.date.isAfter(initialDate!) &&
              element.date.isBefore(finalDate!.add(const Duration(days: 1)));
        }
        var isReceivedBySaler = element.userId == selectedSaler.value!.id;
        return isBetween && isReceivedBySaler;
      }).toList();
      return payments.isNotEmpty;
    }).toList();

    valorRecebido.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              bool isBetween;
              if (byDay.value) {
                isBetween =
                    DateTimeHelper.isSameDay(element.date, selectedDay.value);
              } else {
                isBetween = element.date.isAfter(initialDate!) &&
                    element.date
                        .isBefore(finalDate!.add(const Duration(days: 1)));
              }
              return isBetween;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    //calcular valor recebido em dinheiro
    valorRecebidoDinheiro.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              bool isBetween;
              if (byDay.value) {
                isBetween =
                    DateTimeHelper.isSameDay(element.date, selectedDay.value);
              } else {
                isBetween = element.date.isAfter(initialDate!) &&
                    element.date
                        .isBefore(finalDate!.add(const Duration(days: 1)));
              }
              return isBetween &&
                  element.paymentMethod == PaymentMethod.dinheiro;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    //calcular valor recebido em cartão
    valorRecebidoCartao.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              bool isBetween;
              if (byDay.value) {
                isBetween =
                    DateTimeHelper.isSameDay(element.date, selectedDay.value);
              } else {
                isBetween = element.date.isAfter(initialDate!) &&
                    element.date
                        .isBefore(finalDate!.add(const Duration(days: 1)));
              }
              return isBetween && element.paymentMethod == PaymentMethod.cartao;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    //calcular valor recebido em pix
    valorRecebidoPix.value = ordersReceivedTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.payments.where((element) {
              bool isBetween;
              if (byDay.value) {
                isBetween =
                    DateTimeHelper.isSameDay(element.date, selectedDay.value);
              } else {
                isBetween = element.date.isAfter(initialDate!) &&
                    element.date
                        .isBefore(finalDate!.add(const Duration(days: 1)));
              }
              return isBetween && element.paymentMethod == PaymentMethod.pix;
            }).fold<double>(
                0, (previousValue, element) => previousValue + element.value));

    //calcular quantidade de vendas
    quantityVendas.value = orders.length;

    //calcular produtos vendidos
    productsSold.clear();

    for (var order in orders) {
      for (var product in order.products) {
        var isAvista = product.customValue != 0 && product.quantity != 0;
        if (productsSold.isEmpty) {
          productsSold.add({
            'productName': product.name,
            'quantity_avista': isAvista ? product.quantity : 0,
            'quantity_prazo': isAvista ? 0 : product.quantity,
            'temComissao': product.temComissao,
          });
        } else {
          bool found = false;
          for (var productSold in productsSold) {
            if (productSold['productName'] == product.name) {
              int qAvista = productSold['quantity_avista'] as int;
              int qPrazo = productSold['quantity_prazo'] as int;

              if (isAvista) {
                productSold['quantity_avista'] = qAvista + product.quantity;
              } else {
                productSold['quantity_prazo'] = qPrazo + product.quantity;
              }
              found = true;
              break;
            }
          }
          if (!found) {
            productsSold.add({
              'productName': product.name,
              'quantity_avista': isAvista ? product.quantity : 0,
              'quantity_prazo': isAvista ? 0 : product.quantity,
              'temComissao': product.temComissao,
            });
          }
        }
      }
    }
    //sort products by quantity and name
    productsSold.sort((a, b) {
      int qA = a['quantity_prazo'] as int;
      int qB = b['quantity_prazo'] as int;
      if (qA == qB) {
        return (a['productName'] as String).compareTo(b['productName']);
      } else {
        return qB.compareTo(qA);
      }
    });
    productsSold.refresh();

    // Povoar listas separadas por tipo de pagamento
    _populatePaymentOrdersLists(ordersReceivedTemp);
  }

  void _populatePaymentOrdersLists(List<OrderModel> ordersReceived) {
    ordersCartao.clear();
    ordersPix.clear();
    ordersDinheiro.clear();

    for (var order in ordersReceived) {
      var hasCartao = false;
      var hasPix = false;
      var hasDinheiro = false;

      for (var payment in order.payments) {
        bool isBetween;
        if (byDay.value) {
          isBetween = DateTimeHelper.isSameDay(payment.date, selectedDay.value);
        } else {
          isBetween = payment.date.isAfter(initialDate!) &&
              payment.date.isBefore(finalDate!.add(const Duration(days: 1)));
        }

        var isReceivedBySaler = payment.userId == selectedSaler.value!.id;

        if (isBetween && isReceivedBySaler) {
          switch (payment.paymentMethod) {
            case PaymentMethod.cartao:
              hasCartao = true;
              break;
            case PaymentMethod.pix:
              hasPix = true;
              break;
            case PaymentMethod.dinheiro:
              hasDinheiro = true;
              break;
            case null:
              // Ignorar pagamentos sem método definido
              break;
          }
        }
      }

      if (hasCartao) ordersCartao.add(order);
      if (hasPix) ordersPix.add(order);
      if (hasDinheiro) ordersDinheiro.add(order);
    }
  }

  void changeSaler(UserModel? value) {
    if (value != null) {
      selectedSaler.value = value;
      _saveLastSelectedSaler(value.id);
      refreshOrders();
    }
  }

  void setByDay() {
    byDay.value = !byDay.value;
    selectedDay.value = initialDate!;
    refreshOrders();
  }

  void setSelectedDay(DateTime value) {
    selectedDay.value = value;
    refreshOrders();
  }

  void previousDay() {
    selectedDay.value = selectedDay.value.subtract(const Duration(days: 1));
    refreshOrders();
  }

  void nextDay() {
    selectedDay.value = selectedDay.value.add(const Duration(days: 1));
    refreshOrders();
  }

  bool get canGoToPreviousDay {
    if (initialDate == null) return true;
    return selectedDay.value.isAfter(initialDate!);
  }

  bool get canGoToNextDay {
    if (finalDate == null) return true;
    return selectedDay.value.isBefore(finalDate!);
  }

  Future<List<ReportsBySalerData>> getReportsBySalerData(
      DateTime initialDate, DateTime finalDate) async {
    List<ReportsBySalerData> reportsBySalerData = [];

    salers.assignAll(await _userService.getUsersFromCache());

    this.initialDate = initialDate;
    this.finalDate = finalDate;

    // salers.forEach((saler) async {
    //   selectedSaler.value = saler;
    //   await refreshOrders();
    //   reportsBySalerData.add(ReportsBySalerData(
    //     orders: orders.toList(),
    //     pv: pv.value,
    //     quantityVendas: quantityVendas.value,
    //     productsSold: productsSold.toList(),
    //     saler: saler,
    //   ));
    // });

    for (var saler in salers) {
      selectedSaler.value = saler;
      await refreshOrders();
      reportsBySalerData.add(ReportsBySalerData(
        orders: orders.toList(),
        pv: pv.value,
        quantityVendas: quantityVendas.value,
        productsSold: productsSold.toList(),
        saler: saler,
      ));
    }

    reportsBySalerData.removeWhere((element) => element.quantityVendas == 0);

    reportsBySalerData.sort((a, b) {
      return a.saler.name.compareTo(b.saler.name);
    });
    return reportsBySalerData;
  }
}

class ReportsBySalerData {
  final List<OrderModel> orders;
  final int pv;
  final int quantityVendas;
  final List<Map<String, dynamic>> productsSold;
  final UserModel saler;

  ReportsBySalerData({
    required this.orders,
    required this.pv,
    required this.quantityVendas,
    required this.productsSold,
    required this.saler,
  });
}
