import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/valor_recebido_tile.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import './reports_by_saler_controller.dart';

class ReportsBySalerPage extends GetView<ReportsBySalerController> {
  const ReportsBySalerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Get.isDarkMode ? Colors.grey[900] : Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.indigo, Colors.indigo.shade700],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        foregroundColor: Colors.white,
        title: const Row(
          children: [
            Icon(FontAwesomeIcons.chartLine, size: 20),
            SizedBox(width: 8),
            Text('Relatório de Vendas'),
          ],
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header com período
              _buildPeriodHeader(),
              const SizedBox(height: 16),

              // Seleção de vendedor
              _buildSalerSelector(),
              const SizedBox(height: 20),

              // Cards de métricas
              _buildMetricsCards(),
              const SizedBox(height: 16),

              // Valor recebido
              _buildValueReceivedSection(),
              const SizedBox(height: 20),

              // Produtos vendidos
              _buildProductsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPeriodHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                FontAwesomeIcons.calendar,
                color: Colors.indigo,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Período',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (!controller.byDay.value) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.indigo.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.indigo.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'De',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        DateTimeHelper.getFormattedDate(
                            controller.initialDate!),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                    ],
                  ),
                  const Icon(
                    FontAwesomeIcons.arrowRight,
                    size: 14,
                    color: Colors.indigo,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Até',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        DateTimeHelper.getFormattedDate(controller.finalDate!),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.indigo,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: controller.setByDay,
                icon: const Icon(FontAwesomeIcons.calendarDay, size: 16),
                label: const Text('Ver por dia'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo.withOpacity(0.1),
                  foregroundColor: Colors.indigo,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.indigo.withOpacity(0.3)),
                  ),
                ),
              ),
            ),
          ] else ...[
            // Navegação por dias
            Row(
              children: [
                // Botão dia anterior
                IconButton(
                  onPressed: controller.canGoToPreviousDay
                      ? controller.previousDay
                      : null,
                  icon: Icon(
                    FontAwesomeIcons.chevronLeft,
                    size: 16,
                    color: controller.canGoToPreviousDay
                        ? Colors.indigo
                        : Colors.grey[400],
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: controller.canGoToPreviousDay
                        ? Colors.indigo.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    padding: const EdgeInsets.all(12),
                  ),
                ),
                const SizedBox(width: 8),

                // Data atual (clicável para abrir date picker)
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      showDatePicker(
                        context: Get.context!,
                        initialDate: controller.selectedDay.value,
                        firstDate: controller.initialDate!,
                        lastDate: controller.finalDate!,
                      ).then((value) {
                        if (value != null) {
                          controller.setSelectedDay(value);
                        }
                      });
                    },
                    icon: const Icon(FontAwesomeIcons.calendar, size: 16),
                    label: Text(
                      DateTimeHelper.getFormattedDate(
                          controller.selectedDay.value),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // Botão próximo dia
                IconButton(
                  onPressed:
                      controller.canGoToNextDay ? controller.nextDay : null,
                  icon: Icon(
                    FontAwesomeIcons.chevronRight,
                    size: 16,
                    color: controller.canGoToNextDay
                        ? Colors.indigo
                        : Colors.grey[400],
                  ),
                  style: IconButton.styleFrom(
                    backgroundColor: controller.canGoToNextDay
                        ? Colors.indigo.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    padding: const EdgeInsets.all(12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Botão para voltar ao modo período
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: controller.setByDay,
                icon: const Icon(FontAwesomeIcons.calendarWeek, size: 16),
                label: const Text('Ver por período'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.grey.withOpacity(0.1),
                  foregroundColor: Colors.grey[700],
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.grey.withOpacity(0.3)),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSalerSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                FontAwesomeIcons.user,
                color: Colors.indigo,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Vendedor Selecionado',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            decoration: BoxDecoration(
              color: Get.isDarkMode ? Colors.grey[700] : Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Get.isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
              ),
            ),
            child: DropdownButton<UserModel>(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              isExpanded: true,
              underline: const SizedBox(),
              value: controller.selectedSaler.value,
              onChanged: controller.changeSaler,
              icon: const Icon(
                Icons.keyboard_arrow_down,
                color: Colors.indigo,
              ),
              style: TextStyle(
                color: Get.isDarkMode ? Colors.white : Colors.black,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              items: controller.salers
                  .map((user) => DropdownMenuItem(
                        value: user,
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 16,
                              backgroundColor: Colors.indigo.withOpacity(0.2),
                              child: Text(
                                user.name.substring(0, 1).toUpperCase(),
                                style: const TextStyle(
                                  color: Colors.indigo,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Text(user.name),
                          ],
                        ),
                      ))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsCards() {
    return Row(
      children: [
        Expanded(
          child: _buildMetricCard(
            title: 'P.V',
            value: '${controller.pv.value}',
            icon: FontAwesomeIcons.chartBar,
            color: Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildMetricCard(
            title: 'Vendas',
            value: '${controller.quantityVendas.value}',
            icon: FontAwesomeIcons.shoppingCart,
            color: Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 16,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Get.isDarkMode ? Colors.white : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValueReceivedSection() {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ValorRecebidoTile(
        receivedValue: controller.valorRecebido.value,
        receivedValueDinheiro: controller.valorRecebidoDinheiro.value,
        receivedValueCartao: controller.valorRecebidoCartao.value,
        receivedValuePix: controller.valorRecebidoPix.value,
        title: 'Total Recebido',
        ordersCartao: controller.ordersCartao,
        ordersPix: controller.ordersPix,
        ordersDinheiro: controller.ordersDinheiro,
      ),
    );
  }

  Widget _buildProductsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[800] : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                FontAwesomeIcons.boxes,
                color: Colors.indigo,
                size: 16,
              ),
              SizedBox(width: 8),
              Text(
                'Produtos Vendidos',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (controller.productsSold.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    FontAwesomeIcons.boxOpen,
                    size: 48,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Nenhum produto vendido',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            )
          else
            _buildProductsTable(),
        ],
      ),
    );
  }

  Widget _buildProductsTable() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Get.isDarkMode ? Colors.grey[600]! : Colors.grey[300]!,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.indigo.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    'Produto',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    'P.V',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    'A.V',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.indigo,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Produtos
          ...controller.productsSold.asMap().entries.map((entry) {
            final index = entry.key;
            final product = entry.value;
            final isLast = index == controller.productsSold.length - 1;

            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: index % 2 == 0
                    ? (Get.isDarkMode ? Colors.grey[850] : Colors.grey[50])
                    : (Get.isDarkMode ? Colors.grey[800] : Colors.white),
                borderRadius: isLast
                    ? const BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      )
                    : null,
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Row(
                      children: [
                        if (product['temComissao'] == true)
                          Container(
                            width: 8,
                            height: 8,
                            margin: const EdgeInsets.only(right: 8),
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                          ),
                        Expanded(
                          child: Text(
                            product['productName'],
                            style: TextStyle(
                              color: product['temComissao'] == true
                                  ? (Get.isDarkMode
                                      ? Colors.blue[400]
                                      : Colors.blue[700])
                                  : (Get.isDarkMode
                                      ? Colors.white
                                      : Colors.black),
                              fontWeight: product['temComissao'] == true
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 8),
                      decoration: BoxDecoration(
                        color: product['quantity_prazo'] > 0
                            ? Colors.blue.withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        product['quantity_prazo'].toString(),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: product['quantity_prazo'] > 0
                              ? Colors.blue
                              : Colors.grey[400],
                          fontWeight: product['quantity_prazo'] > 0
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 8),
                      decoration: BoxDecoration(
                        color: product['quantity_avista'] > 0
                            ? Colors.orange.withOpacity(0.1)
                            : Colors.transparent,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        product['quantity_avista'].toString(),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: product['quantity_avista'] > 0
                              ? Colors.orange
                              : Colors.grey[400],
                          fontWeight: product['quantity_avista'] > 0
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
