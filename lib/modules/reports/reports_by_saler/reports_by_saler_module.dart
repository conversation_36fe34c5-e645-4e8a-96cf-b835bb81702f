import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/reports/reports_by_saler/reports_by_saler_bindings.dart';
import 'package:fl_app/modules/reports/reports_by_saler/reports_by_saler_page.dart';
import 'package:get/get.dart';

class ReportsBySalerModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/reports_by_saler',
      page: () => const ReportsBySalerPage(),
      binding: ReportsBySalerBindings(),
    ),
  ];
}
