class DownloadStatusModel {
  bool productsDownloaded;
  bool clientsDownloaded;
  bool ordersDownloaded;

  DownloadStatusModel({
    this.productsDownloaded = false,
    this.clientsDownloaded = false,
    this.ordersDownloaded = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'productsDownloaded': productsDownloaded,
      'clientsDownloaded': clientsDownloaded,
      'ordersDownloaded': ordersDownloaded,
    };
  }

  factory DownloadStatusModel.fromMap(Map<String, dynamic> map) {
    return DownloadStatusModel(
      productsDownloaded: map['productsDownloaded'] ?? false,
      clientsDownloaded: map['clientsDownloaded'] ?? false,
      ordersDownloaded: map['ordersDownloaded'] ?? false,
    );
  }
}
