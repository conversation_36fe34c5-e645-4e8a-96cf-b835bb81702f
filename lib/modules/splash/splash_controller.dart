import 'dart:developer';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/modules/splash/download_status_model.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:get/get.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:hive_flutter/hive_flutter.dart';

enum DownloadStatus { notStarted, downloading, completed, failed }

class SplashController extends GetxController {
  final UserService _userService;

  SplashController({required UserService userService})
      : _userService = userService;

  // Status de download para cada repositório
  final Rx<DownloadStatus> productsStatus = DownloadStatus.notStarted.obs;
  final Rx<DownloadStatus> clientsStatus = DownloadStatus.notStarted.obs;
  final Rx<DownloadStatus> ordersStatus = DownloadStatus.notStarted.obs;

  final showDownload = false.obs;

  late Box downloadStatusBox;

  RxString ordersStatusMessage = ''.obs;
  RxBool ordersInitialized = false.obs;

  RxString clientsStatusMessage = ''.obs;
  RxBool clientsInitialized = false.obs;

  RxString productsStatusMessage = ''.obs;
  RxBool productsInitialized = false.obs;

  @override
  void onReady() {
    super.onReady();
    _startInitialization();
  }

  Future<void> _startInitialization() async {
    try {
      final isLogged = await _userService.isLogged();
      log('isLogged: $isLogged');
      if (isLogged) {
        UserModel? user = await _userService.getUserAuthenticated();
        if (user != null) {
          if (user.authorized == false) {
            Get.offAllNamed('/unauthorized');
            return;
          }
          Get.put<UserModel>(user);
        }
        log('APP START: isAppInitialized: ${_userService.isAppInitialized()}');
        if (_userService.isAppInitialized()) {
          Get.offAllNamed('/home');
          return;
        }
        await _initializeApp();
        _userService.setAppInitialized(true);
        if (productsStatus.value == DownloadStatus.failed ||
            clientsStatus.value == DownloadStatus.failed ||
            ordersStatus.value == DownloadStatus.failed) {
          Get.showSnackbar(GetSnackBar(
            title: 'Erro',
            message: 'Erro ao baixar dados. Toque para retentar.',
            duration: const Duration(seconds: 5),
            onTap: (snack) {
              Get.back();
              retryFailedDownloads();
            },
          ));
        } else {
          Get.offAllNamed('/home');
        }
      } else {
        Get.offAllNamed('/login');
      }
    } catch (e, s) {
      log('Erro ao verificar se o usuário está logado: $e',
          error: e, stackTrace: s);
      Get.showSnackbar(GetSnackBar(
        title: 'Erro',
        message: 'Erro ao verificar se o usuário está logado: $e',
        duration: const Duration(seconds: 5),
      ));
      Get.offAllNamed('/login');
    }
  }

  Future<void> _initializeApp() async {
    downloadStatusBox = Hive.box('download_status');
    log('downloadStatusBox: ${downloadStatusBox.toMap()}');
    DownloadStatusModel statusModel = DownloadStatusModel.fromMap(
        Map<String, dynamic>.from(downloadStatusBox.get('status')));

    await Future.wait([
      verifyProducts(statusModel),
      verifyClients(statusModel),
      verifyOrders(statusModel),
    ]);

    await Future.delayed(const Duration(milliseconds: 500));
  }

  Future<void> verifyProducts(DownloadStatusModel statusModel) async {
    if (!statusModel.productsDownloaded) {
      await downloadProducts();
    } else {
      productsStatus.value = DownloadStatus.completed;
      productsInitialized.value = true;
      productsStatusMessage.value = 'Produtos';
    }
  }

  Future<void> verifyClients(DownloadStatusModel statusModel) async {
    if (!statusModel.clientsDownloaded) {
      await downloadClients();
    } else {
      clientsStatus.value = DownloadStatus.completed;
      clientsStatusMessage.value = 'Clientes';
      var repo = Get.find<ClientRepository>();
      await repo.initialize();

      clientsInitialized.value = true;
    }
  }

  Future<void> verifyOrders(DownloadStatusModel statusModel) async {
    if (!statusModel.ordersDownloaded) {
      await downloadOrders();
    } else {
      ordersStatus.value = DownloadStatus.completed;
      ordersStatusMessage.value = 'Pedidos';
      var repo = Get.find<OrderRepository>();
      await repo.initialize();
      ordersInitialized.value = true;
    }
  }

  Future<void> downloadProducts() async {
    final productRepository = Get.find<ProductRepository>();
    productsStatus.value = DownloadStatus.downloading;
    try {
      final result = await productRepository.getProducts();
      if (result.isNotEmpty) {
        productsStatus.value = DownloadStatus.completed;
      } else {
        productsStatus.value = DownloadStatus.failed;
      }

      // Atualizar status no Hive
      DownloadStatusModel statusModel = DownloadStatusModel.fromMap(
          Map<String, dynamic>.from(downloadStatusBox.get('status')));
      statusModel.productsDownloaded = true;
      await downloadStatusBox.put('status', statusModel.toMap());
    } catch (e) {
      log('Erro ao baixar produtos: $e');
      productsStatus.value = DownloadStatus.failed;
    }
  }

  Future<void> downloadClients() async {
    final clientRepository = Get.find<ClientRepository>();
    clientsStatus.value = DownloadStatus.downloading;
    try {
      await clientRepository.initialize();
      clientsStatus.value = DownloadStatus.completed;

      // Atualizar status no Hive
      DownloadStatusModel statusModel = DownloadStatusModel.fromMap(
          Map<String, dynamic>.from(downloadStatusBox.get('status')));
      statusModel.clientsDownloaded = true;
      await downloadStatusBox.put('status', statusModel.toMap());
    } catch (e) {
      log('Erro ao baixar clientes: $e');
      clientsStatus.value = DownloadStatus.failed;
    }
  }

  Future<void> downloadOrders() async {
    final orderRepository = Get.find<OrderRepository>();
    ordersStatus.value = DownloadStatus.downloading;
    try {
      await orderRepository.initialize();
      ordersStatus.value = DownloadStatus.completed;

      // Atualizar status no Hive
      DownloadStatusModel statusModel = DownloadStatusModel.fromMap(
          Map<String, dynamic>.from(downloadStatusBox.get('status')));
      statusModel.ordersDownloaded = true;
      await downloadStatusBox.put('status', statusModel.toMap());
    } catch (e) {
      log('Erro ao baixar pedidos: $e');
      ordersStatus.value = DownloadStatus.failed;
    }
  }

  // Método para resetar o status de download (opcional)
  Future<void> resetDownloadStatus() async {
    DownloadStatusModel resetStatus = DownloadStatusModel();
    await downloadStatusBox.put('status', resetStatus.toMap());

    // Resetar status nos observables
    productsStatus.value = DownloadStatus.notStarted;
    clientsStatus.value = DownloadStatus.notStarted;
    ordersStatus.value = DownloadStatus.notStarted;
  }

  // Método para retentar downloads que falharam
  Future<void> retryFailedDownloads() async {
    if (productsStatus.value == DownloadStatus.failed) {
      await downloadProducts();
    }
    if (clientsStatus.value == DownloadStatus.failed) {
      await downloadClients();
    }
    if (ordersStatus.value == DownloadStatus.failed) {
      await downloadOrders();
    }
  }
}
