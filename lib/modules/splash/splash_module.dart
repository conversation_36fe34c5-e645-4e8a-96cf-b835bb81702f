import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/splash/splash_bindings.dart';
import 'package:fl_app/modules/splash/splash_page.dart';
import 'package:fl_app/modules/splash/unauthorized/unauthorized_module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SplashModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/',
      page: () => const SplashPage(),
      binding: SplashBindings(),
    ),
    ...UnauthorizedModule().routers,
  ];
}
