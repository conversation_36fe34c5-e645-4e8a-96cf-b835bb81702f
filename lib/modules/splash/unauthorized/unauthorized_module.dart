import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/splash/unauthorized/unauthorized_bindings.dart';
import 'package:fl_app/modules/splash/unauthorized/unauthorized_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class UnauthorizedModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/unauthorized',
      page: () => const UnauthorizedPage(),
      binding: UnauthorizedBindings(),
    ),
  ];
}
