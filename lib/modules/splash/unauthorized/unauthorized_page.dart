import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './unauthorized_controller.dart';

class UnauthorizedPage extends GetView<UnauthorizedController> {
  const UnauthorizedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.all(32),
                child: Image.asset('assets/images/logo_azul.png'),
              ),
              const Text(
                'Aguardando autorização do administrador, volte mais tarde.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 20),
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: () {
                  controller.restartApp();
                },
                child: const Text('Reiniciar aplicativo'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
