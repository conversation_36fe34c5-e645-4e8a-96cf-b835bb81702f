import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_app/modules/splash/splash_controller.dart';

class SplashPage extends GetView<SplashController> {
  const SplashPage({super.key});

  Widget _buildDownloadItem(String title, DownloadStatus status) {
    Widget leading;
    String message;

    switch (status) {
      case DownloadStatus.notStarted:
        leading = const Icon(Icons.download, color: Colors.grey);
        message = "Aguardando...";
        break;
      case DownloadStatus.downloading:
        leading = AnimatedSwitcher(
          duration: const Duration(milliseconds: 500),
          transitionBuilder: (child, animation) {
            return RotationTransition(
              turns: child.key == const ValueKey('sync_icon')
                  ? Tween<double>(begin: 0.75, end: 1).animate(animation)
                  : Tween<double>(begin: 1, end: 1.25).animate(animation),
              child: child,
            );
          },
          child: const Icon(
            Icons.sync,
            key: Value<PERSON>ey('sync_icon'),
            color: Colors.green,
          ),
        );
        message = "Baixando...";
        break;
      case DownloadStatus.completed:
        leading = const Icon(Icons.check_circle, color: Colors.blue);
        message = "Concluído";
        break;
      case DownloadStatus.failed:
        leading = const Icon(Icons.error, color: Colors.red);
        message = "Falhou! Toque para retentar";
        break;
    }

    return GestureDetector(
      onTap: () {
        if (status == DownloadStatus.failed) {
          // Retentar download ao tocar
          switch (title) {
            case "Produtos":
              controller.downloadProducts();
              break;
            case "Clientes":
              controller.downloadClients();
              break;
            case "Pedidos":
              controller.downloadOrders();
              break;
            default:
          }
        }
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            leading,
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "$title:",
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message,
                    style: TextStyle(
                      fontSize: 16,
                      color: status == DownloadStatus.failed
                          ? Get.theme.colorScheme.error
                          : Get.theme.textTheme.bodyLarge!.color,
                      decoration: status == DownloadStatus.failed
                          ? TextDecoration.underline
                          : TextDecoration.none,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox(
        width: Get.width,
        height: Get.height,
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Obx(() {
            if (!controller.showDownload.value) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 80,
                children: [
                  const Image(
                    image: AssetImage('assets/images/logo_azul.png'),
                    width: 300,
                  ),
                  const CircularProgressIndicator(),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: SizedBox(
                      height: 20,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (controller.clientsStatusMessage.isNotEmpty)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              spacing: 8,
                              children: [
                                Text(
                                  controller.clientsStatusMessage.value,
                                  style: TextStyle(
                                    color: controller.clientsInitialized.value
                                        ? Colors.green
                                        : Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .color,
                                  ),
                                ),
                                Icon(
                                  controller.clientsInitialized.value
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color: controller.clientsInitialized.value
                                      ? Colors.green
                                      : Colors.transparent,
                                ),
                              ],
                            ),
                          if (controller.productsStatusMessage.isNotEmpty)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              spacing: 8,
                              children: [
                                Text(
                                  controller.productsStatusMessage.value,
                                  style: TextStyle(
                                    color: controller.productsInitialized.value
                                        ? Colors.green
                                        : Theme.of(context)
                                            .textTheme
                                            .bodyLarge!
                                            .color,
                                  ),
                                ),
                                Icon(
                                  controller.productsInitialized.value
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color: controller.productsInitialized.value
                                      ? Colors.green
                                      : Colors.transparent,
                                ),
                              ],
                            ),
                          if (controller.ordersStatusMessage.isNotEmpty)
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              spacing: 8,
                              children: [
                                Text(
                                  controller.ordersStatusMessage.value,
                                  style: TextStyle(
                                    color: controller.ordersInitialized.value
                                        ? Colors.green
                                        : Get.theme.textTheme.bodyLarge!.color,
                                  ),
                                ),
                                Icon(
                                  controller.ordersInitialized.value
                                      ? Icons.check_circle
                                      : Icons.error,
                                  color: controller.ordersInitialized.value
                                      ? Colors.green
                                      : Colors.transparent,
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            }
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset('assets/images/logo_azul.png', width: 300),
                const SizedBox(height: 40),
                _buildDownloadItem("Produtos", controller.productsStatus.value),
                _buildDownloadItem("Clientes", controller.clientsStatus.value),
                _buildDownloadItem("Pedidos", controller.ordersStatus.value),
                const SizedBox(height: 30),
                // Mostrar um indicador geral se ainda estiver baixando
                if (controller.productsStatus.value ==
                        DownloadStatus.downloading ||
                    controller.clientsStatus.value ==
                        DownloadStatus.downloading ||
                    controller.ordersStatus.value == DownloadStatus.downloading)
                  const CircularProgressIndicator(
                    strokeCap: StrokeCap.round,
                  ),
              ],
            );
          }),
        ),
      ),
    );
  }
}
