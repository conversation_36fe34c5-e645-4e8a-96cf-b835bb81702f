import 'package:fl_app/services/bluetooth/bluetooth_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';

class BluetoothController extends GetxController {
  final BluetoothService _bluetoothService;

  BluetoothController({required BluetoothService bluetoothService})
      : _bluetoothService = bluetoothService;

  final RxBool isBluetoothEnabled = false.obs;
  final RxString mainPrinterMac = "".obs;
  final RxString mainPrinterName = "".obs;
  final RxInt batteryLevel = 0.obs;
  final RxList<BluetoothInfo> availableBluetoothDevices = <BluetoothInfo>[].obs;

  final RxBool isLoading = false.obs;

  @override
  void onReady() {
    super.onReady();
    refreshBluetooth();
  }

  Future<void> refreshBluetooth() async {
    isBluetoothEnabled(await _bluetoothService.isBluetoothEnabled());
    mainPrinterMac(await _bluetoothService.getMainMac());
    mainPrinterName(await _bluetoothService.getMainName());
    batteryLevel(await _bluetoothService.getBaterryLevel());
    if (mainPrinterMac.value == "" && mainPrinterName.value == "") {
      availableBluetoothDevices
          .assignAll(await _bluetoothService.getAvailableBluetoothDevices());
    }
  }

  void connect(String macAdress, String name) async {
    isLoading(true);
    final result = await _bluetoothService.setConnect(macAdress, name);
    isLoading(false);
    if (result) {
      mainPrinterMac(macAdress);
      mainPrinterName(name);
    } else {
      Get.showSnackbar(const GetSnackBar(
        message: 'Erro ao conectar na impressora',
        backgroundColor: Colors.black,
        duration: Duration(seconds: 6),
      ));
    }
  }

  void disconnect() {
    Get.defaultDialog(
      title: 'Desconectar Impressora',
      middleText: 'Tem certeza que deseja desconectar a impressora?',
      textCancel: 'Cancelar',
      textConfirm: 'Confirmar',
      onConfirm: () {
        _bluetoothService.disconnect();
        _bluetoothService.setMainMac("");
        _bluetoothService.setMainName("");
        mainPrinterMac("");
        mainPrinterName("");
        refreshBluetooth();
        Get.back();
      },
    );
  }

  enableBluetooth() {}
}
