import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './bluetooth_controller.dart';

class BluetoothPage extends GetView<BluetoothController> {
  const BluetoothPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Impressora Bluetooth'),
        actions: [
          //refresh
          IconButton(
            onPressed: () => controller.refreshBluetooth(),
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body:
          //final RxBool isBluetoothEnabled = false.obs;
          // final RxBool isConnected = false.obs;
          // final RxString mainPrinterMac = "".obs;
          // final RxString mainPrinterName = "".obs;
          // final RxList<BluetoothInfo> availableBluetoothDevices = <BluetoothInfo>[].obs;
          Obx(
        () {
          if (controller.isLoading.value) {
            return const Center(child: CircularProgressIndicator());
          } else {
            return controller.isBluetoothEnabled.value
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (controller.mainPrinterMac.value != "" &&
                          controller.mainPrinterName.value != "")
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                'Impressora Conectada',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleLarge!
                                    .copyWith(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                              ),
                              const SizedBox(height: 16),
                              ListTile(
                                leading: Icon(Icons.print,
                                    color:
                                        Theme.of(context).colorScheme.primary),
                                title: Text(controller.mainPrinterName.value),
                                subtitle: Text(
                                    'MAC: ${controller.mainPrinterMac.value}'),
                              ),
                              ListTile(
                                leading: const Icon(Icons.battery_full,
                                    color: Colors.green),
                                title: const Text('Nível da Bateria'),
                                subtitle:
                                    Text('${controller.batteryLevel.value}%'),
                              ),
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed: controller.disconnect,
                                icon: const Icon(Icons.link_off),
                                label: const Text('Desconectar Impressora'),
                              ),
                            ],
                          ),
                        ),
                      if (!(controller.mainPrinterMac.value != "" &&
                          controller.mainPrinterName.value != ""))
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    'Dispositivos Bluetooth Disponíveis',
                                    style:
                                        Theme.of(context).textTheme.titleLarge,
                                  ),
                                  const SizedBox(height: 16),
                                  Expanded(
                                    child: ListView.builder(
                                      itemCount: controller
                                          .availableBluetoothDevices.length,
                                      itemBuilder: (context, index) {
                                        final device = controller
                                            .availableBluetoothDevices[index];
                                        return Card(
                                          child: ListTile(
                                            leading:
                                                const Icon(Icons.bluetooth),
                                            title: Text(device.name),
                                            subtitle: Text(device.macAdress),
                                            trailing:
                                                const Icon(Icons.chevron_right),
                                            onTap: () => controller.connect(
                                                device.macAdress, device.name),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                    ],
                  )
                : const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.bluetooth_disabled,
                            size: 80, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'O Bluetooth está desabilitado.',
                          style: TextStyle(fontSize: 18),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
          }
        },
      ),
    );
  }
}
