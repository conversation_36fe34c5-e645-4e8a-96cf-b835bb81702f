import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/bluetooth/bluetooth_bindings.dart';
import 'package:fl_app/modules/bluetooth/bluetooth_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class BluetoothModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/bluetooth',
      page: () => const BluetoothPage(),
      binding: BluetoothBindings(),
    ),
  ];
}
