import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/select_location/select_location_bindings.dart';
import 'package:fl_app/modules/select_location/select_location_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SelectLocationModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/select_location',
      page: () => const SelectLocationPage(),
      binding: SelectLocationBindings(),
    ),
  ];
}
