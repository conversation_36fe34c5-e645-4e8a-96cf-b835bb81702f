import 'dart:async';

import 'package:fl_app/application/ui/loader/loader_mixin.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class SelectLocationController extends GetxController with LoaderMixin {
  final GeolocationService _geolocationService;

  SelectLocationController({required GeolocationService geolocationService})
      : _geolocationService = geolocationService;

  final Completer<GoogleMapController> mapController =
      Completer<GoogleMapController>();

  var isLoading = false.obs;

  final Rxn<double> latitude = Rxn<double>();

  final Rxn<double> longitude = Rxn<double>();

  final initialCameraPosition = const CameraPosition(
    target: LatLng(-9.108202, -37.119815),
    zoom: 18,
  );

  final markers = <Marker>{}.obs;

  @override
  void onInit() {
    _loadMapStyles();
    loaderListener(isLoading);
    if (Get.arguments != null) {
      latitude((Get.arguments as LatLng).latitude);
      longitude((Get.arguments as LatLng).longitude);
      markers.add(
        Marker(
          markerId: const MarkerId('1'),
          position: LatLng(Get.arguments.latitude, Get.arguments.longitude),
        ),
      );
    }
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
    if (latitude.value == null && longitude.value == null) {
      await goToCurrentLocation();
    }
  }

  onMapCreated(GoogleMapController controller) {
    controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: LatLng(
            latitude.value ?? -9.108202,
            longitude.value ?? -37.119815,
          ),
          zoom: 18,
        ),
      ),
    );
    if (!mapController.isCompleted) {
      mapController.complete(controller);
    }
    if (_darkMapStyle != null && Get.isDarkMode) {
      controller.setMapStyle(_darkMapStyle!);
    }
  }

  String? _darkMapStyle;

  Future _loadMapStyles() async {
    _darkMapStyle = await rootBundle.loadString('assets/json/map_dark.json');
  }

  Future<Position?> getCurrentLocation() async {
    return await _geolocationService.getCurrentLocation();
  }

  void setLocation(double lat, double lng) {
    latitude(lat);
    longitude(lng);
    markers.clear();
    markers.add(
      Marker(
        markerId: const MarkerId('1'),
        position: LatLng(lat, lng),
      ),
    );
  }

  Future<void> goToCurrentLocation() async {
    var position = await getCurrentLocation();
    if (position != null) {
      latitude(position.latitude);
      longitude(position.longitude);
      markers.clear();
      markers.add(
        Marker(
          markerId: const MarkerId('1'),
          position: LatLng(position.latitude, position.longitude),
        ),
      );
      await mapController.future.then((value) {
        value.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(
                position.latitude,
                position.longitude,
              ),
              zoom: 18,
            ),
          ),
        );
      });
    }
  }
}
