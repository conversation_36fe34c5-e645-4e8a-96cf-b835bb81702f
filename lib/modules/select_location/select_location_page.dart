// ignore_for_file: invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import './select_location_controller.dart';

class SelectLocationPage extends GetView<SelectLocationController> {
  const SelectLocationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Selecionar Localização'),
        actions: [
          IconButton(
            onPressed: controller.goToCurrentLocation,
            icon: const Icon(Icons.my_location),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(() {
              return GoogleMap(
                  onMapCreated: (GoogleMapController mapController) {
                    controller.mapController.complete(mapController);
                    controller.onMapCreated(mapController);
                  },
                  initialCameraPosition: controller.initialCameraPosition,
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false,
                  markers: controller.markers.value,
                  onTap: (LatLng latLng) {
                    controller.setLocation(
                      latLng.latitude,
                      latLng.longitude,
                    );
                  });
            }),
          ),
          SizedBox(
            width: double.infinity,
            child: Obx(() {
              return ElevatedButton(
                onPressed: controller.latitude.value != null &&
                        controller.longitude.value != null
                    ? () {
                        Get.back(
                          result: LatLng(
                            controller.latitude.value!,
                            controller.longitude.value!,
                          ),
                        );
                      }
                    : null,
                child: const Text('Confirmar'),
              );
            }),
          ),
        ],
      ),
    );
  }
}
