import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/client/form_client/form_client_bindings.dart';
import 'package:fl_app/modules/client/form_client/form_client_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class FormClientModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/form_client',
      page: () => FormClientPage(),
      binding: FormClientBindings(),
    ),
  ];
}
