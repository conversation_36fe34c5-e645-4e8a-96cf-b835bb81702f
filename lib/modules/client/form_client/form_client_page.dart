import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './form_client_controller.dart';

class FormClientPage extends GetView<FormClientController> {
  FormClientPage({super.key});

  final isEdit = Get.arguments != null;

  final _formKey = GlobalKey<FormState>();

  final FocusNode _nameFocus = FocusNode();
  final FocusNode _addressFocus = FocusNode();
  final FocusNode _localDescriptionFocus = FocusNode();
  final FocusNode _numberFocus = FocusNode();
  final FocusNode _phoneFocus = FocusNode();

  final TextEditingController _addressController = TextEditingController(
      text: Get.arguments != null ? Get.arguments.address : '');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? 'Editar Cliente' : 'Novo Cliente'),
      ),
      body: SingleChildScrollView(
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  TextFormField(
                    initialValue: controller.client.value?.name,
                    focusNode: _nameFocus,
                    decoration: const InputDecoration(labelText: 'Nome'),
                    onFieldSubmitted: (_) {
                      FocusScope.of(context).requestFocus(_addressFocus);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Insira um nome';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      controller.name(value!);
                    },
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    controller: _addressController,
                    focusNode: _addressFocus,
                    decoration: const InputDecoration(labelText: 'Endereço'),
                    onFieldSubmitted: (_) {
                      FocusScope.of(context)
                          .requestFocus(_localDescriptionFocus);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Insira um endereço';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      controller.address(value!);
                    },
                  ),
                  //Wrap off Suggestions horizontally chip
                  Obx(() {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (controller.addressSuggestions.isNotEmpty)
                          const Text('Sugestões:'),
                        Wrap(
                          children: controller.addressSuggestions
                              .map(
                                (suggestion) => GestureDetector(
                                  onTap: () {
                                    _addressController.text = suggestion;
                                  },
                                  child: Container(
                                    margin: const EdgeInsets.all(2),
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.indigo),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(suggestion),
                                  ),
                                ),
                              )
                              .toList(),
                        ),
                      ],
                    );
                  }),
                  const SizedBox(height: 8),
                  TextFormField(
                    initialValue: controller.client.value?.localDescription,
                    focusNode: _localDescriptionFocus,
                    decoration:
                        const InputDecoration(labelText: 'Descrição do Local'),
                    onFieldSubmitted: (_) {
                      FocusScope.of(context).requestFocus(_numberFocus);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Insira uma descrição';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      controller.localDescription(value!);
                    },
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: controller.client.value?.number,
                          focusNode: _numberFocus,
                          onFieldSubmitted: (_) {
                            FocusScope.of(context).requestFocus(_phoneFocus);
                          },
                          decoration:
                              const InputDecoration(labelText: 'Número'),
                          onSaved: (value) {
                            controller.number(value!);
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      //phone number
                      Expanded(
                        child: TextFormField(
                          initialValue:
                              controller.phoneFormatter.getMaskedText(),
                          focusNode: _phoneFocus,
                          onFieldSubmitted: (_) {
                            FocusScope.of(context).unfocus();
                          },
                          decoration:
                              const InputDecoration(labelText: 'Telefone'),
                          keyboardType: TextInputType.phone,
                          onSaved: (value) {
                            controller.phoneNumber(
                                controller.phoneFormatter.getUnmaskedText());
                          },
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              if (value.length != 15) {
                                return 'Insira um telefone válido';
                              }
                            }
                            return null;
                          },
                          inputFormatters: [controller.phoneFormatter],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Obx(() {
                    return Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      height: 65,
                      alignment: Alignment.center,
                      child: DropdownButton(
                        items: controller.routes
                            .map((element) => DropdownMenuItem(
                                  value: element.id,
                                  child: Text(
                                    element.name,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ))
                            .toList(),
                        onChanged: (value) {
                          controller.routeId(value.toString());
                        },
                        value: controller.routeId.value,
                        hint: const Text('Selecione uma rota'),
                        underline: Container(),
                      ),
                    );
                  }),
                  const SizedBox(height: 8),
                  // Selecionar localização
                  Row(
                    children: [
                      Expanded(
                        child: Obx(() {
                          return OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                  color: controller.latitude.value != null
                                      ? Colors.green
                                      : Colors.grey,
                                  width: controller.latitude.value != null
                                      ? 2
                                      : 1),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.only(
                                  topLeft: const Radius.circular(10),
                                  bottomLeft: const Radius.circular(10),
                                  bottomRight:
                                      controller.longitude.value != null
                                          ? Radius.zero
                                          : const Radius.circular(10),
                                  topRight: controller.longitude.value != null
                                      ? Radius.zero
                                      : const Radius.circular(10),
                                ),
                              ),
                            ),
                            onPressed: () {
                              FocusScope.of(context).unfocus();
                              controller.goToSelectLocation(context);
                            },
                            child: Text(
                              controller.latitude.value != null
                                  ? '${controller.latitude.value}, ${controller.longitude.value}'
                                  : 'Selecione uma localização',
                              overflow: TextOverflow.ellipsis,
                            ),
                          );
                        }),
                      ),
                      Obx(
                        () => controller.longitude.value != null
                            ? SizedBox(
                                width: 40,
                                child: ElevatedButton(
                                  onPressed: controller.clearLocation,
                                  style: ElevatedButton.styleFrom(
                                    shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(10),
                                          bottomRight: Radius.circular(10)),
                                      side: BorderSide(
                                          color: Colors.red, width: 2),
                                    ),
                                    padding: const EdgeInsets.all(0),
                                    foregroundColor: Colors.red,
                                  ),
                                  child: const Icon(
                                    Icons.clear,
                                  ),
                                ),
                              )
                            : const SizedBox(),
                      ),
                      const SizedBox(width: 4),
                      ElevatedButton(
                        onPressed: () {
                          FocusScope.of(context).unfocus();
                          controller.selectCurrentLocation();
                        },
                        // label: const Text('Localização\nAtual',
                        //     textAlign: TextAlign.center),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: const Icon(Icons.my_location),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Obx(
                    () => SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: controller.isLoading.value
                            ? null
                            : () async {
                                if (_formKey.currentState!.validate()) {
                                  _formKey.currentState!.save();
                                  try {
                                    if (isEdit) {
                                      await controller.updateClient();
                                    } else {
                                      await controller.saveClient();
                                    }
                                  } catch (e, s) {
                                    Get.showSnackbar(GetSnackBar(
                                      message:
                                          '${e.toString().replaceAll('Exception:', '')}\n$s',
                                      duration: const Duration(seconds: 3),
                                    ));
                                    controller.isLoading(false);
                                  }
                                }
                              },
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          backgroundColor: Colors.indigo,
                          foregroundColor: Colors.white,
                        ),
                        child: controller.isLoading.value
                            ? const CircularProgressIndicator(
                                strokeCap: StrokeCap.round,
                              )
                            : Text(isEdit ? 'Editar' : 'Salvar'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
