import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class FormClientController extends GetxController {
  late ClientService _clientService;
  late SalesRoutesService _salesRoutesService;
  late GeolocationService _geolocationService;
  late AppStateService _appStateService;
  late UserService _userService;

  FormClientController({
    required ClientService clientService,
    required SalesRoutesService salesRoutesService,
    required AppStateService appStateService,
    required GeolocationService geolocationService,
    required UserService userService,
  }) {
    _clientService = clientService;
    _appStateService = appStateService;
    _salesRoutesService = salesRoutesService;
    _geolocationService = geolocationService;
    _userService = userService;
  }

  var isLoading = false.obs;

  final Rxn<ClientModel> client = Rxn<ClientModel>(Get.arguments);

  var name = Rxn<String>();
  var address = Rxn<String>();
  var number = Rxn<String>();
  var localDescription = Rxn<String>();
  var latitude = Rxn<double>();
  var longitude = Rxn<double>();
  var routeId = Rxn<String>();
  var phoneNumber = Rxn<String>();

  final RxList<SaleRouteModel> routes = <SaleRouteModel>[].obs;

  final RxnString isVendendoRouteId = RxnString();

  final RxList<String> addressSuggestions = <String>[].obs;

  var phoneFormatter = MaskTextInputFormatter(
      mask: '(##) #####-####',
      filter: {"#": RegExp(r'[0-9]')},
      type: MaskAutoCompletionType.lazy);

  UserModel? user;
  @override
  void onInit() {
    if (Get.arguments != null) {
      client(Get.arguments as ClientModel);
      name(Get.arguments.name);
      address(Get.arguments.address);
      number(Get.arguments.number);
      localDescription(Get.arguments.localDescription);
      latitude(Get.arguments.latitude);
      longitude(Get.arguments.longitude);
      routeId(Get.arguments.routeId);
      phoneNumber(Get.arguments.phoneNumber);
      if (Get.arguments.phoneNumber != null) {
        phoneFormatter = MaskTextInputFormatter(
            mask: '(##) #####-####',
            filter: {"#": RegExp(r'[0-9]')},
            type: MaskAutoCompletionType.lazy,
            initialText: Get.arguments.phoneNumber);
      }
    }
    _salesRoutesService.getSalesRoutesFromCache().then((value) {
      routes.clear();
      routes.addAll(value);
    });
    verifySaleRoute();

    _userService.getUserAuthenticated().then((value) {
      user = value;
      updateAddressSuggestions();
    });
    super.onInit();
  }

  Future<void> saveClient() async {
    if (isLoading.value == true) return;
    isLoading(true);
    if (routeId.value == null || routeId.value!.isEmpty) {
      isLoading(false);
      throw Exception('Selecione uma rota');
    }
    ClientModel clientModel = ClientModel(
      name: name.value!.trim(),
      address: address.value!.trim(),
      number: number.value!,
      localDescription: localDescription.value!.trim(),
      latitude: latitude.value,
      longitude: longitude.value,
      lastModified: Timestamp.now(),
      routeId: routeId.value!,
      phoneNumber: phoneNumber.value,
    );
    final clientResult = await _clientService.addClient(clientModel);
    isLoading(false);
    Get.back(result: clientResult);
  }

  Future<void> updateClient() async {
    if (isLoading.value == true) return;
    isLoading(true);
    if (routeId.value == null || routeId.value!.isEmpty) {
      isLoading(false);
      throw Exception('Selecione uma rota');
    }
    ClientModel clientModel = ClientModel(
      id: client.value!.id,
      name: name.value!.trim(),
      address: address.value!.trim(),
      number: number.value!,
      localDescription: localDescription.value!.trim(),
      latitude: latitude.value,
      longitude: longitude.value,
      lastModified: Timestamp.now(),
      routeId: routeId.value!,
      phoneNumber: phoneNumber.value,
    );
    await _clientService.updateClient(clientModel);
    isLoading(false);
    Get.back(result: clientModel);
  }

  Future<void> selectCurrentLocation() async {
    try {
      isLoading(true);
      var position = await _geolocationService.getCurrentLocation();
      latitude(position?.latitude);
      longitude(position?.longitude);
      isLoading(false);
    } catch (e) {
      isLoading(false);
      Get.snackbar('Erro', 'Erro ao obter localização $e');
    }
  }

  Future<void> goToSelectLocation(BuildContext context) async {
    try {
      if (latitude.value != null && longitude.value != null) {
        var result = await Get.toNamed('/select_location',
            arguments: LatLng(
              latitude.value!,
              longitude.value!,
            ));
        if (result != null) {
          latitude(result.latitude);
          longitude(result.longitude);
        }
      } else {
        var result = await Get.toNamed('/select_location');
        if (result != null) {
          latitude(result.latitude);
          longitude(result.longitude);
        }
      }
    } catch (e) {
      isLoading(false);
      Get.snackbar('Erro', 'Erro ao obter localização $e');
    }
  }

  Future<void> verifySaleRoute() async {
    final state = _appStateService.getAppState();

    if (state.isSelling && Get.arguments == null) {
      routeId(state.isSellingRouteId);
      isVendendoRouteId(state.isSellingRouteId);
    }
  }

  Future<void> updateAddressSuggestions() async {
    try {
      List<String> rotas = [];
      if (isVendendoRouteId.value != null) {
        rotas.add(isVendendoRouteId.value!);
      } else if (user != null && user!.cobrador) {
        rotas.addAll(user!.authorizedBillingRoutes);
      }
      final clientsNearby = await _clientService.getClientsNearby(
        await _geolocationService.getCurrentLocation(),
        rotas,
        limit: 30,
      );
      //get all addresses
      final addresses = clientsNearby.map((e) => e.address).toList();
      //remove duplicates
      final uniqueAddresses = addresses.toSet().toList();
      final fiveAddresses = uniqueAddresses.length > 5
          ? uniqueAddresses.sublist(0, 5)
          : uniqueAddresses;
      addressSuggestions.assignAll(fiveAddresses);
    } catch (e) {
      Get.snackbar('Erro', 'Erro ao obter sugestões de endereço $e');
    }
  }

  void clearLocation() {
    latitude.value = null;
    longitude.value = null;
  }
}
