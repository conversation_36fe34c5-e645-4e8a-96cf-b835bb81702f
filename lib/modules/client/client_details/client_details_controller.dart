import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';
import 'package:fl_app/services/cart/cart_service.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ClientDetailsController extends GetxController {
  final ClientService _clientService;
  final SalesRoutesRepository _salesRoutesRepository;
  final CartService _cartService;

  ClientDetailsController({
    required ClientService clientService,
    required CartService cartService,
    required SalesRoutesRepository salesRoutesRepository,
  })  : _clientService = clientService,
        _cartService = cartService,
        _salesRoutesRepository = salesRoutesRepository {
    init();
  }

  void init() {
    refreshOrders();
    routeName.value =
        _salesRoutesRepository.getSaleRouteName(client.value.routeId)!;
  }

  Future<void> goToNewCart() async {
    final newShoppingCart = await _cartService.createShoppingCart();
    await Get.toNamed('/cart', arguments: {
      'order': null,
      'cart': newShoppingCart,
      'client': client.value,
    });
    refreshOrders();
  }

  void refreshOrders() {
    _clientService.getOrdersFromClient(client.value.id!).then((orders) {
      orders.sort((a, b) => a.date.compareTo(b.date) * -1);
      this.orders.assignAll(orders);
    });
  }

  Rx<ClientModel> client = Rx<ClientModel>(Get.arguments as ClientModel);
  RxList<OrderModel> orders = <OrderModel>[].obs;

  RxString routeName = ''.obs;

  Future<void> goToFormClientEdit() async {
    final result = await Get.toNamed('/form_client', arguments: client.value);
    if (result != null) {
      client.value = result as ClientModel;
      routeName.value =
          _salesRoutesRepository.getSaleRouteName(client.value.routeId)!;
    }
  }

  Future<void> deleteClient() async {
    final delete = await Get.dialog(AlertDialog(
      title: const Text('Deletar cliente'),
      content: Text('Deseja realmente deletar o cliente ${client.value.name}?'),
      actions: [
        TextButton(
          onPressed: () {
            Get.back(result: true);
          },
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: const Text('Confirmar'),
        ),
        ElevatedButton(
            onPressed: () {
              Get.back(result: false);
            },
            child: const Text('Cancelar')),
      ],
    ));

    if (delete != null && delete) {
      try {
        await _clientService.deleteClient(client.value);
        Get.back();
      } catch (e) {
        Get.snackbar('Erro ao deletar cliente', e.toString());
      }
    }
  }

  void callPhone() {}

  void goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    refreshOrders();
  }
}
