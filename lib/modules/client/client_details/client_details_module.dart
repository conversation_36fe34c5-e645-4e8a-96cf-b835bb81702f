import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/client/client_details/client_details_bindings.dart';
import 'package:fl_app/modules/client/client_details/client_details_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ClientDetailsModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/client_details',
      page: () => const ClientDetailsPage(),
      binding: ClientDetailsBindings(),
    ),
  ];
}
