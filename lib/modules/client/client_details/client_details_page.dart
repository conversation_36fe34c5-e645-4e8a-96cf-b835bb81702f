import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/order_list_card.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './client_details_controller.dart';

class ClientDetailsPage extends GetView<ClientDetailsController> {
  const ClientDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: [
          ElevatedButton(
            onPressed: () => controller.goToFormClientEdit(),
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              side: const BorderSide(color: Colors.indigo),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            ),
            child: const Row(
              children: [
                Icon(Icons.edit),
                SizedBox(width: 4),
                Text(
                  'Editar',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          //TODO: implementar permissões para deletar cliente
          IconButton(
            onPressed: () => controller.deleteClient(),
            icon: const Icon(Icons.delete),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Obx(() {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: Get.isDarkMode
                                ? Colors.indigo[900]
                                : Colors.indigo[50],
                          ),
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                controller.client.value.name,
                                style: const TextStyle(fontSize: 24),
                                textAlign: TextAlign.center,
                              ),
                              Text(
                                controller.client.value.number != null &&
                                        controller.client.value.number != ''
                                    ? '${controller.client.value.address}, nº ${controller.client.value.number}'
                                    : controller.client.value.address,
                                textAlign: TextAlign.center,
                                style: const TextStyle(fontSize: 16),
                              ),
                              Text(
                                controller.client.value.localDescription,
                                style: const TextStyle(fontSize: 16),
                                textAlign: TextAlign.center,
                              ),
                              Text(
                                controller.routeName.value,
                                style: const TextStyle(fontSize: 16),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Pedidos',
                        style: TextStyle(
                          fontSize: 18,
                        ),
                      ),
                      Row(
                        children: [
                          controller.client.value.latitude != null &&
                                  controller.client.value.longitude != null
                              ? IconButton(
                                  onPressed: () {
                                    Get.toNamed('/client_location',
                                        arguments: controller.client.value);
                                  },
                                  icon: const Icon(Icons.map),
                                )
                              : Icon(
                                  Icons.location_off,
                                  color: Colors.red[300],
                                ),
                          if (controller.client.value.phoneNumber != null &&
                              controller.client.value.phoneNumber != '')
                            IconButton(
                              onPressed: () {
                                controller.callPhone();
                              },
                              icon: const Icon(Icons.call),
                            ),
                          // More info
                          IconButton(
                            onPressed: () {
                              Get.defaultDialog(
                                title: 'Outras informações',
                                content: Column(
                                  children: [
                                    ListTile(
                                      title: const Text('ID'),
                                      subtitle: Text(
                                        controller.client.value.id ?? "Sem ID",
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    ListTile(
                                      title: const Text('Última modificação'),
                                      subtitle: Text(
                                        '${DateTimeHelper.getFormattedDate(controller.client.value.lastModified.toDate())} ${DateTimeHelper.getFormattedTime(controller.client.value.lastModified.toDate())}',
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    ListTile(
                                      title: const Text('ID da rota'),
                                      subtitle: Text(
                                        controller.client.value.routeId,
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                            icon: const Icon(Icons.info),
                          ),
                          ElevatedButton(
                            onPressed: controller.goToNewCart,
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Get.isDarkMode ? Colors.indigo : Colors.white,
                              foregroundColor:
                                  Get.isDarkMode ? Colors.white : Colors.indigo,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              side: const BorderSide(color: Colors.indigo),
                            ),
                            child: const Text('Novo Pedido'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Obx(() => Column(
                      children: [
                        ...controller.orders.map((order) {
                          return OrderListCard(
                            order: order,
                            onTap: (orderModel) {
                              controller.goToOrderDetails(orderModel);
                            },
                          );
                        }),
                        if (controller.orders.isEmpty)
                          SizedBox(
                            height: Get.size.height * 0.5,
                            child: const Center(
                              child: Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Text('Nenhum pedido encontrado'),
                              ),
                            ),
                          ),
                      ],
                    )),
              ],
            );
          }),
        ),
      ),
    );
  }
}
