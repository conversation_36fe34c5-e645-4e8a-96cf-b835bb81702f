import 'package:fl_app/application/helpers/sales_routes_helper.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './client_controller.dart';

class ClientPage extends GetView<ClientController> {
  const ClientPage({super.key});

  Widget _buildSearchField(BuildContext context) {
    return Obx(() {
      return TextField(
        controller: controller.searchController,
        focusNode: controller.searchFocus,
        decoration: InputDecoration(
          hintText: 'Pesquisar clientes',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: controller.search.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: controller.clearSearch,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          fillColor: Theme.of(context).cardColor,
          filled: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        ),
        onChanged: controller.search.call,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Clientes (${controller.clientsFiltered.length})',
                style: const TextStyle(
                  fontSize: 18,
                ),
              ),
              if (controller.rotaName != null && controller.rotaName != '')
                Text(
                  '${controller.rotaName}',
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
            ],
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: _buildSearchField(context),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => controller.goToFormClient(),
            icon: const Icon(Icons.add),
          ),
          Obx(
            () => IconButton(
              onPressed: () {
                controller.isSelecting.toggle();
                if (!controller.isSelecting.value) {
                  controller.clientsToChangeRoute.clear();
                }
              },
              icon: Icon(controller.isSelecting.value
                  ? Icons.close
                  : Icons.check_box_outlined),
            ),
          ),
          Obx(
            () => controller.isSelecting.value
                ? PopupMenuButton(
                    onSelected: (value) {
                      if (value == 'changeRoute') {
                        controller.changeRoute();
                      }
                    },
                    itemBuilder: (context) {
                      return [
                        const PopupMenuItem(
                          value: 'changeRoute',
                          child: Text('Mudar rota'),
                        ),
                      ];
                    },
                  )
                : const SizedBox(),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(() {
              final clients = controller.clientsFiltered;
              if (clients.isEmpty && controller.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(
                    strokeCap: StrokeCap.round,
                  ),
                );
              } else if (clients.isEmpty && !controller.isLoading.value) {
                return const Center(
                  child: Text('Nenhum cliente cadastrado'),
                );
              }
              return RefreshIndicator(
                onRefresh: () async {
                  await controller.refreshClients();
                },
                child: Scrollbar(
                  child: ListView.builder(
                    itemCount: clients.length,
                    itemBuilder: (context, index) {
                      final client = clients[index];

                      final routeName =
                          SalesRoutesHelper.getSaleRouteName(client.routeId);
                      return Card(
                        child: ListTile(
                          leading: SizedBox(
                            width: 40,
                            height: 40,
                            child: Obx(
                              () => AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                transitionBuilder: (child, animation) =>
                                    ScaleTransition(
                                        scale: animation, child: child),
                                child: controller.isSelecting.value
                                    ? Checkbox(
                                        key: ValueKey(
                                            'checkbox_${client.id}'), // Chave única para distinguir os widgets
                                        value: controller.clientsToChangeRoute
                                            .contains(client),
                                        onChanged: (value) {
                                          controller.selectClient(client);
                                        },
                                      )
                                    : CircleAvatar(
                                        key: ValueKey('avatar_${client.id}'),
                                        radius: 20,
                                        child:
                                            Text(client.name[0].toUpperCase()),
                                      ),
                              ),
                            ),
                          ),
                          title: Text(client.name),
                          subtitle: Text(
                              '${client.address} ${(client.number != null && client.number != '') ? ' nº ${client.number} -' : '-'} ${routeName ?? 'Rota não encontrada'}'),
                          onTap: () => controller.goToClientDetails(client),
                          trailing: client.latitude == null ||
                                  client.longitude == null
                              ? Icon(
                                  Icons.location_off,
                                  color: Colors.red[300],
                                )
                              : null,
                        ),
                      );
                    },
                  ),
                ),
              );
            }),
          ),
          Obx(
            () => AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: controller.isSelecting.value ? 1 : 0,
              child: controller.isSelecting.value
                  ? Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(8),
                      color: Theme.of(context).colorScheme.secondaryContainer,
                      child: Text(
                        'Clientes selecionados: ${controller.clientsToChangeRoute.length}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : const SizedBox(),
            ),
          ),
        ],
      ),
    );
  }
}
