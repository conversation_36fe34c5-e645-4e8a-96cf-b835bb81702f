import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ClientController extends GetxController {
  final ClientService _clientService;
  final SalesRoutesService _salesRoutesService;

  ClientController({
    required ClientService clientService,
    required SalesRoutesService salesRoutesService,
  })  : _clientService = clientService,
        _salesRoutesService = salesRoutesService;

  final RxList<ClientModel> clients = <ClientModel>[].obs;
  final RxList<ClientModel> clientsFiltered = <ClientModel>[].obs;

  RxBool isSelecting = false.obs;
  final RxList<ClientModel> clientsToChangeRoute = <ClientModel>[].obs;

  RxBool isLoading = false.obs;

  void selectClient(ClientModel client) {
    if (clientsToChangeRoute.contains(client)) {
      clientsToChangeRoute.remove(client);
    } else {
      clientsToChangeRoute.add(client);
    }
  }

  void changeRoute() async {
    if (clientsToChangeRoute.isEmpty) {
      Get.snackbar(
        'Atenção',
        'Selecione ao menos um cliente para mudar de rota',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }
    var routes = await _salesRoutesService.getSalesRoutesFromCache();
    Rx<SaleRouteModel?> selectedRoute = Rx<SaleRouteModel?>(null);
    Get.defaultDialog(
      title: 'Mudar rota',
      content: Column(
        children: [
          const Text('Selecione a nova rota para os clientes selecionados',
              textAlign: TextAlign.center),
          const SizedBox(height: 16),
          Obx(
            () => Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(5),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: DropdownButton<String>(
                underline: Container(),
                value: selectedRoute.value?.id,
                items: routes
                    .map(
                      (route) => DropdownMenuItem(
                        value: route.id,
                        child: Text(route.name),
                      ),
                    )
                    .toList(),
                onChanged: (value) {
                  selectedRoute.value =
                      routes.firstWhere((route) => route.id == value);
                },
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('Cancelar'),
              ),
              TextButton(
                onPressed: () async {
                  if (selectedRoute.value == null) {
                    Get.showSnackbar(
                      const GetSnackBar(
                        title: 'Atenção',
                        message: 'Selecione uma rota para continuar',
                        duration: Duration(seconds: 2),
                      ),
                    );
                    return;
                  }
                  Get.back();
                  changeRoutes(selectedRoute.value!.id!);
                },
                child: const Text('Mudar rota'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> changeRoutes(String selectedRoute) async {
    isLoading(true);
    await _clientService.changeRoute(
      clientsToChangeRoute,
      selectedRoute,
    );
    clientsToChangeRoute.clear();
    refreshClients();
    isLoading(false);
    Get.showSnackbar(
      const GetSnackBar(
        title: 'Sucesso',
        message: 'Clientes alterados de rota com sucesso',
        duration: Duration(seconds: 2),
      ),
    );
  }

  RxString search = ''.obs;

  String? rotaId = Get.arguments;
  String? rotaName = '';

  TextEditingController searchController = TextEditingController();

  final workers = <Worker>[].obs;

  FocusNode searchFocus = FocusNode();

  @override
  void onInit() {
    super.onInit();
    refreshClients();
    if (rotaId != null && rotaId!.isNotEmpty) {
      var route = _salesRoutesService.getRouteById(rotaId!);
      rotaName = route?.name ?? 'Rota não encontrada';
    }
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchClient(),
        time: const Duration(milliseconds: 500),
      ),
    );
    super.onReady();
  }

  @override
  void onClose() {
    for (var worker in workers) {
      worker.dispose();
    }
    super.onClose();
  }

  Future<void> goToFormClient() async {
    await Get.toNamed('/form_client');
    await refreshClients();
  }

  Future<void> goToClientDetails(ClientModel client) async {
    await Get.toNamed('/client_details', arguments: client);
    await refreshClients();
  }

  Future<void> deleteClient(ClientModel client) async {
    await _clientService.deleteClient(client);
    clients.remove(client);
  }

  Future<void> refreshClients() async {
    isLoading(true);
    _clientService.getClientsFromCache(rotaId).then((clients) {
      this.clients.assignAll(clients);
      clientsFiltered.assignAll(clients);
      if (search.isNotEmpty) {
        searchClient();
      }
    });
    isLoading(false);
  }

  clearSearch() {
    search.value = '';
    clientsFiltered.assignAll(clients);
    searchController.clear();
    searchFocus.unfocus();
  }

  searchClient() {
    if (search.isEmpty) {
      clientsFiltered.assignAll(clients);
      return;
    }
    clientsFiltered.assignAll(
      clients.where(
        (client) =>
            TextHelper.removeAcento(client.name.toLowerCase())
                .contains(TextHelper.removeAcento(search.toLowerCase())) ||
            TextHelper.removeAcento(client.address.toLowerCase())
                .contains(TextHelper.removeAcento(search.toLowerCase())) ||
            TextHelper.removeAcento(client.localDescription.toLowerCase())
                .contains(TextHelper.removeAcento(search.toLowerCase())),
      ),
    );
  }
}
