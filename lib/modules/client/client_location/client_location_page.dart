import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import './client_location_controller.dart';

class ClientLocationPage extends GetView<ClientLocationController> {
  const ClientLocationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          title: const Text('Localização'),
          actions: [
            IconButton(
              onPressed: () => controller.isSatelliteView.toggle(),
              icon: Icon(controller.isSatelliteView.value
                  ? Icons.map
                  : Icons.satellite),
            ),
          ],
        ),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  //go to client location button
                  Expanded(
                    child: ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.indigo,
                        foregroundColor: Colors.white,
                      ),
                      onPressed: controller.goToClientLocation,
                      label: const Text('Ir para localização do cliente'),
                      icon: const Icon(Icons.location_on),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                height: Get.size.height,
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: GoogleMap(
                    initialCameraPosition: controller.cameraPosition!,
                    mapType: controller.isSatelliteView.value
                        ? MapType.satellite
                        : MapType.normal,
                    onMapCreated: controller.onMapCreated,
                    myLocationEnabled: true,
                    myLocationButtonEnabled: true,
                    zoomControlsEnabled: false,
                    markers: controller.markers,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
