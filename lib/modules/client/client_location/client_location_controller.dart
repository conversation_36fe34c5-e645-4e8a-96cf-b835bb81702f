import 'dart:async';

import 'package:fl_app/models/client_model.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:url_launcher/url_launcher_string.dart';

class ClientLocationController extends GetxController {
  ClientModel client = Get.arguments as ClientModel;

  CameraPosition? cameraPosition;
  Set<Marker> markers = {};

  RxBool isSatelliteView = false.obs;

  final Completer<GoogleMapController> _mapController =
      Completer<GoogleMapController>();

  String? _darkMapStyle;

  onMapCreated(GoogleMapController controller) {
    if (!_mapController.isCompleted) {
      _mapController.complete(controller);
    }
    if (_darkMapStyle != null && Get.isDarkMode) {
      controller.setMapStyle(_darkMapStyle!);
    }
  }

  @override
  void onInit() {
    _loadMapStyles();
    super.onInit();
    if (client.latitude != null && client.longitude != null) {
      cameraPosition = CameraPosition(
          target: LatLng(client.latitude!, client.longitude!), zoom: 18);

      markers.add(Marker(
          markerId: const MarkerId('1'),
          position: LatLng(client.latitude!, client.longitude!)));
    }
  }

  Future _loadMapStyles() async {
    _darkMapStyle = await rootBundle.loadString('assets/json/map_dark.json');
  }

  void goToClientLocation() async {
    //open google maps with client location
    String url =
        'https://www.google.com/maps/search/?api=1&query=${client.latitude},${client.longitude}';

    if (await canLaunchUrlString(url)) {
      await launchUrlString(url);
    } else {
      Get.showSnackbar(const GetSnackBar(
          title: 'Erro', message: 'Erro ao ligar para o cliente'));
    }
  }
}
