import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/client/client_location/client_location_bindings.dart';
import 'package:fl_app/modules/client/client_location/client_location_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ClientLocationModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/client_location',
      page: () => const ClientLocationPage(),
      binding: ClientLocationBindings(),
    ),
  ];
}
