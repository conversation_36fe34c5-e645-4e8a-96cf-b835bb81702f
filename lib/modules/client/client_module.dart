import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/client/client_bindings.dart';
import 'package:fl_app/modules/client/client_details/client_details_module.dart';
import 'package:fl_app/modules/client/client_location/client_location_module.dart';
import 'package:fl_app/modules/client/client_page.dart';
import 'package:fl_app/modules/client/form_client/form_client_module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ClientModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/clients',
      page: () => const ClientPage(),
      binding: ClientBindings(),
    ),
    ...FormClientModule().routers,
    ...ClientDetailsModule().routers,
    ...ClientLocationModule().routers,
  ];
}
