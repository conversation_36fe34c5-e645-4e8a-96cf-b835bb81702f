import 'dart:developer';

import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/order_product_model.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class SaleStockController extends GetxController {
  final OrderRepository _orderRepository;
  final ProductRepository _productRepository;

  SaleStockController({
    required OrderRepository orderRepository,
    required ProductRepository productRepository,
  })  : _orderRepository = orderRepository,
        _productRepository = productRepository;

  String routeId = Get.arguments;

  final RxList<OrderModel> lastFourMonthsOrders = <OrderModel>[].obs;
  final RxList<ProductSales> productSalesList = <ProductSales>[].obs;
  final RxBool isLoading = true.obs;

  // Gera os últimos 4 meses completos, já excluindo o atual
  final List<MonthInfo> lastFourMonths = getLastFourCompleteMonths();

  final RxList<ProductModel> allProducts = <ProductModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    _loadData();
    _loadProducts();
  }

  Future<void> _loadProducts() async {
    try {
      final products =
          await _productRepository.getProductsFromCache(withDeleted: true);
      allProducts.assignAll(products);
    } catch (e) {
      log('Erro ao carregar produtos: $e');
    }
  }

  Future<void> _loadData() async {
    try {
      isLoading(true);
      final orders = await _orderRepository.getRouteSalesLast4Months(routeId);
      lastFourMonthsOrders.assignAll(orders);

      _processOrders(orders);
    } catch (e) {
      log('Erro ao carregar vendas dos últimos 4 meses: $e');
    } finally {
      isLoading(false);
    }
  }

  void _processOrders(List<OrderModel> orders) {
    // Mapa para agrupar produtos (productId + variationId) => ProductSales
    Map<String, ProductSales> salesMap = {};

    for (var order in orders) {
      for (var product in order.products) {
        final productKey =
            '${product.productId}-${product.variationId ?? 'null'}';

        // Se não existe, inicializa
        if (!salesMap.containsKey(productKey)) {
          salesMap[productKey] = ProductSales(
            product: product,
            monthlySales: {},
            averageSales: 0,
            adjustedQuantity: 0,
          );
        }

        // Verifica se a data do pedido cai em algum dos “últimos 4 meses”
        final orderYear = order.date.year;
        final orderMonth = order.date.month;

        for (var m in lastFourMonths) {
          if (m.year == orderYear && m.month == orderMonth) {
            salesMap[productKey]!.monthlySales[m.monthAbbr] =
                (salesMap[productKey]!.monthlySales[m.monthAbbr] ?? 0) +
                    product.quantity;
            break;
          }
        }
      }
    }

    // Calcula a média de vendas
    salesMap.forEach((_, productSales) {
      final total = productSales.monthlySales.values.fold<int>(
        0,
        (sum, qty) => sum + qty,
      );

      final count = productSales.monthlySales.length;
      productSales.averageSales = (count > 0) ? total / count : 0;
      productSales.adjustedQuantity = productSales.averageSales.round();
    });

    // 1) Converte os valores do mapa para uma lista
    var allProductSales = salesMap.values.toList();

    // 2) Ordena pelas maiores médias (opcional)
    allProductSales.sort((a, b) => b.averageSales.compareTo(a.averageSales));

    // 3) Agrupa por productId
    //    Exemplo: <productId, List<ProductSales>>
    final groupedByProductId = <String, List<ProductSales>>{};

    for (var ps in allProductSales) {
      final pid = ps.product.productId;
      groupedByProductId.putIfAbsent(pid, () => []).add(ps);
    }

    // 4) Monta a lista final, deixando variações “um depois do outro”
    //    Se quiser ordenar as variações de cada produto de outra forma (p. ex. alfabética),
    //    faça sort no groupedByProductId[pid] aqui dentro.
    final finalList = <ProductSales>[];
    for (var entry in groupedByProductId.entries) {
      // entry.key é o productId
      // entry.value é a lista de variações

      // Se quiser ordenar as variações pelo nome ou variationId:
      entry.value.sort((a, b) {
        final aVar = a.product.variationId ?? '';
        final bVar = b.product.variationId ?? '';
        return aVar.compareTo(bVar);
        // ou outra lógica, se quiser maior->menor, use bVar.compareTo(aVar)
      });

      finalList.addAll(entry.value);
    }

    // 5) Atribui a lista final, com cada productId “agrupado” e
    //    variações juntas
    productSalesList.assignAll(finalList);
  }

  /// Conclusão
  void concluirCarregamento() {
    // Lógica para salvar as quantidades ajustadas
    Get.snackbar('Carregamento', 'Concluído com sucesso!');
  }

  String? getImagemProduto(OrderProductModel product) {
    final productModel = allProducts.firstWhereOrNull(
      (p) => p.id == product.productId,
    );

    if (productModel != null) {
      if (product.variationId != null) {
        final variation = productModel.variations.firstWhereOrNull(
          (v) => v.id == product.variationId,
        );

        if (variation != null) {
          return variation.imageUrl;
        }
      }
      return productModel.imagem;
    }
    return null;
  }
}

class ProductSales {
  final OrderProductModel product;
  final Map<String, int> monthlySales; // Ex: {'Set': 12, 'Out': 15, ...}
  double averageSales;
  int adjustedQuantity; // Para armazenar a quantidade ajustada pelo usuário

  ProductSales({
    required this.product,
    required this.monthlySales,
    required this.averageSales,
    required this.adjustedQuantity,
  });

  int get maxQuantity {
    return monthlySales.values.reduce((a, b) => a > b ? a : b);
  }
}

class MonthInfo {
  final int year;
  final int month;
  final String monthAbbr;

  MonthInfo({
    required this.year,
    required this.month,
    required this.monthAbbr,
  });
}

List<MonthInfo> getLastFourCompleteMonths() {
  List<MonthInfo> list = [];

  DateTime now = DateTime.now();
  // Mês atual (não queremos incluir), então vamos para o "mês anterior"
  DateTime date =
      DateTime(now.year, now.month, 1).subtract(const Duration(days: 1));
  // Agora 'date' está em algum dia do mês anterior.
  // Garantimos que seja o primeiro dia do mês (facilita a leitura):
  date = DateTime(date.year, date.month, 1);

  // Precisamos de 4 meses completos
  for (int i = 0; i < 4; i++) {
    // Formata abreviação em português
    String abbr = DateFormat.MMM('pt_BR').format(date).toLowerCase();
    // Ajusta para 3 letras, se quiser
    if (abbr.length > 3) {
      abbr = abbr.substring(0, 3);
    }

    list.add(
      MonthInfo(
        year: date.year,
        month: date.month,
        monthAbbr: abbr,
      ),
    );

    // Vai para o mês anterior
    date = DateTime(date.year, date.month - 1, 1);
  }

  // A lista ficou em ordem “mês mais recente -> mais antigo”.
  // Se quiser exibir do mais antigo para o mais recente, faça .reversed.toList()
  return list.reversed.toList();
}
