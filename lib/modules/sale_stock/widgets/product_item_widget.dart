import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_app/modules/sale_stock/sale_stock_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProductItemWidget extends StatefulWidget {
  final ProductSales productSales;
  final List<MonthInfo> lastFourMonths;
  final String? image;

  const ProductItemWidget({
    super.key,
    required this.productSales,
    required this.lastFourMonths,
    this.image,
  });

  @override
  State<ProductItemWidget> createState() => _ProductItemWidgetState();
}

class _ProductItemWidgetState extends State<ProductItemWidget> {
  late int _quantity; // Armazena a quantidade ajustada
  late TextEditingController _quantityController;

  @override
  void initState() {
    super.initState();
    // // Inicializa com a média arredondada
    // _quantity = (widget.productSales.adjustedQuantity * 1.15).round();

    //pega a maior quantidade vendida e adiciona 10%
    _quantity = (widget.productSales.maxQuantity * 1.05).round();
    _quantityController = TextEditingController(text: _quantity.toString());
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final product = widget.productSales.product;
    final sales = widget.productSales.monthlySales;
    final avg = widget.productSales.averageSales;
    final months = widget.lastFourMonths;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          children: [
            // Linha com a imagem e o nome do produto
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              spacing: 12,
              children: [
                // Imagem do produto

                // Nome do produto
                Expanded(
                  child: Text(
                    product.name,
                    style: const TextStyle(
                        fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Meses e quantidades lado a lado
            Row(
              spacing: 8,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: CachedNetworkImage(
                    imageUrl: widget.image ?? 'https://via.placeholder.com/60',
                    width: 60,
                    height: 60,
                    fit: BoxFit.cover,
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                    useOldImageOnUrlChange: false,
                  ),
                ),
                Expanded(
                    flex: 4,
                    child: _buildLastFourMonthsInfo(months, sales, avg)),
                // Expanded(
                //   flex: 2,
                //   child: _buildQuantityField(),
                // )
                Expanded(
                  flex: 2,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Get.theme.colorScheme.primary.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'Sugerido:',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Get.theme.colorScheme.secondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _quantity.toString(),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Mostra os últimos 4 meses lado a lado e as quantidades vendidas abaixo
  Widget _buildLastFourMonthsInfo(
      List<MonthInfo> months, Map<String, int> sales, double avg) {
    List<Widget> monthWidgets = [];
    List<Widget> qtyWidgets = [];

    for (var month in months) {
      monthWidgets.add(
        Expanded(
          child: Center(
            child: Text(
              month.monthAbbr,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ),
      );

      qtyWidgets.add(
        Expanded(
          child: Center(
            child: Text(
              sales[month.monthAbbr]?.toString() ?? '0',
            ),
          ),
        ),
      );
    }

    monthWidgets.add(
      Expanded(
        child: Center(
          child: Text(
            'Média',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.secondary,
            ),
          ),
        ),
      ),
    );

    qtyWidgets.add(
      Expanded(
        child: Center(
          child: Text(
            avg.toStringAsFixed(0),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Get.theme.colorScheme.secondary,
            ),
          ),
        ),
      ),
    );

    return Column(
      children: [
        Row(
          children: monthWidgets,
        ),
        Row(
          children: qtyWidgets,
        ),
      ],
    );
  }

  /// Campo de entrada para quantidade, com botões + e -
  Widget _buildQuantityField() {
    return Row(
      children: [
        // Botão -
        IconButton.filled(
          icon: const Icon(Icons.remove, color: Colors.white),
          onPressed: () {
            if (_quantity > 0) {
              setState(() {
                _quantity--;
                _quantityController.text = _quantity.toString();
              });
            }
          },
        ),
        // Campo de texto mostrando a quantidade
        Expanded(
          child: TextFormField(
            textAlign: TextAlign.center,
            controller: _quantityController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        // Botão +
        IconButton.filled(
          icon: const Icon(Icons.add, color: Colors.white),
          onPressed: () {
            setState(() {
              _quantity++;
              _quantityController.text = _quantity.toString();
            });
          },
        ),
      ],
    );
  }
}
