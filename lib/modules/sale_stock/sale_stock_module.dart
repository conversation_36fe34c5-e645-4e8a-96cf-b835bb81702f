import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/sale_stock/sale_stock_bindings.dart';
import 'package:fl_app/modules/sale_stock/sale_stock_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SaleStockModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/sale-stock',
      page: () => const SaleStockPage(),
      binding: SaleStockBindings(),
    ),
  ];
}
