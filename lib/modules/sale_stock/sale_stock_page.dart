import 'package:fl_app/modules/sale_stock/widgets/product_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'sale_stock_controller.dart';

class SaleStockPage extends GetView<SaleStockController> {
  const SaleStockPage({super.key});

  @override
  Widget build(BuildContext context) {
    // Usamos Scaffold para ter AppBar, corpo, etc.
    return Scaffold(
      appBar: AppBar(
        title: const Text('Carregar Carro'),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final products = controller.productSalesList;

        if (products.isEmpty) {
          return const Center(child: Text('Nenhum produto encontrado.'));
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: products.length,
          itemBuilder: (context, index) {
            final productSales = products[index];
            return ProductItemWidget(
              productSales: productSales,
              lastFourMonths: controller.lastFourMonths,
              image: controller.getImagemProduto(productSales.product),
            );
          },
        );
      }),
      // bottomNavigationBar: SafeArea(
      //   child: Padding(
      //     padding: const EdgeInsets.all(16),
      //     child: ElevatedButton(
      //       onPressed: () => controller.concluirCarregamento(),
      //       child: const Text('Concluir Carregamento'),
      //     ),
      //   ),
      // ),
    );
  }
}
