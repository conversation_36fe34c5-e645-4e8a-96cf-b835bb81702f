import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/main.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';
import 'package:restart_app/restart_app.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

class SettingsController extends GetxController {
  final UserService _userService;
  final SettingsService _settingsService;

  SettingsController({
    required UserService userService,
    required SettingsService settingsService,
  })  : _userService = userService,
        _settingsService = settingsService;

  Rxn<UserModel> user = Rxn<UserModel>();

  RxBool isLoading = false.obs;

  RxBool isDarkMode = false.obs;

  Rx<int> initialFinalDay = 19.obs;

  // ----------------------
  // Shorebird (via service)
  // ----------------------
  bool get isShorebirdAvailable => _settingsService.isShorebirdAvailable;

  final Rxn<Patch> currentPatchVersion = Rxn<Patch>();
  final RxBool isCheckingForUpdate = false.obs;
  final RxString releaseVersion = ''.obs;

  @override
  void onInit() {
    super.onInit();
    initialFinalDay.value = _settingsService.getInitialFinalDay();

    // Ler a patch atual no service
    _settingsService.readCurrentPatch().then((currentPatch) {
      currentPatchVersion.value = currentPatch;
    });
    getReleaseVersion();
    init();
  }

  void init() async {
    user.value = await _userService.getUserAuthenticated();
    Get.find<SettingsService>().getDarkMode().then((value) {
      isDarkMode.value = value;
    });
  }

  Future<String> getReleaseVersion() async {
    final version = await _settingsService.getPubspecVersion();
    releaseVersion.value = version;
    return version;
  }

  // ----------------------
  // Checar atualização
  // ----------------------
  Future<void> checkForUpdate() async {
    isCheckingForUpdate.value = true;

    final UpdateStatus status =
        await _settingsService.checkForShorebirdUpdate();
    isCheckingForUpdate.value = false;

    if (status == UpdateStatus.outdated) {
      _showUpdateAvailableBanner();
    } else if (status == UpdateStatus.restartRequired) {
      Get.showSnackbar(
        GetSnackBar(
          title: 'Nova atualização disponível',
          message: 'Reinicie o aplicativo para aplicar a atualização.',
          duration: const Duration(seconds: 5),
          mainButton: TextButton(
            onPressed: () {
              Restart.restartApp();
            },
            child: const Text('Reiniciar'),
          ),
          backgroundColor: Colors.green,
          borderRadius: 8,
          margin: const EdgeInsets.all(8),
        ),
      );
    } else if (status == UpdateStatus.unavailable) {
      Get.showSnackbar(
        const GetSnackBar(
          title: 'Não foi possível verificar atualizações',
          message: 'Tente novamente mais tarde.',
          duration: Duration(seconds: 5),
          backgroundColor: Colors.red,
          borderRadius: 8,
          margin: EdgeInsets.all(8),
        ),
      );
    } else if (status == UpdateStatus.upToDate) {
      Get.showSnackbar(
        const GetSnackBar(
          title: 'Nenhuma atualização disponível',
          message: 'Você já está na versão mais recente.',
          duration: Duration(seconds: 5),
          backgroundColor: Colors.green,
          borderRadius: 8,
          margin: EdgeInsets.all(8),
        ),
      );
    } else {
      Get.showSnackbar(
        const GetSnackBar(
          title: 'Erro ao verificar atualização',
          message: 'Tente novamente mais tarde.',
          duration: Duration(seconds: 5),
          backgroundColor: Colors.red,
          borderRadius: 8,
          margin: EdgeInsets.all(8),
        ),
      );
    }
  }

  // ----------------------
  // Fazer download da patch
  // ----------------------
  Future<void> _downloadUpdate() async {
    _showDownloadingBanner();

    try {
      await _settingsService.applyShorebirdUpdate();
    } on UpdateException catch (error) {
      Get.showSnackbar(
        GetSnackBar(
          title: 'Erro ao baixar atualização',
          message: error.message,
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
    Get.closeAllSnackbars();
    _showRestartBanner();
  }

  // Exibição de banners (Snackbar)
  void _showDownloadingBanner() {
    Get.showSnackbar(
      const GetSnackBar(
        titleText: Text(
          'Baixando atualização...',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        messageText: Text(
          'Aguarde enquanto a atualização é baixada.',
          style: TextStyle(
            color: Colors.black,
            fontSize: 14,
          ),
        ),
        duration: Duration(seconds: 25),
        showProgressIndicator: true,
        backgroundColor: Colors.amber,
        borderRadius: 8,
        margin: EdgeInsets.all(8),
      ),
    );
  }

  void _showUpdateAvailableBanner() {
    Get.showSnackbar(
      GetSnackBar(
        titleText: const Text(
          'Nova atualização disponível',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        messageText: const Text(
          'Clique aqui para baixar',
          style: TextStyle(
            color: Colors.black,
            fontSize: 14,
          ),
        ),
        duration: const Duration(seconds: 10),
        onTap: (snack) {
          Get.closeAllSnackbars();
          _downloadUpdate();
        },
        backgroundColor: Colors.amber,
        borderRadius: 8,
        margin: const EdgeInsets.all(8),
      ),
    );
  }

  void _showRestartBanner() {
    Get.showSnackbar(
      GetSnackBar(
        title: 'Atualização concluída',
        message: 'Reinicie o aplicativo para aplicar a atualização.',
        duration: const Duration(seconds: 5),
        mainButton: TextButton(
          onPressed: () {
            Restart.restartApp();
          },
          child: const Text('Reiniciar'),
        ),
        backgroundColor: Colors.green,
        borderRadius: 8,
        margin: const EdgeInsets.all(8),
      ),
    );
  }

  void changeAdminPassword() async {
    try {
      final isConnected = await Get.find<ConnectionService>().checkConnection();
      if (!isConnected) {
        Get.showSnackbar(
          const GetSnackBar(
            title: 'Erro',
            message: 'Sem conexão com a internet para alterar a senha',
            backgroundColor: Colors.red,
            duration: Duration(seconds: 1),
          ),
        );
        return;
      }
      final savePassword = await _settingsService.getAdminPassword();
      //get dialog to change password, requires current password, new password and confirm new password
      //if current password is correct, save new password
      //if current password is incorrect, show error message
      //if new password and confirm new password are different, show error message
      //if new password and confirm new password are equal, save new password

      String currentPassword = '';
      String newPassword = '';
      String confirmNewPassword = '';

      FocusNode currentPasswordFocus = FocusNode();
      FocusNode newPasswordFocus = FocusNode();
      FocusNode confirmNewPasswordFocus = FocusNode();

      bool result = await Get.dialog(
        AlertDialog(
          title: const Text(
            'Alterar senha de administrador',
            style: TextStyle(fontSize: 18),
          ),
          actionsPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  onChanged: (value) {
                    currentPassword = value;
                  },
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'Senha atual',
                  ),
                  focusNode: currentPasswordFocus,
                  onSubmitted: (value) {
                    currentPasswordFocus.unfocus();
                    newPasswordFocus.requestFocus();
                  },
                ),
                const SizedBox(height: 8),
                TextField(
                  onChanged: (value) {
                    newPassword = value;
                  },
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'Nova senha',
                  ),
                  focusNode: newPasswordFocus,
                  onSubmitted: (value) {
                    newPasswordFocus.unfocus();
                    confirmNewPasswordFocus.requestFocus();
                  },
                ),
                const SizedBox(height: 8),
                TextField(
                  onChanged: (value) {
                    confirmNewPassword = value;
                  },
                  obscureText: true,
                  decoration: const InputDecoration(
                    labelText: 'Confirmar nova senha',
                  ),
                  focusNode: confirmNewPasswordFocus,
                  onSubmitted: (value) {
                    confirmNewPasswordFocus.unfocus();
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back(result: false);
              },
              child: const Text('Cancelar'),
            ),
            TextButton(
              onPressed: () async {
                if (newPassword.isEmpty ||
                    confirmNewPassword.isEmpty ||
                    currentPassword.isEmpty) {
                  Get.showSnackbar(
                    const GetSnackBar(
                      title: 'Erro',
                      message: 'Preencha todos os campos',
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 1),
                    ),
                  );
                  return;
                }
                if (currentPassword == savePassword) {
                  if (newPassword == confirmNewPassword) {
                    await _settingsService.setAdminPassword(newPassword);
                    Get.back(result: true);
                  } else {
                    Get.showSnackbar(
                      const GetSnackBar(
                        title: 'Erro',
                        message: 'Nova senha e confirmação de senha diferentes',
                        backgroundColor: Colors.red,
                        duration: Duration(seconds: 1),
                      ),
                    );
                  }
                } else {
                  Get.showSnackbar(
                    const GetSnackBar(
                      title: 'Erro',
                      message: 'Senha atual incorreta',
                      backgroundColor: Colors.red,
                      duration: Duration(seconds: 1),
                    ),
                  );
                }
              },
              child: const Text('Salvar'),
            ),
          ],
        ),
      );
      if (result) {
        Get.showSnackbar(
          const GetSnackBar(
            title: 'Sucesso',
            message: 'Senha alterada com sucesso',
            backgroundColor: Colors.green,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      Get.showSnackbar(
        GetSnackBar(
          title: 'Erro',
          message: 'Erro ao alterar senha: $e',
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  forceUpdateClients() async {
    isLoading(true);
    try {
      if (await Get.find<ConnectionService>().checkConnection() == false) {
        Get.showSnackbar(const GetSnackBar(
            message: 'Sem conexão para atualizar os clientes'));
        return;
      }
      final ClientRepository clientRepository = Get.find();
      await clientRepository.updateCache();
      Get.showSnackbar(
          const GetSnackBar(message: 'Clientes atualizados com sucesso'));
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
          message: 'Erro ao atualizar os clientes: $e',
          backgroundColor: Colors.red));
    } finally {
      isLoading(false);
    }
  }

  forceUpdateOrders() async {
    isLoading(true);
    SalesRoutesService salesRoutesService = Get.find<SalesRoutesService>();

    final routes = await salesRoutesService.getSalesRoutesFromCache();

    List<SaleRouteModel> selectedRoutes = [];

    List<MultiSelectItem<SaleRouteModel>> items = [
      MultiSelectItem(
        SaleRouteModel(
          name: 'all',
          salesDay: 0,
          lastModified: Timestamp.now(),
        ),
        'Todas as rotas',
      ),
      ...routes.map((e) => MultiSelectItem(
            e,
            e.name,
          ))
    ];

    await Get.dialog(
      MultiSelectDialog(
        title: const Text('Selecione as rotas'),
        items: items,
        initialValue: selectedRoutes,
        onConfirm: (values) {
          selectedRoutes = values;
        },
        checkColor: Colors.amber[400],
        selectedColor: Colors.indigo,
      ),
    );

    try {
      if (await Get.find<ConnectionService>().checkConnection() == false) {
        Get.showSnackbar(const GetSnackBar(
            message: 'Sem conexão para atualizar os pedidos'));
        return;
      } else if (selectedRoutes.isEmpty) {
        Get.showSnackbar(
            const GetSnackBar(message: 'Nenhuma rota selecionada'));
        return;
      }

      if (selectedRoutes.where((e) => e.name == 'all').isNotEmpty) {
        final OrderRepository orderRepository = Get.find();
        await orderRepository.updateCache();
      } else {
        final OrderRepository orderRepository = Get.find();
        List<String> routesIds = selectedRoutes.map((e) => e.id!).toList();
        await orderRepository.updateCacheByRoutes(routesIds);
      }
      Get.showSnackbar(
          const GetSnackBar(message: 'Pedidos atualizados com sucesso'));
    } catch (e) {
      log('Erro ao atualizar os pedidos: $e');
      Get.showSnackbar(GetSnackBar(
          message: 'Erro ao atualizar os pedidos: $e',
          backgroundColor: Colors.red));
    } finally {
      isLoading(false);
    }
  }

  Future<void> removeOldOrdersFromCache() async {
    isLoading(true);
    try {
      final OrderRepository orderRepository = Get.find();
      await orderRepository.removeOldDeletedOrdersFromCache();
      await orderRepository.removeOldJoinedOrdersFromCache();
      await orderRepository.removeOldPaidOrJoinedOrdersFromCache();
      Get.showSnackbar(
        const GetSnackBar(
            message: 'Pedidos antigos removidos com sucesso',
            duration: Duration(seconds: 5)),
      );
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
          message: 'Erro ao remover pedidos antigos: $e',
          backgroundColor: Colors.red));
    } finally {
      isLoading(false);
    }
  }

  forceUpdateProducts() async {
    isLoading(true);
    try {
      if (await Get.find<ConnectionService>().checkConnection() == false) {
        Get.showSnackbar(const GetSnackBar(
            message: 'Sem conexão para atualizar os produtos'));
        return;
      }
      final ProductRepository productRepository = Get.find();
      await productRepository.updateCache();
      Get.showSnackbar(
          const GetSnackBar(message: 'Produtos atualizados com sucesso'));
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
          message: 'Erro ao atualizar os produtos: $e',
          backgroundColor: Colors.red));
    } finally {
      isLoading(false);
    }
  }

  void selectInitialFinalDay() {
    Get.dialog(
      AlertDialog(
        title: const Text('Selecione o dia'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(
              29,
              (index) => ListTile(
                title: Text('${index + 1}'),
                onTap: () {
                  initialFinalDay.value = index + 1;
                  _settingsService.setInitialFinalDay(index + 1);
                  Get.back();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  void goToSyncDataOffline() {
    Get.toNamed('/offline_sync');
  }
}
