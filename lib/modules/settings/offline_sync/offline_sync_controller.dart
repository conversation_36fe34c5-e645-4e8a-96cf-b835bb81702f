import 'dart:developer';

import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
import 'package:get/get.dart';
import 'package:light_flutter_nearby_connections/light_flutter_nearby_connections.dart';

class OfflineSyncController extends GetxController {
  RxBool server = false.obs;
  RxBool sender = false.obs;

  OfflineSyncRepositoryImpl offlineSyncRepository;

  OfflineSyncController({required this.offlineSyncRepository}) {
    offlineSyncRepository.getDeviceType() == DeviceType.browser
        ? server.value = true
        : sender.value = true;
    log('OfflineSyncController: ${offlineSyncRepository.getDeviceType()}');
  }

  void setServer() {
    server.value = true;
    sender.value = false;
    offlineSyncRepository.setDeviceType(DeviceType.browser);
  }

  void setSender() {
    server.value = false;
    sender.value = true;
    offlineSyncRepository.setDeviceType(DeviceType.advertiser);
  }

  void startServer() {
    offlineSyncRepository.startServer();
  }

  void startSender() {
    offlineSyncRepository.startSender();
  }

  int getCountOrders() {
    return offlineSyncRepository.ordersToSync.length;
  }

  int getCountProducts() {
    return offlineSyncRepository.productsToSync.length;
  }

  int getCountClients() {
    return offlineSyncRepository.clientsToSync.length;
  }

  void toggleEnable() {
    offlineSyncRepository.setEnable(!offlineSyncRepository.isEnable.value);
    log('Offline Sync ${offlineSyncRepository.isEnable.value ? 'Enabled' : 'Disabled'}');
  }

  void handleDeviceButtonClick(Device device) {
    switch (device.state) {
      case SessionState.notConnected:
        offlineSyncRepository.nearbyService?.invitePeer(
          deviceID: device.deviceId,
          deviceName: device.deviceName,
        );
        offlineSyncRepository.addToSavedConnectedDevices(device.deviceName);
        break;
      case SessionState.connected:
        offlineSyncRepository.nearbyService
            ?.disconnectPeer(deviceID: device.deviceId);
        break;
      case SessionState.connecting:
        // Optionally handle connecting state
        break;
    }
  }
}
