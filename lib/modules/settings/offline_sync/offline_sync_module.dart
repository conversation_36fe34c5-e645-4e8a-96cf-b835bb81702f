import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/settings/offline_sync/offline_sync_bindings.dart';
import 'package:fl_app/modules/settings/offline_sync/offline_sync_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class OfflineSyncModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/offline_sync',
      page: () => const OfflineSyncPage(),
      binding: OfflineSyncBindings(),
    ),
  ];
}
