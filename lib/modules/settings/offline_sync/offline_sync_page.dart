import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:light_flutter_nearby_connections/light_flutter_nearby_connections.dart';
import 'offline_sync_controller.dart';

class OfflineSyncPage extends GetView<OfflineSyncController> {
  const OfflineSyncPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 16),
                _buildEnableToggle(),
                if (controller.offlineSyncRepository.isEnable.value)
                  Expanded(child: _buildSyncOptions()),
              ],
            )),
      ),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      title: const Text('Sincronização Offline'),
      actions: [
        IconButton(
          icon: const Icon(Icons.delete),
          tooltip: 'Limpar Dispositivos Salvos',
          onPressed:
              controller.offlineSyncRepository.clearSavedConnectedDevices,
        )
      ],
    );
  }

  Widget _buildEnableToggle() {
    return Obx(
      () => SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: controller.toggleEnable,
          style: ElevatedButton.styleFrom(
            backgroundColor: controller.offlineSyncRepository.isEnable.value
                ? Colors.green
                : Colors.red,
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: Text(
            controller.offlineSyncRepository.isEnable.value
                ? 'Habilitado'
                : 'Desabilitado',
            style: const TextStyle(fontSize: 16, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildSyncOptions() {
    return Column(
      children: [
        Card(
          child: Column(
            children: [
              const SizedBox(height: 16),
              _buildStatusIndicators(),
              const SizedBox(height: 16),
              _buildModeToggle(),
              const SizedBox(height: 16),
              _buildActionButtons(),
              const SizedBox(height: 16),
            ],
          ),
        ),
        if (controller.server.value || controller.sender.value)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        children: [
                          const Text('Pedidos'),
                          Text('${controller.getCountOrders()}'),
                        ],
                      ),
                    ),
                    Container(
                      height: 50,
                      width: 1,
                      color: Get.theme.dividerColor.withOpacity(0.3),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        children: [
                          const Text('Produtos'),
                          Text('${controller.getCountProducts()}'),
                        ],
                      ),
                    ),
                    Container(
                      height: 50,
                      width: 1,
                      color: Get.theme.dividerColor.withOpacity(0.3),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        children: [
                          const Text('Clientes'),
                          Text('${controller.getCountClients()}'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        Expanded(child: _buildDeviceList()),
      ],
    );
  }

  Widget _buildStatusIndicators() {
    return Obx(() => Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _StatusIndicator(
              label: 'Navegando',
              isActive: controller.offlineSyncRepository.isBrowsing.value,
              activeColor: Colors.blue,
            ),
            _StatusIndicator(
              label: 'Anunciando',
              isActive: controller.offlineSyncRepository.isAdvertising.value,
              activeColor: Colors.orange,
            ),
          ],
        ));
  }

  Widget _buildModeToggle() {
    return Obx(() => ToggleButtons(
          borderRadius: BorderRadius.circular(8),
          constraints:
              BoxConstraints(minWidth: (Get.width / 2) - 24, minHeight: 50),
          isSelected: [controller.server.value, controller.sender.value],
          onPressed: (index) {
            if (index == 0) {
              controller.setServer();
            } else {
              controller.setSender();
            }
          },
          children: const [
            _ToggleButton(label: 'Receber', icon: FontAwesomeIcons.download),
            _ToggleButton(label: 'Enviar', icon: FontAwesomeIcons.upload),
          ],
        ));
  }

  Widget _buildActionButtons() {
    return Obx(() {
      List<Widget> buttons = [];
      if (controller.server.value) {
        buttons.add(_ActionButton(
          label: controller.offlineSyncRepository.isBrowsing.value
              ? 'Parar Servidor'
              : 'Iniciar Servidor',
          icon: controller.offlineSyncRepository.isBrowsing.value
              ? Icons.stop
              : Icons.play_arrow,
          color: controller.offlineSyncRepository.isBrowsing.value
              ? Colors.red
              : Colors.green,
          onPressed: controller.offlineSyncRepository.isBrowsing.value
              ? controller.offlineSyncRepository.stopServer
              : controller.startServer,
        ));
      }

      if (controller.sender.value) {
        buttons.add(_ActionButton(
          label: controller.offlineSyncRepository.isAdvertising.value
              ? 'Parar Envio'
              : 'Iniciar Envio',
          icon: controller.offlineSyncRepository.isAdvertising.value
              ? Icons.stop
              : Icons.send,
          color: controller.offlineSyncRepository.isAdvertising.value
              ? Colors.red
              : Colors.green,
          onPressed: controller.offlineSyncRepository.isAdvertising.value
              ? controller.offlineSyncRepository.stopSender
              : controller.startSender,
        ));
      }

      return Wrap(
        spacing: 16,
        children: buttons,
      );
    });
  }

  Widget _buildDeviceList() {
    return Obx(() {
      if (controller.server.value) {
        return _DeviceList(
          devices: controller.offlineSyncRepository.devices,
          onButtonClicked: controller.handleDeviceButtonClick,
        );
      } else if (controller.sender.value) {
        return _DeviceList(
          devices: controller.offlineSyncRepository.connectedDevices,
          onButtonClicked: controller.handleDeviceButtonClick,
        );
      } else {
        return const Center(
            child: Text('Selecione um modo para sincronização.'));
      }
    });
  }
}

class _StatusIndicator extends StatelessWidget {
  final String label;
  final bool isActive;
  final Color activeColor;

  const _StatusIndicator({
    required this.label,
    required this.isActive,
    required this.activeColor,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          isActive
              ? FontAwesomeIcons.circleCheck
              : FontAwesomeIcons.circleXmark,
          color: isActive ? activeColor : Colors.grey,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(label,
            style: TextStyle(color: isActive ? activeColor : Colors.grey)),
      ],
    );
  }
}

class _ToggleButton extends StatelessWidget {
  final String label;
  final IconData icon;

  const _ToggleButton({required this.label, required this.icon});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 8),
        Icon(icon, size: 20),
        const SizedBox(height: 4),
        Text(label),
        const SizedBox(height: 4),
      ],
    );
  }
}

class _ActionButton extends StatelessWidget {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onPressed;

  const _ActionButton({
    required this.label,
    required this.icon,
    required this.color,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      icon: Icon(icon, color: Colors.white),
      label: Text(
        label,
        style: const TextStyle(color: Colors.white),
      ),
    );
  }
}

class _DeviceList extends StatelessWidget {
  final List<Device> devices;
  final Function(Device) onButtonClicked;

  const _DeviceList({
    required this.devices,
    required this.onButtonClicked,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (devices.isEmpty) {
        return const Center(child: Text('Nenhum dispositivo encontrado.'));
      }

      return Card(
        child: ListView.separated(
          itemCount: devices.length,
          separatorBuilder: (context, index) =>
              const Divider(color: Colors.grey),
          itemBuilder: (context, index) {
            final device = devices[index];
            return _DeviceListItem(
              device: device,
              onButtonClicked: onButtonClicked,
            );
          },
        ),
      );
    });
  }
}

class _DeviceListItem extends StatelessWidget {
  final Device device;
  final Function(Device) onButtonClicked;

  const _DeviceListItem({
    required this.device,
    required this.onButtonClicked,
  });

  String _getStateName(SessionState state) {
    switch (state) {
      case SessionState.notConnected:
        return "Desconectado";
      case SessionState.connecting:
        return "Conectando...";
      default:
        return "Conectado";
    }
  }

  Color _getStateColor(SessionState state) {
    switch (state) {
      case SessionState.notConnected:
        return Colors.red;
      case SessionState.connecting:
        return Colors.orange;
      default:
        return Colors.green;
    }
  }

  String _getButtonLabel(SessionState state) {
    switch (state) {
      case SessionState.notConnected:
      case SessionState.connecting:
        return "Conectar";
      default:
        return "Desconectar";
    }
  }

  Color _getButtonColor(SessionState state) {
    switch (state) {
      case SessionState.notConnected:
      case SessionState.connecting:
        return Colors.green;
      default:
        return Colors.red;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(device.deviceName),
      subtitle: Text(
        _getStateName(device.state),
        style: TextStyle(color: _getStateColor(device.state)),
      ),
      trailing: ElevatedButton(
        onPressed: () => onButtonClicked(device),
        style: ElevatedButton.styleFrom(
          backgroundColor: _getButtonColor(device.state),
        ),
        child: Text(
          _getButtonLabel(device.state),
          style: const TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}
