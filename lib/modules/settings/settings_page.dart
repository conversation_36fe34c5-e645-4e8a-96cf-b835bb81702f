import 'package:fl_app/services/settings/settings_service.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './settings_controller.dart';

class SettingsPage extends GetView<SettingsController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configurações'),
      ),
      body: SingleChildScrollView(
        child: Obx(
          () => controller.isLoading.value
              ? const SizedBox(
                  height: 100,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          strokeCap: StrokeCap.round,
                        ),
                      ],
                    ),
                  ),
                )
              : Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Card(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          width: double.infinity,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: <Widget>[
                              if (controller.isShorebirdAvailable)
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Text(
                                      'Versão do Aplicativo: ',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text(
                                      '${controller.releaseVersion} #${controller.currentPatchVersion.value?.number ?? 0}',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red,
                                      ),
                                    ),
                                  ],
                                ),
                              if (!controller.isShorebirdAvailable)
                                const Text('Atualizações não disponíveis'),
                              const SizedBox(height: 8),
                              if (controller.isShorebirdAvailable)
                                Row(
                                  children: [
                                    Expanded(
                                      child: ElevatedButton(
                                        onPressed:
                                            controller.isCheckingForUpdate.value
                                                ? null
                                                : controller.checkForUpdate,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.indigo,
                                          foregroundColor: Colors.white,
                                        ),
                                        child: controller
                                                .isCheckingForUpdate.value
                                            ? const CircularProgressIndicator(
                                                strokeCap: StrokeCap.round,
                                              )
                                            : const Text(
                                                'Verificar atualizações'),
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    // SwitchListTile light/dark theme
                    SwitchListTile(
                      title: const Text('Tema Escuro'),
                      value: controller.isDarkMode.value,
                      onChanged: (value) {
                        controller.isDarkMode.toggle();
                        Get.changeThemeMode(
                          Get.isDarkMode ? ThemeMode.light : ThemeMode.dark,
                        );
                        Get.find<SettingsService>().togleDarkMode();
                      },
                    ),

                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Obx(
                        () => Row(
                          children: [
                            const Text(
                              'Dia de Início/Fim do Mês:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.indigo[50],
                                foregroundColor: Colors.indigo,
                                textStyle: const TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.bold),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              onPressed: () {
                                controller.selectInitialFinalDay();
                              },
                              child: Text(
                                  controller.initialFinalDay.value.toString()),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (controller.user.value?.admin ?? false)
                      ListTile(
                        leading: const Icon(Icons.lock),
                        title: const Text('Alterar senha de administrador'),
                        onTap: () => controller.changeAdminPassword(),
                      ),
                    ListTile(
                      leading: const Icon(Icons.sync),
                      title: const Text('Forçar atualização de Clientes'),
                      onTap: () => controller.forceUpdateClients(),
                    ),
                    ListTile(
                      leading: const Icon(Icons.sync),
                      title: const Text('Forçar atualização de Pedidos'),
                      onTap: () => controller.forceUpdateOrders(),
                    ),
                    ListTile(
                      leading: const Icon(Icons.sync),
                      title: const Text('Forçar atualização de Produtos'),
                      onTap: () => controller.forceUpdateProducts(),
                    ),
                    ListTile(
                      leading: const Icon(Icons.offline_share_rounded),
                      title: const Text('Sincronizar Dados Offline'),
                      onTap: () => controller.goToSyncDataOffline(),
                    ),
                    ListTile(
                      leading: const Icon(Icons.delete),
                      title: const Text('Remover Pedidos Antigos do Cache'),
                      onTap: () => controller.removeOldOrdersFromCache(),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}
