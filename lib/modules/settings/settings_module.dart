import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/settings/offline_sync/offline_sync_module.dart';
import 'package:fl_app/modules/settings/settings_bindings.dart';
import 'package:fl_app/modules/settings/settings_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SettingsModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/settings',
      page: () => const SettingsPage(),
      binding: SettingsBindings(),
    ),
    ...OfflineSyncModule().routers,
  ];
}
