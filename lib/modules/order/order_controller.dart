import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum PaymentFilterType {
  all, // Todos os pedidos
  paid, // Apenas pagos
  unpaid // Apenas não pagos
}

class OrderController extends GetxController {
  final OrderRepository _orderRepository;
  final SalesRoutesService _salesRoutesService;
  final UserService _userService;

  OrderController(
      {required OrderRepository orderRepository,
      required UserService userService,
      required SalesRoutesService salesRoutesService})
      : _orderRepository = orderRepository,
        _userService = userService,
        _salesRoutesService = salesRoutesService;

  final RxList<OrderModel> orders = <OrderModel>[].obs;
  final RxList<OrderModel> filteredOrders = <OrderModel>[].obs;

  final isLoading = false.obs;

  String? rotaId = Get.arguments;
  String? rotaName = '';

  Rx<DateTime> initialDate =
      DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .subtract(const Duration(days: 30))
          .obs;
  Rx<DateTime> finalDate =
      DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .add(const Duration(days: 1))
          .obs;
  Rx<DateTime> selectedDate =
      DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day)
          .obs;
  Rx<bool> isDateRange = false.obs;

  RxString routeId = RxString("");
  RxString sallerId = RxString("");

  RxBool showToDelivery = false.obs;
  Rx<PaymentFilterType> paymentFilter = PaymentFilterType.all.obs;
  RxBool orderAscending = false.obs;

  RxString filterText = "".obs;

  List<SaleRouteModel> routes = [];
  List<UserModel> users = [];

  RxBool isFiltering = false.obs;

  RxString search = ''.obs;

  TextEditingController searchController = TextEditingController();

  final workers = <Worker>[].obs;

  FocusNode searchFocus = FocusNode();

  @override
  void onInit() {
    super.onInit();

    if (rotaId != null && rotaId!.isNotEmpty) {
      var route = _salesRoutesService.getRouteById(rotaId!);
      rotaName = route?.name ?? 'Rota não encontrada';
      // Se a página foi aberta com uma rota específica, pré-selecionar no filtro
      routeId.value = rotaId!;
    }
    refreshOrders();
    refreshRoutes();
    refreshUsers();
    _userService.getUsersFromCache().then((users) {
      this.users = users;
    });
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchOrder(),
        time: const Duration(milliseconds: 500),
      ),
    );
    super.onReady();
  }

  Future<void> refreshOrders() async {
    isLoading(true);
    await _orderRepository
        .getOrdersFromCache(ascending: false, routeId: rotaId)
        .then((orders) {
      this.orders.assignAll(orders);

      // Se há filtros aplicados, reaplicar os filtros
      if (isFiltering.value) {
        _applyCurrentFilters();
      } else {
        filteredOrders.assignAll(orders);
      }

      if (search.isNotEmpty) {
        searchOrder();
      }
    });
    isLoading(false);
  }

  Future<void> refreshRoutes() async {
    _salesRoutesService.getSalesRoutesFromCache().then((routes) {
      this.routes = routes;
    });
  }

  Future<void> refreshUsers() async {
    _userService.getUsers().then((users) {
      this.users = users;
    });
  }

  /// Reaplica filtros e busca após navegação
  Future<void> _reapplyFiltersAndSearch() async {
    await refreshOrders();
    // O refreshOrders() já reaplica os filtros se isFiltering.value == true
    // Então não precisamos chamar filterOrders() novamente aqui
    if (search.isNotEmpty) {
      searchOrder();
    }
  }

  void goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    await _reapplyFiltersAndSearch();
  }

  void filterOrders() async {
    isLoading(true);
    await _applyCurrentFilters();
    isFiltering(true);
    setFilterText();
    isLoading(false);
  }

  Future<void> _applyCurrentFilters() async {
    bool? showPaidOrders;
    switch (paymentFilter.value) {
      case PaymentFilterType.paid:
        showPaidOrders = true;
        break;
      case PaymentFilterType.unpaid:
        showPaidOrders = false;
        break;
      case PaymentFilterType.all:
        showPaidOrders = null;
        break;
    }

    await _orderRepository
        .getOrdersFromCache(
      ascending: orderAscending.value,
      finalDate: isDateRange.value
          ? finalDate.value.add(const Duration(days: 1))
          : selectedDate.value.add(const Duration(days: 1)),
      initialDate: isDateRange.value ? initialDate.value : selectedDate.value,
      routeId: routeId.value,
      showPaidOrders: showPaidOrders,
      showToDelivery: showToDelivery.value ? true : null,
      sellerId: sallerId.value,
    )
        .then((orders) {
      filteredOrders.assignAll(orders);
    });
  }

  void clearFilters() {
    routeId.value = "";
    sallerId.value = "";
    showToDelivery.value = false;
    paymentFilter.value = PaymentFilterType.all;
    isDateRange.value = false;
    selectedDate.value = DateTime.now();
    initialDate.value = DateTime.now().subtract(const Duration(days: 30));
    finalDate.value = DateTime.now();
    isFiltering.value = false;

    refreshOrders();
  }

  void setFilterText() {
    String text = "";
    if (routeId.value != "") {
      text += routes.firstWhere((route) => route.id == routeId.value).name;
    }
    if (text != "" && sallerId.value != "") {
      text += ", ";
    }
    if (sallerId.value != "") {
      text += users.firstWhere((user) => user.id == sallerId.value).name;
    }
    if (text != "" && showToDelivery.value && text[text.length - 1] != ", ") {
      text += ", ";
    }
    if (showToDelivery.value) {
      text += "Para entrega";
    }
    if (text != "" &&
        paymentFilter.value != PaymentFilterType.all &&
        text[text.length - 1] != ", ") {
      text += ", ";
    }
    if (paymentFilter.value == PaymentFilterType.paid) {
      text += "Pagos";
    } else if (paymentFilter.value == PaymentFilterType.unpaid) {
      text += "Pendentes";
    }
    if (isDateRange.value) {
      if (text != "" && text[text.length - 1] != ", ") {
        text += ", ";
      }
      text +=
          "${DateTimeHelper.getFormattedDate(initialDate.value)} até ${DateTimeHelper.getFormattedDate(finalDate.value)}";
    } else {
      if (text != "" && text[text.length - 1] != ", ") {
        text += ", ";
      }
      var data = DateTimeHelper.getFormattedDate(selectedDate.value);
      if (data == DateTimeHelper.getFormattedDate(DateTime.now())) {
        text += "Hoje";
      } else {
        text += data;
      }
    }
    if (text == "") {
      text = "Sem filtros";
    } else {
      text = "$text.";
    }

    filterText.value = text;
  }

  clearSearch() {
    search.value = '';
    filteredOrders.assignAll(orders);
    searchController.clear();
    searchFocus.unfocus();
  }

  searchOrder() {
    if (search.isEmpty) {
      filteredOrders.assignAll(orders);
      return;
    }
    filteredOrders.assignAll(
      orders.where((order) =>
          TextHelper.removeAcento(order.clientName.toLowerCase())
              .contains(TextHelper.removeAcento(search.toLowerCase())) ||
          TextHelper.removeAcento(order.clientAddress.toLowerCase())
              .contains(TextHelper.removeAcento(search.toLowerCase())) ||
          TextHelper.removeAcento(order.clientLocalDescription.toLowerCase())
              .contains(TextHelper.removeAcento(search.toLowerCase()))),
    );
  }

  void goToDeletedOrders() async {
    await Get.toNamed('/deleted_orders', arguments: rotaId);
    await _reapplyFiltersAndSearch();
  }
}
