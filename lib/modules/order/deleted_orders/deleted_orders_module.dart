import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/deleted_orders/deleted_orders_bindings.dart';
import 'package:fl_app/modules/order/deleted_orders/deleted_orders_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class DeletedOrdersModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/deleted_orders',
      page: () => const DeletedOrdersPage(),
      binding: DeletedOrdersBindings(),
    ),
  ];
}
