import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DeletedOrdersController extends GetxController {
  final OrderRepository _orderRepository;

  DeletedOrdersController({required OrderRepository orderRepository})
      : _orderRepository = orderRepository;

  final RxList<OrderModel> orders = <OrderModel>[].obs;
  final RxList<OrderModel> filteredOrders = <OrderModel>[].obs;

  final isLoading = false.obs;

  String? rotaId = Get.arguments;
  String? rotaName = '';

  RxString routeId = RxString("");
  RxString search = ''.obs;

  TextEditingController searchController = TextEditingController();

  final workers = <Worker>[].obs;

  FocusNode searchFocus = FocusNode();

  @override
  void onInit() {
    super.onInit();

    if (rotaId != null && rotaId!.isNotEmpty) {
      var route = Get.find<SalesRoutesService>().getRouteById(rotaId!);
      rotaName = route?.name ?? 'Rota não encontrada';
    }
    refreshOrders();
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchOrder(),
        time: const Duration(milliseconds: 500),
      ),
    );
    super.onReady();
  }

  Future<void> refreshOrders() async {
    isLoading(true);
    await _orderRepository
        .getDeletedOrdersFromCache(routeId: rotaId)
        .then((orders) {
      this.orders.assignAll(orders);
      filteredOrders.assignAll(orders);
      if (search.isNotEmpty) {
        searchOrder();
      }
    });
    isLoading(false);
  }

  searchOrder() {
    if (search.isEmpty) {
      filteredOrders.assignAll(orders);
      return;
    }
    filteredOrders.assignAll(
      orders.where((order) =>
          TextHelper.removeAcento(order.clientName.toLowerCase())
              .contains(TextHelper.removeAcento(search.toLowerCase())) ||
          TextHelper.removeAcento(order.clientAddress.toLowerCase())
              .contains(TextHelper.removeAcento(search.toLowerCase())) ||
          TextHelper.removeAcento(order.clientLocalDescription.toLowerCase())
              .contains(TextHelper.removeAcento(search.toLowerCase()))),
    );
  }

  void goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    await refreshOrders();
  }

  clearSearch() {
    search.value = '';
    filteredOrders.assignAll(orders);
    searchController.clear();
    searchFocus.unfocus();
  }
}
