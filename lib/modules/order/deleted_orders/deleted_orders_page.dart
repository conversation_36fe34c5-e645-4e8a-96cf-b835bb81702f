import 'package:fl_app/application/ui/widgets/order_list_card.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './deleted_orders_controller.dart';

class DeletedOrdersPage extends GetView<DeletedOrdersController> {
  const DeletedOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        centerTitle: true,
        title: Obx(
          () => Column(
            children: [
              Text(
                'Pedidos apagados (${controller.filteredOrders.length})',
              ),
              if (controller.rotaId != null)
                Text(
                  '${controller.rotaName}',
                  style: const TextStyle(fontSize: 16),
                ),
            ],
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Obx(() {
          final orders = controller.filteredOrders;

          return Column(
            children: [
              Obx(
                () => Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  child: SearchBar(
                    controller: controller.searchController,
                    hintText: 'Pesquisar pedidos apagados',
                    leading: const Icon(Icons.search),
                    onChanged: (value) => controller.search(value),
                    elevation: WidgetStateProperty.all(2),
                    trailing: [
                      if (controller.search.isNotEmpty)
                        IconButton(
                          onPressed: () => controller.clearSearch(),
                          icon: const Icon(Icons.clear),
                        ),
                    ],
                    shape: WidgetStateProperty.all(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    focusNode: controller.searchFocus,
                  ),
                ),
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    if (orders.isEmpty && controller.isLoading.value)
                      const Center(
                        child: CircularProgressIndicator(
                          strokeCap: StrokeCap.round,
                        ),
                      ),
                    if (orders.isEmpty && !controller.isLoading.value)
                      const Center(
                        child: Text('Nenhum pedido apagado encontrado'),
                      ),
                    if (orders.isNotEmpty && !controller.isLoading.value)
                      Expanded(
                        child: RefreshIndicator(
                          onRefresh: () async {
                            await controller.refreshOrders();
                          },
                          child: Scrollbar(
                            child: ListView.builder(
                              itemCount: orders.length,
                              itemBuilder: (context, index) {
                                final order = orders[index];
                                return OrderListCard(
                                  order: order,
                                  onTap: controller.goToOrderDetails,
                                  showDaysFromNowLeading: false,
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
}
