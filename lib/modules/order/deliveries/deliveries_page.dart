import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './deliveries_controller.dart';

class DeliveriesPage extends GetView<DeliveriesController> {
  const DeliveriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Entregas',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          Obx(
            () => controller.user.value != null && !controller.user.value!.admin
                ? ChoiceChip(
                    label: const Text('Minhas rotas'),
                    selected: controller.showOnlyMyRoutes.value,
                    onSelected: (value) {
                      controller.showOnlyMyRoutes.value = value;
                      controller.refreshOrdersDelivery();
                    },
                  )
                : const SizedBox(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              await controller.refreshOrdersDelivery();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Text('Entregas atualizadas'),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
          ),
        ],
      ),
      body: Obx(
        () {
          if (controller.loading.value) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (controller.ordersDelivery.isEmpty) {
            return Center(
              child: Text(
                'Nenhuma entrega pendente',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            );
          }

          return ListView(
            padding: const EdgeInsets.all(8),
            children: controller.ordersDelivery.keys.map((route) {
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                elevation: 4,
                shadowColor:
                    Theme.of(context).colorScheme.primary.withOpacity(0.4),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ExpansionTile(
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  collapsedBackgroundColor:
                      Theme.of(context).colorScheme.surfaceContainerHighest,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(
                      color: Theme.of(context).colorScheme.primaryContainer,
                      width: 1,
                    ),
                  ),
                  title: Text(
                    route.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  children: controller.ordersDelivery[route]!
                      .map(
                        (order) => Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 0),
                          child: Card(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            elevation: 1,
                            child: ListTile(
                              leading: CircleAvatar(
                                backgroundColor: Theme.of(context)
                                    .colorScheme
                                    .primaryContainer,
                                child: const Icon(
                                  Icons.delivery_dining,
                                  color: Colors.white,
                                ),
                              ),
                              title: Text(
                                '${order.clientName}, ${order.clientAddress}',
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 4),
                                  if (order.toDelivery)
                                    ...order.products.map(
                                      (e) => Text(
                                        '${e.quantity}x ${e.name}',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium,
                                      ),
                                    ),
                                  if (!order.toDelivery)
                                    ...order.products
                                        .where((e) => e.quantityToDelivery > 0)
                                        .map(
                                          (e) => Text(
                                            '${e.quantityToDelivery}x ${e.name} ${e.deliveryNote.isNotEmpty ? '- (${e.deliveryNote})' : ''}',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodyMedium,
                                          ),
                                        ),
                                ],
                              ),
                              onTap: () => controller.goToOrderDetails(order),
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              );
            }).toList(),
          );
        },
      ),
    );
  }
}
