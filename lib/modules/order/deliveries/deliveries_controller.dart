import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';

class DeliveriesController extends GetxController {
  final OrderRepository _orderRepository;

  DeliveriesController({required OrderRepository orderRepository})
      : _orderRepository = orderRepository;

  RxMap<SaleRouteModel, List<OrderModel>> ordersDelivery =
      <SaleRouteModel, List<OrderModel>>{}.obs;

  RxList<String> myRoutes = <String>[].obs;

  RxBool showOnlyMyRoutes = false.obs;

  RxBool loading = false.obs;

  Rxn<UserModel> user = Rxn<UserModel>();

  @override
  void onInit() {
    var routes = Get.arguments;
    if (routes != null && (routes as List<String>).isNotEmpty) {
      myRoutes.addAll(routes);
      showOnlyMyRoutes(true);
    }
    refreshOrdersDelivery();
    init();
    super.onInit();
  }

  init() async {
    user.value = await Get.find<UserService>().getUserAuthenticated();
  }

  Future<void> refreshOrdersDelivery() async {
    loading.value = true;
    try {
      if (showOnlyMyRoutes.value && myRoutes.isNotEmpty) {
        Map<SaleRouteModel, List<OrderModel>> tempRoutesOrders =
            await _orderRepository.getOrdersDeliveryGroupedByRoute();
        // remove routes that are not in myRoutes
        tempRoutesOrders
            .removeWhere((key, value) => !myRoutes.contains(key.id));
        ordersDelivery.assignAll(tempRoutesOrders);
      } else {
        ordersDelivery.assignAll(
            await _orderRepository.getOrdersDeliveryGroupedByRoute());
      }
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
        title: 'Erro ao buscar entregas',
        message: e.toString(),
        duration: const Duration(seconds: 5),
      ));
    } finally {
      loading.value = false;
    }
  }

  goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    refreshOrdersDelivery();
  }
}
