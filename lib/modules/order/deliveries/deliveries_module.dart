import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/deliveries/deliveries_bindings.dart';
import 'package:fl_app/modules/order/deliveries/deliveries_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class DeliveriesModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/deliveries',
      page: () => const DeliveriesPage(),
      binding: DeliveriesBindings(),
    ),
  ];
}
