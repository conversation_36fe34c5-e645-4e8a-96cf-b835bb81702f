import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/deleted_orders/deleted_orders_module.dart';
import 'package:fl_app/modules/order/deliveries/deliveries_module.dart';
import 'package:fl_app/modules/order/order_bindings.dart';
import 'package:fl_app/modules/order/order_page.dart';
import 'package:fl_app/modules/order/sale_billing/sale_billing_module.dart';
import 'package:fl_app/modules/order/view_order/view_order_module.dart';
import 'package:get/get.dart';

class OrderModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/orders',
      page: () => const OrderPage(),
      binding: OrderBindings(),
    ),
    ...ViewOrderModule().routers,
    ...SaleBillingModule().routers,
    ...DeliveriesModule().routers,
    ...DeletedOrdersModule().routers,
  ];
}
