import 'package:action_slider/action_slider.dart';
import 'package:fl_app/application/helpers/date_time_helper.dart';

import 'package:fl_app/application/ui/widgets/custom_icon_button.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/modules/order/view_order/components/order_widget.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

import './view_order_controller.dart';

class ViewOrderPage extends GetView<ViewOrderController> {
  ViewOrderPage({super.key})
      : id = Get.arguments.id // Store the id from Get.arguments
  ;

  final String id;

  @override
  String get tag => id;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ObxValue(
          (order) {
            var isSameDay = DateTimeHelper.isSameDay(
              order.value.date,
              DateTime.now(),
            );
            var user = controller.user.value;
            var appState = controller.appState.value;

            return Scaffold(
              floatingActionButton: user == null || order.value.isDeleted
                  ? const SizedBox.shrink()
                  : FloatingActionButton(
                      onPressed: () {
                        Get.bottomSheet(
                          Container(
                            decoration: BoxDecoration(
                              color: Get.theme.colorScheme.surface,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(16),
                                topRight: Radius.circular(16),
                              ),
                              border: Border(
                                top: BorderSide(
                                  color: Get.theme.colorScheme.primary,
                                  width: 2,
                                ),
                              ),
                            ),
                            child: Wrap(
                              children: [
                                if (!order.value.isPaid &&
                                    !order.value.isJoined)
                                  Column(
                                    children: [
                                      if (((user.admin ||
                                          // cobrador fixo, autorizado na rota
                                          (user.cobrador &&
                                              user.cobradorFixo &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId)) ||
                                          // cobrador temporário, autorizado na rota e cobrando, independente do dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              appState.isCobrando) ||
                                          // cobrador temporário, autorizado na rota e não cobrando, no mesmo dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              !appState.isCobrando &&
                                              isSameDay) ||
                                          // qualquer usuario vendendo na rota
                                          (appState.isSelling &&
                                              appState.isSellingRouteId ==
                                                  controller
                                                      .order.value.routeId &&
                                              isSameDay))))
                                        ListTile(
                                          leading: const Icon(
                                              FontAwesomeIcons.calendar),
                                          title: const Text('Marcar dia'),
                                          onTap: () async {
                                            Get.back();
                                            await controller
                                                .showMarkDialog(context);
                                          },
                                        ),
                                      ListTile(
                                        leading: const Icon(
                                            FontAwesomeIcons.penToSquare),
                                        title: const Text('Editar Cliente'),
                                        onTap: () async {
                                          Get.back();
                                          controller.goToEditClient();
                                        },
                                      ),
                                      if ((user.admin ||
                                          // cobrador fixo, autorizado na rota
                                          (user.cobrador &&
                                              user.cobradorFixo &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId)) ||
                                          // cobrador temporário, autorizado na rota e cobrando, independente do dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              appState.isCobrando) ||
                                          // cobrador temporário, autorizado na rota e não cobrando, no mesmo dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              !appState.isCobrando &&
                                              isSameDay) ||
                                          // qualquer usuario vendendo na rota
                                          (appState.isSelling &&
                                              appState.isSellingRouteId ==
                                                  controller
                                                      .order.value.routeId &&
                                              isSameDay)))
                                        ListTile(
                                          leading: const Icon(FontAwesomeIcons
                                              .handHoldingDollar),
                                          title:
                                              const Text('Descontar Dinheiro'),
                                          onTap: () async {
                                            Get.back();
                                            await controller
                                                .showDiscountDialog(context);
                                          },
                                        ),
                                      const Divider(
                                        height: 0,
                                      ),
                                      if ((user.admin ||
                                          // cobrador fixo, autorizado na rota
                                          (user.cobrador &&
                                              user.cobradorFixo &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId)) ||
                                          // cobrador temporário, autorizado na rota e cobrando, independente do dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              appState.isCobrando) ||
                                          // cobrador temporário, autorizado na rota e não cobrando, no mesmo dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              !appState.isCobrando &&
                                              isSameDay) ||
                                          // qualquer usuario vendendo na rota
                                          (appState.isSelling &&
                                              appState.isSellingRouteId ==
                                                  controller
                                                      .order.value.routeId &&
                                              isSameDay)))
                                        Container(
                                          color: Get.isDarkMode
                                              ? const Color.fromARGB(
                                                  255, 163, 57, 0)
                                              : Colors.orange[50],
                                          child: ListTile(
                                            leading: Icon(
                                              FontAwesomeIcons.fileImport,
                                              color: Get.isDarkMode
                                                  ? Colors.orange[50]
                                                  : Colors.orange[900],
                                            ),
                                            title: Text(
                                              'Juntar com outro pedido',
                                              style: TextStyle(
                                                color: Get.isDarkMode
                                                    ? Colors.orange[50]
                                                    : Colors.orange[900],
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            onTap: () async {
                                              Get.back();
                                              await controller
                                                  .showJoinedDialog(context);
                                            },
                                          ),
                                        ),
                                      if ((user.admin ||
                                          // cobrador fixo, autorizado na rota
                                          (user.cobrador &&
                                              user.cobradorFixo &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId)) ||
                                          // cobrador temporário, autorizado na rota e cobrando, independente do dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              appState.isCobrando) ||
                                          // cobrador temporário, autorizado na rota e não cobrando, no mesmo dia
                                          (user.cobrador &&
                                              user.cobradorTemporario &&
                                              user.authorizedBillingRoutes
                                                  .contains(controller
                                                      .order.value.routeId) &&
                                              !appState.isCobrando &&
                                              isSameDay) ||
                                          // qualquer usuario vendendo na rota
                                          (appState.isSelling &&
                                              appState.isSellingRouteId ==
                                                  controller
                                                      .order.value.routeId &&
                                              isSameDay)))
                                        Container(
                                          color: Get.isDarkMode
                                              ? Colors.green[900]
                                              : Colors.green[50],
                                          child: ListTile(
                                            leading: Icon(
                                              Icons.check,
                                              color: Get.isDarkMode
                                                  ? Colors.green[50]
                                                  : Colors.green[900],
                                            ),
                                            title: Text(
                                              'Marcar como pago',
                                              style: TextStyle(
                                                color: Get.isDarkMode
                                                    ? Colors.green[50]
                                                    : Colors.green[900],
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            onTap: () async {
                                              Get.back();
                                              await controller
                                                  .showPaidDialog(context);
                                            },
                                          ),
                                        ),
                                    ],
                                  ),
                                if (order.value.isPaid &&
                                    (user.admin ||
                                        // cobrador fixo, autorizado na rota
                                        (user.cobrador &&
                                            user.cobradorFixo &&
                                            user.authorizedBillingRoutes
                                                .contains(controller
                                                    .order.value.routeId)) ||
                                        //TODO: cobrador temporário, autorizado na rota e marcado como pago no mesmo dia
                                        // qualquer usuario vendendo na rota
                                        (appState.isSelling &&
                                            appState.isSellingRouteId ==
                                                controller
                                                    .order.value.routeId &&
                                            isSameDay)))
                                  ListTile(
                                    leading: const Icon(
                                      Icons.close,
                                      color: Colors.red,
                                    ),
                                    title: const Text(
                                      'Desmarcar como pago',
                                      style: TextStyle(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    onTap: () async {
                                      Get.back();
                                      await controller
                                          .showUnpaidDialog(context);
                                    },
                                  ),
                                if (order.value.isJoined &&
                                    (user.admin ||
                                        // cobrador fixo, autorizado na rota
                                        (user.cobrador &&
                                            user.cobradorFixo &&
                                            user.authorizedBillingRoutes
                                                .contains(controller
                                                    .order.value.routeId)) ||
                                        // cobrador temporário, autorizado na rota e cobrando, independente do dia
                                        (user.cobrador &&
                                            user.cobradorTemporario &&
                                            user.authorizedBillingRoutes
                                                .contains(controller
                                                    .order.value.routeId) &&
                                            appState.isCobrando) ||
                                        // cobrador temporário, autorizado na rota e não cobrando, no mesmo dia
                                        (user.cobrador &&
                                            user.cobradorTemporario &&
                                            user.authorizedBillingRoutes
                                                .contains(controller
                                                    .order.value.routeId) &&
                                            !appState.isCobrando &&
                                            isSameDay) ||
                                        // qualquer usuario vendendo na rota
                                        (appState.isSelling &&
                                            appState.isSellingRouteId ==
                                                controller
                                                    .order.value.routeId &&
                                            isSameDay)))
                                  ListTile(
                                    leading: const Icon(
                                      Icons.close,
                                      color: Colors.red,
                                    ),
                                    title: const Text(
                                      'Desmarcar como juntado',
                                      style: TextStyle(
                                        color: Colors.red,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    onTap: () async {
                                      Get.back();
                                      await controller
                                          .showUnjoinedDialog(context);
                                    },
                                  ),
                              ],
                            ),
                          ),
                        );
                      },
                      child: const Icon(Icons.more_horiz_rounded),
                    ),
              appBar: AppBar(
                actions: order.value.isDeleted
                    ? []
                    : [
                        //Editar
                        if (user != null &&
                            (user.admin ||
                                // cobrador fixo, autorizado na rota
                                (user.cobrador &&
                                    user.cobradorFixo &&
                                    user.authorizedBillingRoutes
                                        .contains(order.value.routeId)) ||
                                // cobrador temporário, autorizado na rota e no mesmo dia
                                (user.cobrador &&
                                    user.cobradorTemporario &&
                                    user.authorizedBillingRoutes
                                        .contains(order.value.routeId) &&
                                    DateTimeHelper.isSameDay(
                                      order.value.date,
                                      DateTime.now(),
                                    )) ||
                                // qualquer usuario vendendo na rota
                                (appState.isSelling &&
                                    appState.isSellingRouteId ==
                                        order.value.routeId &&
                                    DateTimeHelper.isSameDay(
                                      order.value.date,
                                      DateTime.now(),
                                    ))))
                          CustomIconButton(
                            iconData: FontAwesomeIcons.penToSquare,
                            onTap: () {
                              controller.goToEditOrder();
                            },
                          ),

                        //APAGAR
                        if (user != null &&
                            (user.admin ||
                                // cobrador fixo, autorizado na rota
                                (user.cobrador &&
                                    user.cobradorFixo &&
                                    user.authorizedBillingRoutes
                                        .contains(order.value.routeId)) ||
                                // cobrador temporário, autorizado na rota e no mesmo dia
                                (user.cobrador &&
                                    user.cobradorTemporario &&
                                    user.authorizedBillingRoutes
                                        .contains(order.value.routeId) &&
                                    DateTimeHelper.isSameDay(
                                      order.value.date,
                                      DateTime.now(),
                                    )) ||
                                // qualquer usuario vendendo na rota
                                (appState.isSelling &&
                                    appState.isSellingRouteId ==
                                        order.value.routeId &&
                                    DateTimeHelper.isSameDay(
                                      order.value.date,
                                      DateTime.now(),
                                    ))))
                          CustomIconButton(
                            iconData: FontAwesomeIcons.trash,
                            onTap: () {
                              showDialog(
                                  context: context,
                                  builder: (ctx) {
                                    return AlertDialog(
                                      title: const Text('Apagar'),
                                      content: const Text(
                                          'Tem certeza que deseja apagar esse pedido?'),
                                      actions: [
                                        TextButton(
                                          child: const Text(
                                            'Apagar',
                                            style: TextStyle(color: Colors.red),
                                          ),
                                          onPressed: () {
                                            Get.back(result: true);
                                          },
                                        ),
                                        ElevatedButton(
                                          child: const Text('Cancelar'),
                                          onPressed: () {
                                            Navigator.of(context).pop(false);
                                          },
                                        ),
                                      ],
                                    );
                                  }).then((value) {
                                if (value != null) {
                                  if (value == true) {
                                    controller.deleteOrder();
                                  }
                                }
                              });
                            },
                          ),
                        //COMPARTILHAR
                        IconButton(
                          icon: const Icon(FontAwesomeIcons.share),
                          onPressed: () async {
                            //dialog with 2 options: share as image or as text
                            Get.bottomSheet(
                              Container(
                                decoration: BoxDecoration(
                                  color: Get.theme.colorScheme.surface,
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    topRight: Radius.circular(16),
                                  ),
                                  border: Border(
                                    top: BorderSide(
                                      color: Get.theme.colorScheme.primary,
                                      width: 2,
                                    ),
                                  ),
                                ),
                                child: Wrap(
                                  children: [
                                    if (order.value.clientPhone != null)
                                      ListTile(
                                        leading: const Icon(
                                            FontAwesomeIcons.whatsapp),
                                        title: const Text(
                                            'Compartilhar com o cliente'),
                                        onTap: () {
                                          Get.back();
                                          controller.shareClient();
                                        },
                                      ),
                                    if (order.value.clientPhone != null)
                                      const Divider(),
                                    ListTile(
                                      leading:
                                          const Icon(FontAwesomeIcons.image),
                                      title: const Text(
                                          'Compartilhar como imagem'),
                                      onTap: () {
                                        Get.back();
                                        controller.shareAsImage();
                                      },
                                    ),
                                    ListTile(
                                      leading: const Icon(Icons.text_fields),
                                      title:
                                          const Text('Compartilhar como texto'),
                                      onTap: () {
                                        Get.back();
                                        controller.shareAsText();
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                        //Imprimir Pix
                        CustomIconButton(
                          iconData: FontAwesomeIcons.pix,
                          onTap: () async {
                            await controller.showPixValueChoiceDialog();
                          },
                        ),

                        //Imprimir
                        CustomIconButton(
                          iconData: FontAwesomeIcons.print,
                          onTap: () async {
                            await controller.printOrder();
                          },
                        ),
                      ],
              ),
              body: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (!order.value.isDeleted)
                      Column(
                        children: [
                          if (controller
                                  .order.value.dayMarkingItems.isNotEmpty &&
                              controller
                                  .order.value.dayMarkingItems.last.active &&
                              !order.value.isPaid &&
                              !order.value.isJoined)
                            Card(
                              color: order.value.dayMarkingItems.last.otherMonth
                                  ? Colors.amber[800]
                                  : order.value.dayMarkingItems.last.vaiFazerPix
                                      ? Colors.deepPurple
                                      : order.value.dayMarkingItems.last
                                              .vaiLigar
                                          ? Colors.red
                                          : order.value.dayMarkingItems.last
                                                  .after
                                              ? Colors.blue[700]
                                              : Colors.indigo[700],
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          order.value.dayMarkingItems.last
                                                  .otherMonth
                                              ? FontAwesomeIcons.calendar
                                              : controller
                                                      .order
                                                      .value
                                                      .dayMarkingItems
                                                      .last
                                                      .vaiFazerPix
                                                  ? FontAwesomeIcons.pix
                                                  : controller
                                                          .order
                                                          .value
                                                          .dayMarkingItems
                                                          .last
                                                          .vaiLigar
                                                      ? FontAwesomeIcons.phone
                                                      : FontAwesomeIcons
                                                          .calendarCheck,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          order.value.dayMarkingItems.last.after
                                              ? 'DEPOIS'
                                              : controller
                                                      .order
                                                      .value
                                                      .dayMarkingItems
                                                      .last
                                                      .otherMonth
                                                  ? 'PROXIMO MÊS'
                                                  : controller
                                                          .order
                                                          .value
                                                          .dayMarkingItems
                                                          .last
                                                          .vaiFazerPix
                                                      ? 'Vai fazer PIX'
                                                      : controller
                                                              .order
                                                              .value
                                                              .dayMarkingItems
                                                              .last
                                                              .vaiLigar
                                                          ? 'Vai ligar'
                                                          : controller
                                                                      .order
                                                                      .value
                                                                      .dayMarkingItems
                                                                      .last
                                                                      .dayToVisit !=
                                                                  null
                                                              ? 'Marcado para ${DateTimeHelper.getFormattedDate(order.value.dayMarkingItems.last.dayToVisit!)}${order.value.dayMarkingItems.last.dayToVisit!.hour != 0 ? ' às ${order.value.dayMarkingItems.last.dayToVisit!.hour.toString().padLeft(2, '0')}:${order.value.dayMarkingItems.last.dayToVisit!.minute.toString().padLeft(2, '0')}' : ''}'
                                                              : 'ERROR',
                                          style: const TextStyle(
                                            fontSize: 19,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                    if (order.value.dayMarkingItems.last
                                            .observation !=
                                        '')
                                      Padding(
                                          padding:
                                              const EdgeInsets.only(top: 8),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Divider(),
                                              Text(
                                                'Obs: ${order.value.dayMarkingItems.last.observation}',
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          )),
                                    const SizedBox(height: 8),
                                    if (user != null &&
                                        (user.admin ||
                                            (user.cobrador &&
                                                (user.cobradorFixo ||
                                                    user.cobradorTemporario))))
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          ElevatedButton(
                                            onPressed: () async {
                                              await controller
                                                  .showMarkDialog(context);
                                            },
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.black,
                                              foregroundColor: Colors.white,
                                            ),
                                            child: const Text('Remarcar'),
                                          ),
                                          const SizedBox(width: 8),
                                          CircleAvatar(
                                            backgroundColor: Colors.black,
                                            child: IconButton(
                                              color: Colors.white,
                                              onPressed: () {
                                                //show history of markings in a dialog
                                                controller
                                                    .showMarkingsHistory();
                                              },
                                              icon: const Icon(
                                                  Icons.format_list_bulleted),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          CircleAvatar(
                                            backgroundColor: Colors.black,
                                            child: IconButton(
                                              color: Colors.white,
                                              onPressed: () {
                                                //ask if the user wants to delete the marking
                                                showDialog(
                                                  context: context,
                                                  builder: (ctx) {
                                                    return AlertDialog(
                                                      title:
                                                          const Text('Remover'),
                                                      content: const Text(
                                                          'Tem certeza que deseja remover essa marcação?'),
                                                      actions: [
                                                        TextButton(
                                                          child: const Text(
                                                            'Remover',
                                                            style: TextStyle(
                                                                color:
                                                                    Colors.red),
                                                          ),
                                                          onPressed: () {
                                                            Get.back(
                                                                result: true);
                                                          },
                                                        ),
                                                        ElevatedButton(
                                                          child: const Text(
                                                              'Cancelar'),
                                                          onPressed: () {
                                                            Navigator.of(
                                                                    context)
                                                                .pop(false);
                                                          },
                                                        ),
                                                      ],
                                                    );
                                                  },
                                                ).then((value) {
                                                  if (value != null) {
                                                    if (value == true) {
                                                      controller
                                                          .deleteMarking();
                                                    }
                                                  }
                                                });
                                              },
                                              icon: const Icon(Icons.close),
                                            ),
                                          ),
                                        ],
                                      ),
                                    Text(
                                      'marcado em ${DateTimeHelper.getFormattedDate(order.value.dayMarkingItems.last.createdAt)}, por ${order.value.dayMarkingItems.last.createdBy}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.white.withOpacity(0.9),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (order.value.toDelivery)
                            Card(
                              shadowColor:
                                  Theme.of(context).colorScheme.secondary,
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.delivery_dining,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Pedido para entregar',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    //button to mark as delivered
                                    ActionSlider.standard(
                                      height: 50,
                                      sliderBehavior: SliderBehavior.stretch,
                                      backgroundColor: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      toggleColor: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      action: (actionController) async {
                                        actionController.success();
                                        await Future.delayed(
                                            const Duration(milliseconds: 500));
                                        await controller.markAsDelivered();
                                      },
                                      child: Text(
                                        'Marcar como entregue',
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSecondary,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (order.value.products
                              .any((element) => element.quantityToDelivery > 0))
                            Card(
                              shadowColor:
                                  Theme.of(context).colorScheme.secondary,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const Row(
                                      mainAxisSize: MainAxisSize.min,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.delivery_dining,
                                          color: Colors.black,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Produtos para entregar',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    //show products to deliver
                                    ...order.value.products
                                        .where((element) =>
                                            element.quantityToDelivery > 0)
                                        .map(
                                          (e) => ListTile(
                                            title: Text(
                                              '${e.quantityToDelivery} x ${e.name} ${e.deliveryNote != '' ? '- (${e.deliveryNote})' : ''}',
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        ),

                                    //button to mark as delivered
                                    ActionSlider.standard(
                                      height: 50,
                                      sliderBehavior: SliderBehavior.stretch,
                                      backgroundColor: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      toggleColor: Theme.of(context)
                                          .colorScheme
                                          .onSecondary,
                                      action: (actionController) async {
                                        actionController.success();
                                        await Future.delayed(
                                            const Duration(milliseconds: 500));
                                        await controller
                                            .markProductsAsDelivered();
                                      },
                                      child: Text(
                                        'Marcar como entregue',
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSecondary,
                                            fontWeight: FontWeight.bold),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (order.value.isPaid)
                            Card(
                              color: Colors.green,
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.check,
                                          color: Colors.white,
                                        ),
                                        Text(
                                          'Pedido pago',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                    if (controller
                                            .order.value.payments.isNotEmpty &&
                                        order.value.payments.last.userName !=
                                            null)
                                      Text(
                                        'Pago em ${DateTimeHelper.getFormattedDate(order.value.payments.last.date)} às ${DateTimeHelper.getFormattedTime(order.value.payments.last.date)}, por ${order.value.payments.last.userName}',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: Colors.white.withOpacity(0.9),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          if (order.value.isJoined)
                            Card(
                              color: Colors.orange[900]!,
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    if (controller
                                            .order.value.joinedInOrderId ==
                                        null)
                                      const Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            FontAwesomeIcons.fileImport,
                                            color: Colors.white,
                                          ),
                                          SizedBox(width: 8),
                                          Text(
                                            'Juntado com outro pedido',
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    if (controller
                                            .order.value.joinedInOrderId !=
                                        null)
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const Icon(
                                            FontAwesomeIcons.fileImport,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(width: 8),
                                          const Text(
                                            'Juntado com outro ',
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              controller.goToJoinedOrder();
                                            },
                                            child: Text(
                                              'PEDIDO',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.amber[300],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    if (order.value.joinedAt != null)
                                      Builder(builder: (context) {
                                        String text =
                                            'Juntado em ${DateTimeHelper.getFormattedDate(order.value.joinedAt!)} às ${DateTimeHelper.getFormattedTime(order.value.joinedAt!)}';
                                        if (order.value.joinedBy != null) {
                                          text +=
                                              ', por ${order.value.joinedBy}';
                                        }
                                        return Text(
                                          text,
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                            color:
                                                Colors.white.withOpacity(0.9),
                                          ),
                                        );
                                      }),
                                  ],
                                ),
                              ),
                            ),
                          if (!order.value.isPaid &&
                              !order.value.isJoined &&
                              order.value.payments.isNotEmpty)
                            Container(
                              decoration: BoxDecoration(
                                color: Get.isDarkMode
                                    ? Colors.green[900]
                                    : Colors.green[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              margin: const EdgeInsets.only(bottom: 8),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 8,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Icon(
                                          Icons.list,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Pagamentos',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 4),
                                    ...order.value.payments.map(
                                      (e) => Container(
                                        padding: const EdgeInsets.all(0),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: Colors.green[300]!,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(4),
                                        ),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 16),
                                                child: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'R\$ ${e.value.toStringAsFixed(2)} - ${e.paymentMethod?.name}',
                                                    ),
                                                    Text(
                                                      '${DateTimeHelper.getFormattedDate(e.date)} as ${DateTimeHelper.getFormattedTime(e.date)} - ${e.userName != null ? 'por ${e.userName}' : ''}',
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            if (user != null &&
                                                (user.admin ||
                                                    // cobrador fixo, autorizado na rota
                                                    (user.cobrador &&
                                                        controller.user.value!
                                                            .cobradorFixo &&
                                                        controller.user.value!
                                                            .authorizedBillingRoutes
                                                            .contains(controller
                                                                .order
                                                                .value
                                                                .routeId)) ||
                                                    // cobrador temporário, autorizado na rota e no mesmo dia
                                                    (user.cobrador &&
                                                        user
                                                            .cobradorTemporario &&
                                                        user.authorizedBillingRoutes
                                                            .contains(controller
                                                                .order
                                                                .value
                                                                .routeId) &&
                                                        isSameDay) ||
                                                    // qualquer usuario vendendo na rota
                                                    (appState.isSelling &&
                                                        appState.isSellingRouteId ==
                                                            controller
                                                                .order
                                                                .value
                                                                .routeId &&
                                                        isSameDay)))
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.delete,
                                                  size: 20,
                                                ),
                                                onPressed: () {
                                                  //ask if the user wants to delete the payment
                                                  showDialog(
                                                    context: context,
                                                    builder: (ctx) {
                                                      return AlertDialog(
                                                        title: const Text(
                                                            'Apagar'),
                                                        content: const Text(
                                                            'Tem certeza que deseja apagar esse pagamento?'),
                                                        actions: [
                                                          TextButton(
                                                            child: const Text(
                                                              'Apagar',
                                                              style: TextStyle(
                                                                  color: Colors
                                                                      .red),
                                                            ),
                                                            onPressed: () {
                                                              Get.back(
                                                                  result: true);
                                                            },
                                                          ),
                                                          ElevatedButton(
                                                            child: const Text(
                                                                'Cancelar'),
                                                            onPressed: () {
                                                              Navigator.of(
                                                                      context)
                                                                  .pop(false);
                                                            },
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  ).then((value) {
                                                    if (value != null) {
                                                      if (value == true) {
                                                        controller
                                                            .deletePayment(e);
                                                      }
                                                    }
                                                  });
                                                },
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8),
                                      child: Text(
                                        'Restante a pagar: R\$ ${order.value.getTotalPending().toStringAsFixed(2)}',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (order.value.clientLatitude == null ||
                              order.value.clientLongitude == null)
                            Card(
                              color: Colors.red[800],
                              child: Padding(
                                padding: const EdgeInsets.all(8),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        const Icon(
                                          Icons.location_off,
                                          color: Colors.white,
                                        ),
                                        const SizedBox(width: 8),
                                        Column(
                                          mainAxisSize: MainAxisSize.min,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Sem localização',
                                              style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 18,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Text(
                                                'Edite o cliente para adicionar',
                                                style: TextStyle(
                                                  color: Colors.white
                                                      .withOpacity(0.9),
                                                  fontSize: 12,
                                                ),
                                                textAlign: TextAlign.start),
                                          ],
                                        ),
                                        const Spacer(),
                                        ElevatedButton.icon(
                                          onPressed: () {
                                            controller.goToEditClient();
                                          },
                                          style: ElevatedButton.styleFrom(
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            backgroundColor: Colors.white,
                                            foregroundColor: Colors.red[800],
                                          ),
                                          icon: const Icon(Icons.edit),
                                          label: const Text(
                                            'Editar\nCliente',
                                            textAlign: TextAlign.center,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      ),
                    if (order.value.isDeleted)
                      Card(
                        color: Colors.red[800],
                        child: Padding(
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.delete,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 8),
                                  Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Pedido apagado',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      if (order.value.deletedBy != null &&
                                          order.value.deletedAt != null)
                                        Text(
                                          'Apagado por ${order.value.deletedBy}\nno dia ${DateTimeHelper.getFormattedDate(order.value.deletedAt!)} às ${DateTimeHelper.getFormattedTime(order.value.deletedAt!)}',
                                          style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.9),
                                            fontSize: 12,
                                          ),
                                        ),
                                    ],
                                  ),
                                  const Spacer(),
                                  ElevatedButton.icon(
                                    onPressed: () {
                                      controller.restoreOrder();
                                    },
                                    style: ElevatedButton.styleFrom(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      backgroundColor: Colors.white,
                                      foregroundColor: Colors.red[800],
                                    ),
                                    icon: const Icon(Icons.restore_from_trash),
                                    label: const Text(
                                      'Restaurar\nPedido',
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    OrderWidget(order.value),
                    const SizedBox(height: 16),
                    _buildAdditionalInfoSection(order.value, Theme.of(context)),
                  ],
                ),
              ),
            );
          },
          controller.order,
        ),
        Obx(() => controller.isPrinting.value
            ? Container(
                color: Colors.black.withOpacity(0.5),
                child: const Center(
                  child: Card(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(
                            strokeCap: StrokeCap.round,
                          ),
                          SizedBox(height: 16),
                          Text('Imprimindo...'),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            : const SizedBox.shrink()),
      ],
    );
  }

  Widget _buildAdditionalInfoSection(OrderModel order, ThemeData theme) {
    return ExpansionTile(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      collapsedShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: const BorderSide(color: Colors.grey),
      ),
      title: const Text('Informações Adicionais'),
      children: [
        _buildInfoTable(order, theme),
      ],
    );
  }

  Widget _buildInfoTable(OrderModel order, ThemeData theme) {
    final mapData = order.toMap();

    final rows = mapData.entries.map((entry) {
      return TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: Text(entry.key,
                style: const TextStyle(fontWeight: FontWeight.w600)),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: Text(entry.value?.toString() ?? 'N/A'),
          ),
        ],
      );
    }).toList();

    return Table(
      border: TableBorder.all(
        color: theme.dividerColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: const {
        0: FixedColumnWidth(120),
        1: FlexColumnWidth(),
      },
      children: rows,
    );
  }

  Widget _buildInfoRow(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child:
              Text(value, style: TextStyle(color: theme.colorScheme.onPrimary)),
        ),
      ]),
    );
  }
}
