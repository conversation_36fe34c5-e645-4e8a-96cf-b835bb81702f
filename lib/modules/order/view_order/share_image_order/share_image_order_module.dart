import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/view_order/share_image_order/share_image_order_bindings.dart';
import 'package:fl_app/modules/order/view_order/share_image_order/share_image_order_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ShareImageOrderModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/share_image_order',
      page: () => const ShareImageOrderPage(),
      binding: ShareImageOrderBindings(),
    ),
  ];
}
