import 'dart:io';
import 'dart:typed_data';

import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

class ShareImageOrderController extends GetxController {
  OrderModel order = Get.arguments as OrderModel;

  WidgetsToImageController widgetsToImageController =
      WidgetsToImageController();

  RxBool loading = false.obs;

  @override
  void onReady() {
    super.onReady();
    Future.delayed(const Duration(milliseconds: 500), () async {
      loading.value = true;
      try {
        await shareAsImage();
      } catch (e) {
        Get.snackbar('Erro', 'Erro ao gerar imagem:$e');
      }
      loading.value = false;
      Get.back();
    });
  }

  Future<void> shareAsImage() async {
    Directory directory = await getTemporaryDirectory();
    Uint8List? pngBytes = await widgetsToImageController.capture();
    if (pngBytes != null) {
      File imgFile = File('${directory.path}/pedido ${order.date}.png');
      imgFile.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [
          XFile.fromData(pngBytes,
              name: 'pedido ${order.date}.png',
              path: imgFile.path,
              mimeType: 'image/png')
        ],
        text: 'Pedido ${DateTimeHelper.getFormattedDate(order.date)}',
      );
    }
  }
}
