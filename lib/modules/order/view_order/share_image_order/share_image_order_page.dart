import 'package:fl_app/modules/order/view_order/components/order_widget.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:widgets_to_image/widgets_to_image.dart';
import './share_image_order_controller.dart';

class ShareImageOrderPage extends GetView<ShareImageOrderController> {
  const ShareImageOrderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              children: [
                WidgetsToImage(
                  controller: controller.widgetsToImageController,
                  child: OrderWidget(
                    controller.order,
                    showClientButton: false,
                  ),
                ),
              ],
            ),
            Container(
              height: Get.height,
              width: Get.width,
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Obx(
                  () => controller.loading.value
                      ? const CircularProgressIndicator(
                          strokeCap: StrokeCap.round,
                        )
                      : Container(),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
