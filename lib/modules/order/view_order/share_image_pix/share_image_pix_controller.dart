import 'dart:io';
import 'dart:typed_data';

import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

class ShareImagePixController extends GetxController {
  OrderModel order =
      (Get.arguments as Map<String, dynamic>)['order'] as OrderModel;
  double value = (Get.arguments as Map<String, dynamic>)['value'] as double;

  WidgetsToImageController widgetsToImageController =
      WidgetsToImageController();

  RxBool loading = false.obs;

  String telefone = '+5587996169235';
  String nome = '<PERSON>';
  String cidade = 'Aguas Belas-PE';

  @override
  void onReady() {
    super.onReady();
    Future.delayed(const Duration(milliseconds: 500), () async {
      loading.value = true;
      try {
        await shareAsImage();
      } catch (e) {
        Get.snackbar('Erro', 'Erro ao gerar imagem:$e');
      }
      loading.value = false;
      Get.back();
    });
  }

  Future<void> shareAsImage() async {
    Directory directory = await getTemporaryDirectory();
    Uint8List? pngBytes = await widgetsToImageController.capture();
    if (pngBytes != null) {
      File imgFile = File('${directory.path}/pedido ${order.date}.png');
      imgFile.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [
          XFile.fromData(pngBytes,
              name: 'pix-${DateTime.now().toLocal()}.png',
              path: imgFile.path,
              mimeType: 'image/png')
        ],
        text:
            'Chave Pix: 87 99616-9235${value > 0 ? '\nValor: ${NumberFormatHelper.format(value)}' : ''}',
      );
    }
  }
}
