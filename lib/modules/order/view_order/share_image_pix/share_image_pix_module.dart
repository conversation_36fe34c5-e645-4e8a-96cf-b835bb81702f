import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/view_order/share_image_pix/share_image_pix_bindings.dart';
import 'package:fl_app/modules/order/view_order/share_image_pix/share_image_pix_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ShareImagePixModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/share_image_pix',
      page: () => const ShareImagePixPage(),
      binding: ShareImagePixBindings(),
    ),
  ];
}
