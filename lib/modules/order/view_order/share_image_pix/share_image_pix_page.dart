import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/application/helpers/print_order_helper.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:widgets_to_image/widgets_to_image.dart';
import 'share_image_pix_controller.dart';

class ShareImagePixPage extends GetView<ShareImagePixController> {
  const ShareImagePixPage({super.key});

  @override
  Widget build(BuildContext context) {
    String payload = PrintOrderHelper.generatePixPayload(
      beneficiaryCity: controller.cidade,
      beneficiaryName: controller.nome,
      pixKey: controller.telefone,
      transactionId: controller.order.id ?? '',
      amount: controller.value.toString(),
    );

    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          WidgetsToImage(
            controller: controller.widgetsToImageController,
            child: Container(
              width: 300,
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Image(
                    image: AssetImage('assets/images/logo_azul_horizontal.png'),
                    width: 200,
                  ),
                  const Text(
                    'Chave PIX: (87) 99616-9235',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                  const Text(
                    'Nome: Francisco Lopes da Silva',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                    ),
                  ),
                  const Text(
                    'Banco: Banco Bradesco S.A.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                    ),
                  ),
                  QrImageView(
                    data: payload,
                    size: 200,
                    eyeStyle: QrEyeStyle(
                      eyeShape: QrEyeShape.square,
                      color: Colors.indigo[900],
                    ),
                    dataModuleStyle: QrDataModuleStyle(
                      dataModuleShape: QrDataModuleShape.square,
                      color: Colors.indigo[900],
                    ),
                  ),
                  if (controller.value != 0)
                    Text(
                      'Valor: ${NumberFormatHelper.format(controller.value)}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  const Divider(
                    color: Colors.black,
                    height: 8,
                  ),
                  const Text(
                    'Apos realizar o pagamento,\nenvie o comprovante para o WhatsApp (87) 99616-9235',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          Container(
            height: Get.height,
            width: Get.width,
            color: Colors.black.withOpacity(0.6),
            child: Center(
              child: Obx(
                () => controller.loading.value
                    ? const Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Compartilhando...',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 16),
                          CircularProgressIndicator(
                            strokeCap: StrokeCap.round,
                            color: Colors.white,
                          ),
                        ],
                      )
                    : Container(),
              ),
            ),
          )
        ],
      ),
    );
  }
}
