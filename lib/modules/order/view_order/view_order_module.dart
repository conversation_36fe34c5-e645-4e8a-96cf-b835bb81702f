import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/view_order/share_image_order/share_image_order_module.dart';
import 'package:fl_app/modules/order/view_order/share_image_pix/share_image_pix_module.dart';
import 'package:fl_app/modules/order/view_order/view_order_bindings.dart';
import 'package:fl_app/modules/order/view_order/view_order_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ViewOrderModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/view-order',
      page: () => ViewOrderPage(),
      binding: ViewOrderBindings(),
    ),
    ...ShareImageOrderModule().routers,
    ...ShareImagePixModule().routers,
  ];
}
