import 'package:action_slider/action_slider.dart';
import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/print_order_helper.dart';
import 'package:fl_app/application/ui/loader/loader_mixin.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/day_making_item.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';
import 'package:fl_app/services/bluetooth/bluetooth_service.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ViewOrderController extends GetxController with LoaderMixin {
  Rx<OrderModel> order = (Get.arguments as OrderModel).obs;
  final OrderRepository _orderRepository;
  final BluetoothService _bluetoothService;
  final UserService _userService;

  RxBool isPrinting = false.obs;

  RxBool isLoading = false.obs;

  Rxn<UserModel> user = Rxn<UserModel>();

  TextEditingController discountCtrl = TextEditingController();

  Rx<AppState> appState = Get.find<AppStateService>().getAppState().obs;

  ViewOrderController({
    required OrderRepository orderRepository,
    required BluetoothService bluetoothService,
    required UserService userService,
  })  : _orderRepository = orderRepository,
        _userService = userService,
        _bluetoothService = bluetoothService {
    init();
    loaderListener(isLoading, barrierDismissible: true);
  }

  init() async {
    user.value = await _userService.getUserAuthenticated();
    user.refresh();
  }

  goToPayment() {}

  goToEditOrder() async {
    OrderModel? orderEdited = await Get.toNamed('/cart', arguments: {
      'order': order.value,
      'cart': ShoppingCart(),
    }) as OrderModel?;
    if (orderEdited != null) {
      order.value = orderEdited;
      order.refresh();
    }
  }

  deleteOrder() {
    _orderRepository.deleteOrder(order.value);
    Get.back();
  }

  Future<void> printOrder() async {
    if (!(await _bluetoothService.isBluetoothEnabled())) {
      Get.snackbar('Erro', 'Bluetooth desabilitado');
      return;
    } else if (await _bluetoothService.getMainMac() == "" &&
        await _bluetoothService.getMainName() == "") {
      Get.showSnackbar(GetSnackBar(
        message: 'Nenhuma impressora selecionada',
        backgroundColor: Colors.black,
        duration: const Duration(seconds: 6),
        mainButton: TextButton(
            onPressed: () {
              Get.toNamed('/bluetooth');
            },
            child: const Text('Selecionar')),
      ));
      return;
    } //Overlay de impressão que bloqueia a tela e aparece imprimindo...

    isPrinting.value = true;

    final result = await _bluetoothService
        .print(await PrintOrderHelper.getOrderBytesToPrint(order.value));

    if (result == false) {
      final connected = await _bluetoothService.isConnected();
      if (connected == false) {
        Get.showSnackbar(GetSnackBar(
          message: 'Impressora não conectada',
          backgroundColor: Colors.black,
          duration: const Duration(seconds: 6),
          mainButton: TextButton(
              onPressed: () {
                Get.toNamed('/bluetooth');
              },
              child: const Text('Conectar')),
        ));
      } else {
        Get.showSnackbar(const GetSnackBar(
          message: 'Erro ao imprimir',
          backgroundColor: Colors.black,
          duration: Duration(seconds: 6),
        ));
      }
    }
    isPrinting.value = false;
  }

  Future<void> printPix({double? valor}) async {
    if (!(await _bluetoothService.isBluetoothEnabled())) {
      Get.snackbar('Erro', 'Bluetooth desabilitado');
      return;
    } else if (await _bluetoothService.getMainMac() == "" &&
        await _bluetoothService.getMainName() == "") {
      Get.showSnackbar(GetSnackBar(
        message: 'Nenhuma impressora selecionada',
        backgroundColor: Colors.black,
        duration: const Duration(seconds: 6),
        mainButton: TextButton(
            onPressed: () {
              Get.toNamed('/bluetooth');
            },
            child: const Text('Selecionar')),
      ));
      return;
    } //Overlay de impressão que bloqueia a tela e aparece imprimindo...

    isPrinting.value = true;

    final result = await _bluetoothService.print(await PrintOrderHelper.getPix(
      order: order.value,
      value: valor,
    ));

    if (result == false) {
      final connected = await _bluetoothService.isConnected();
      if (connected == false) {
        Get.showSnackbar(GetSnackBar(
          message: 'Impressora não conectada',
          backgroundColor: Colors.black,
          duration: const Duration(seconds: 6),
          mainButton: TextButton(
              onPressed: () {
                Get.toNamed('/bluetooth');
              },
              child: const Text('Conectar')),
        ));
      } else {
        Get.showSnackbar(const GetSnackBar(
          message: 'Erro ao imprimir',
          backgroundColor: Colors.black,
          duration: Duration(seconds: 6),
        ));
      }
    }
    isPrinting.value = false;
  }

  Future<void> showPixValueChoiceDialog() async {
    RxDouble val = order.value.getTotalPending().obs;
    RxBool setValue = false.obs;

    TextEditingController valCtrl = TextEditingController();
    valCtrl.value = TextEditingValue(text: val.value.toString());

    await Get.dialog(
      AlertDialog(
        title: const Column(
          children: [
            Icon(FontAwesomeIcons.pix),
            SizedBox(height: 8),
            Text('Imprimir PIX'),
          ],
        ),
        content: Obx(() {
          return AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: 350,
            width: 200,
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Definir valor'),
                  value: setValue.value,
                  activeColor: Get.theme.colorScheme.secondary,
                  onChanged: (value) {
                    setValue.value = value;
                  },
                  contentPadding: const EdgeInsets.all(0),
                ),
                if (setValue.value)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: TextField(
                      controller: valCtrl,
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        val.value = double.parse(value);
                      },
                      decoration: const InputDecoration(
                        labelText: 'Valor',
                        prefixText: 'R\$ ',
                      ),
                    ),
                  ),
                const Spacer(),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon: const Icon(FontAwesomeIcons.qrcode),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.secondary,
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                    ),
                    onPressed: () {
                      Get.back();
                      showPix(setValue.value ? val.value : 0);
                    },
                    label: const Text('Ver QR Code'),
                  ),
                ),
                const SizedBox(height: 8),
                //compartilhar
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon: const Icon(FontAwesomeIcons.share),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.secondary,
                      foregroundColor: Get.theme.colorScheme.onSecondary,
                    ),
                    onPressed: () {
                      Get.back();
                      sharePixAsImage(setValue.value ? val.value : 0);
                    },
                    label: const Text('Compartilhar'),
                  ),
                ),
                const SizedBox(height: 8),
                //imprimir
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon: const Icon(FontAwesomeIcons.print),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.secondary,
                      foregroundColor: Get.theme.colorScheme.onSecondary,
                    ),
                    onPressed: () {
                      printPix(valor: setValue.value ? val.value : 0);
                      Get.back();
                    },
                    label: const Text('Imprimir'),
                  ),
                ),
                const SizedBox(height: 8),
                //Cancelar
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    style: TextButton.styleFrom(
                      foregroundColor: Get.theme.colorScheme.secondary,
                    ),
                    onPressed: () {
                      Get.back();
                    },
                    child: const Text('Cancelar'),
                  ),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }

  Future<void> showPix(double value) async {
    String telefone = '+5587996169235';
    String nome = 'Francisco Lopes da Silva';
    String cidade = 'Aguas Belas-PE';
    double valor = value;
    String identificador = order.value.id ?? '';

    String payload = PrintOrderHelper.generatePixPayload(
      beneficiaryCity: cidade,
      beneficiaryName: nome,
      pixKey: telefone,
      transactionId: identificador,
      amount: valor != 0 ? valor.toString() : null,
    );

    await Get.dialog(
      AlertDialog(
        title: const Column(
          children: [
            Icon(FontAwesomeIcons.pix),
            SizedBox(height: 8),
            Text('PIX'),
          ],
        ),
        content: SizedBox(
          height: 400,
          width: Get.width * 0.8,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text(
                'Chave PIX: (87) 99616-9235',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Text(
                'Nome: Francisco Lopes da Silva',
                style: TextStyle(
                  fontSize: 16,
                ),
              ),
              const Text(
                'Banco: Banco Bradesco S.A.',
                style: TextStyle(
                  fontSize: 16,
                ),
              ),
              QrImageView(
                data: payload,
                size: 250,
                eyeStyle: QrEyeStyle(
                  eyeShape: QrEyeShape.square,
                  color: Get.isDarkMode ? Colors.white : Colors.black,
                ),
                dataModuleStyle: QrDataModuleStyle(
                  dataModuleShape: QrDataModuleShape.square,
                  color: Get.isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              if (value != 0)
                Text(
                  'Valor: R\$ ${value.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                  ),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            child: const Text(
              'Fechar',
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
    );
  }

  void shareAsImage() async {
    Get.toNamed('/share_image_order', arguments: order.value);
  }

  void sharePixAsImage(double value) async {
    Get.toNamed('/share_image_pix', arguments: {
      'order': order.value,
      'value': value,
    });
  }

  void shareAsText() {
    String text = PrintOrderHelper.getOrderTextToShare(order.value);
    Share.share(text);
  }

  void shareClient() async {
    var text = PrintOrderHelper.getOrderTextToShare(order.value);
    var textEncoded = Uri.encodeFull(text);
    var whatsappUrl =
        "whatsapp://send?phone=55${order.value.clientPhone}&text=$textEncoded";
    await launchUrl(Uri.parse(whatsappUrl));
  }

  markAsDelivered() async {
    order.value = await _orderRepository.markAsDelivered(order.value);
    order.refresh();
  }

  markAsPaid(PaymentMethod paymentMethod, DateTime date) async {
    try {
      order.value =
          await _orderRepository.markAsPaid(order.value, paymentMethod, date);
      order.refresh();
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
        message: e.toString(),
        backgroundColor: Colors.red,
        borderRadius: 8,
        margin: const EdgeInsets.all(8),
        duration: const Duration(seconds: 4),
      ));
    }
  }

  markAsUnpaid() async {
    try {
      order.value = await _orderRepository.markAsUnpaid(order.value);
      order.refresh();
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
        message: e.toString(),
        backgroundColor: Colors.red,
        borderRadius: 8,
        margin: const EdgeInsets.all(8),
        duration: const Duration(seconds: 4),
      ));
    }
  }

  markAsJoined({OrderModel? orderJoined}) async {
    try {
      order.value = await _orderRepository.markAsJoined(order.value,
          orderJoinedId: orderJoined?.id);
      order.refresh();
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
        message: e.toString(),
        backgroundColor: Colors.red,
        borderRadius: 8,
        margin: const EdgeInsets.all(8),
        duration: const Duration(seconds: 4),
      ));
    }
  }

  markAsUnjoined() async {
    try {
      order.value = await _orderRepository.markAsUnjoined(order.value);
      order.refresh();
    } catch (e) {
      Get.showSnackbar(GetSnackBar(
        message: e.toString(),
        backgroundColor: Colors.red,
        borderRadius: 8,
        margin: const EdgeInsets.all(8),
        duration: const Duration(seconds: 4),
      ));
    }
  }

  Future<bool> discountOrder(
      double parse, PaymentMethod paymentMethod, DateTime date) async {
    discountCtrl.clear();

    double totalPaid = 0;
    for (var payment in order.value.payments) {
      totalPaid += payment.value;
    }
    if ((totalPaid + parse) > order.value.calculateTotal()) {
      return false;
    }

    order.value.payments.add(
      Payment(
        date: date,
        value: parse,
        paymentMethod: paymentMethod,
        userId: user.value!.id,
        userName: user.value!.name,
      ),
    );
    if ((totalPaid + parse) == order.value.calculateTotal()) {
      order.value.isPaid = true;
      for (var e in order.value.dayMarkingItems) {
        e.active = false;
      }
    }

    order.value = await _orderRepository.updatePayments(order.value);
    order.refresh();
    return true;
  }

  void deletePayment(Payment e) async {
    order.value.payments.remove(e);
    order.value = await _orderRepository.updatePayments(order.value);
    order.refresh();
  }

  markProductsAsDelivered() async {
    for (var element in order.value.products) {
      element.quantityToDelivery = 0;
      element.deliveryNote = '';
    }
    order.value = await _orderRepository.markProductsAsDelivered(order.value);
    order.refresh();
  }

  Future<void> showMarkDialog(BuildContext context) async {
    Rxn<DateTime> selectedDate = Rxn<DateTime>();
    Rx<TimeOfDay> selectedTime = const TimeOfDay(hour: 0, minute: 0).obs;
    RxBool depois = false.obs;
    RxBool outroMes = false.obs;
    RxBool vaiFazerPix = false.obs;
    RxBool vaiLigar = false.obs;
    RxString obs = ''.obs;
    bool? marcar = await Get.dialog(
      AlertDialog(
        title: Text(
          'Marcar dia',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 18,
            color: Get.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        titleTextStyle: const TextStyle(
          fontSize: 18,
        ),
        content: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: SizedBox(
            height: 270,
            width: Get.width * 0.9,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Row(
                    children: [
                      //STATUS ATUAL
                      Expanded(
                        child: Obx(
                          () => ElevatedButton(
                            onPressed: () async {
                              DateTime? date = await showDatePicker(
                                context: context,
                                initialDate: DateTime.now(),
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now()
                                    .add(const Duration(days: 365)),
                              );
                              if (date != null) {
                                selectedDate.value = date;
                                depois.value = false;
                                outroMes.value = false;
                                vaiFazerPix.value = false;
                                vaiLigar.value = false;
                              }
                            },
                            style: ElevatedButton.styleFrom(
                                backgroundColor: outroMes.value
                                    ? Colors.amber[700]
                                    : depois.value
                                        ? Colors.blue[700]
                                        : vaiFazerPix.value
                                            ? Colors.deepPurple[700]
                                            : vaiLigar.value
                                                ? Colors.red[700]
                                                : selectedDate.value != null
                                                    ? Colors.indigo[700]
                                                    : Colors.indigo[200],
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                )),
                            child: depois.value
                                ? const Text('Depois')
                                : outroMes.value
                                    ? const Text('Outro Mês')
                                    : vaiFazerPix.value
                                        ? const Text('Vai Fazer PIX')
                                        : vaiLigar.value
                                            ? const Text('Vai Ligar')
                                            : selectedDate.value != null
                                                ? Text(DateTimeHelper
                                                    .getFormattedDate(
                                                        selectedDate.value!))
                                                : const Text('Selecionar data',
                                                    style: TextStyle(
                                                        color: Colors.black)),
                          ),
                        ),
                      ),

                      Obx(
                        () => selectedDate.value != null &&
                                !depois.value &&
                                !outroMes.value &&
                                !vaiFazerPix.value &&
                                !vaiLigar.value
                            ? Row(
                                children: [
                                  const SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: () async {
                                      TimeOfDay? time = await showTimePicker(
                                        context: context,
                                        initialTime:
                                            const TimeOfDay(hour: 0, minute: 0),
                                      );
                                      if (time != null) {
                                        selectedTime.value = time;
                                      }
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.indigo[700],
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: Text(
                                        '${selectedTime.value.hour.toString().padLeft(2, '0')}:${selectedTime.value.minute.toString().padLeft(2, '0')}'),
                                  ),
                                ],
                              )
                            : const SizedBox.shrink(),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      //Amanhã
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            selectedDate.value =
                                DateTime.now().add(const Duration(days: 1));
                            depois.value = false;
                            outroMes.value = false;
                            selectedTime.value =
                                const TimeOfDay(hour: 0, minute: 0);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                          ),
                          child: const Text(
                            'Amanhã',
                            style: TextStyle(color: Colors.black),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      //Depois
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            depois.value = true;
                            outroMes.value = false;
                            vaiFazerPix.value = false;
                            vaiLigar.value = false;
                            selectedTime.value =
                                const TimeOfDay(hour: 0, minute: 0);
                          },
                          style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue),
                          child: const Text(
                            'Depois',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            vaiFazerPix.value = true;
                            depois.value = false;
                            outroMes.value = false;
                            vaiLigar.value = false;
                            selectedTime.value =
                                const TimeOfDay(hour: 0, minute: 0);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.deepPurple,
                          ),
                          child: const Text(
                            'Vai Fazer PIX',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            vaiLigar.value = true;
                            depois.value = false;
                            outroMes.value = false;
                            vaiFazerPix.value = false;
                            selectedTime.value =
                                const TimeOfDay(hour: 0, minute: 0);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                          ),
                          child: const Row(
                            children: [
                              Icon(Icons.phone, color: Colors.white),
                              SizedBox(width: 4),
                              Text(
                                'Vai Ligar',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            outroMes.value = true;
                            depois.value = false;
                            vaiFazerPix.value = false;
                            vaiLigar.value = false;
                            selectedTime.value =
                                const TimeOfDay(hour: 0, minute: 0);
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.amber,
                          ),
                          child: const Text(
                            'Outro Mês',
                            style: TextStyle(color: Colors.black),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Observação',
                    ),
                    onChanged: (value) {
                      obs.value = value;
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            child: const Text(
              'Cancelar',
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          ElevatedButton(
            child: const Text(
              'Marcar',
            ),
            onPressed: () {
              Get.back(result: true);
            },
          ),
        ],
      ),
    );

    if ((selectedDate.value != null ||
            depois.value ||
            outroMes.value ||
            vaiFazerPix.value ||
            vaiLigar.value) &&
        marcar != null &&
        marcar) {
      await markDay(
        selectedDate.value,
        selectedTime.value,
        obs.value,
        depois.value,
        outroMes.value,
        vaiFazerPix.value,
        vaiLigar.value,
      );
      String message = '';
      if (outroMes.value) {
        message = 'Marcado para Outro Mês';
      } else if (depois.value) {
        message = 'Marcado para Depois';
      } else {
        if (selectedDate.value != null) {
          message =
              'Marcado para ${DateTimeHelper.getFormattedDate(selectedDate.value!)} às ${selectedTime.value.hour.toString().padLeft(2, '0')}:${selectedTime.value.minute.toString().padLeft(2, '0')}';
        }
      }
      Get.showSnackbar(GetSnackBar(
        message: message,
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
      ));
    }
  }

  markDay(DateTime? markDate, TimeOfDay markTime, String obs, bool depois,
      bool outroMes, bool vaiFazerPix, bool vaiLigar) async {
    if (markDate != null) {
      markDate = DateTime(
        markDate.year,
        markDate.month,
        markDate.day,
        markTime.hour,
        markTime.minute,
      );
    }
    //for (var element in order.value.dayMarkingItems) {
    // set the active to false
    //}
    for (var element in order.value.dayMarkingItems) {
      element.active = false;
    }
    order.value.dayMarkingItems.add(DayMarkingItem(
      observation: obs,
      dayToVisit: markDate,
      after: depois,
      otherMonth: outroMes,
      active: true,
      createdAt: DateTime.now(),
      vaiFazerPix: vaiFazerPix,
      vaiLigar: vaiLigar,
      createdBy: user.value!.name,
    ));
    order.value = await _orderRepository.updateDayMarking(order.value);
    order.refresh();
  }

  void deleteMarking() {
    order.value.dayMarkingItems.last.active = false;
    _orderRepository.updateOrder(order.value);
    order.refresh();
  }

  Future<void> showMarkingsHistory() async {
    Get.dialog(
      AlertDialog(
        title: const Text('Histórico Marcações'),
        content: SizedBox(
          height: 300,
          width: 300,
          child: ListView.builder(
            itemCount: order.value.dayMarkingItems.length,
            itemBuilder: (ctx, index) {
              var item = order.value.dayMarkingItems.reversed.toList()[index];
              return Card(
                child: ListTile(
                  leading: Text(
                    DateTimeHelper.getFormattedDate(item.createdAt),
                  ),
                  title: Text(
                    item.after
                        ? 'DEPOIS'
                        : item.otherMonth
                            ? 'PROXIMO MÊS'
                            : item.vaiFazerPix
                                ? 'VAI FAZER PIX'
                                : item.vaiLigar
                                    ? 'VAI LIGAR'
                                    : item.dayToVisit != null
                                        ? 'Marcado para ${DateTimeHelper.getFormattedDate(item.dayToVisit!)}${item.dayToVisit!.hour != 0 ? ' às ${item.dayToVisit!.hour.toString().padLeft(2, '0')}:${item.dayToVisit!.minute.toString().padLeft(2, '0')}' : ''}'
                                        : '',
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (item.observation != '')
                        Text(
                          'Obs: ${item.observation}',
                        ),
                      if (item.createdBy != '')
                        Text(
                          'Marcado por: ${item.createdBy}',
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void goToEditClient() async {
    ClientModel? client =
        Get.find<ClientService>().getClientFromCache(order.value.clientId);
    if (client != null) {
      await Get.toNamed('/form_client', arguments: client);
      refreshOrder();
    } else {
      Get.showSnackbar(const GetSnackBar(
        message: 'Cliente não encontrado',
        backgroundColor: Colors.red,
      ));
    }
  }

  void refreshOrder({OrderModel? orderUpdated}) async {
    if (orderUpdated != null) {
      order.value = orderUpdated;
    } else {
      order.value =
          await _orderRepository.getOrder(order.value.id!) ?? order.value;
    }
    order.refresh();
    // Get.replace(
    //     ViewOrderController(
    //       orderRepository: _orderRepository,
    //       bluetoothService: _bluetoothService,
    //       userService: _userService,
    //     ),
    //     tag: order.value.id);
    // // Get.toNamed('/view-order', arguments: order.value);
  }

  Future<void> showDiscountDialog(BuildContext context) async {
    RxBool isDinheiro = true.obs;
    RxBool isCartao = false.obs;
    RxBool isPix = false.obs;

    Rx<DateTime> date = DateTime.now().obs;

    discountCtrl.clear();

    bool discount = await Get.dialog(
      AlertDialog(
        title: const Text('Descontar Dinheiro'),
        content: Obx(
          () => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Forma de pagamento:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      height: 80,
                      child: ElevatedButton(
                        onPressed: () {
                          isDinheiro.value = true;
                          isCartao.value = false;
                          isPix.value = false;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isDinheiro.value
                              ? Colors.green[700]
                              : Colors.grey[300],
                          padding: const EdgeInsets.all(2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          alignment: Alignment.center,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              FontAwesomeIcons.moneyBill,
                              color: isDinheiro.value
                                  ? Colors.white
                                  : Colors.grey[700],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Dinheiro',
                              style: TextStyle(
                                color: isDinheiro.value
                                    ? Colors.white
                                    : Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: SizedBox(
                      height: 80,
                      child: ElevatedButton(
                        onPressed: () {
                          isDinheiro.value = false;
                          isCartao.value = true;
                          isPix.value = false;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isCartao.value
                              ? Colors.blue[700]
                              : Colors.grey[300],
                          padding: const EdgeInsets.all(2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          alignment: Alignment.center,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              FontAwesomeIcons.creditCard,
                              color: isCartao.value
                                  ? Colors.white
                                  : Colors.grey[700],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Cartão',
                              style: TextStyle(
                                color: isCartao.value
                                    ? Colors.white
                                    : Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: SizedBox(
                      height: 80,
                      child: ElevatedButton(
                        onPressed: () {
                          isDinheiro.value = false;
                          isCartao.value = false;
                          isPix.value = true;
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isPix.value
                              ? Colors.deepPurple[700]
                              : Colors.grey[300],
                          padding: const EdgeInsets.all(2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          alignment: Alignment.center,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              FontAwesomeIcons.pix,
                              color:
                                  isPix.value ? Colors.white : Colors.grey[700],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'PIX',
                              style: TextStyle(
                                color: isPix.value
                                    ? Colors.white
                                    : Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (user.value != null && user.value!.admin)
                Column(
                  children: [
                    const SizedBox(height: 8),
                    const Text(
                      'Data:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.indigo[50],
                              foregroundColor: Colors.indigo,
                              textStyle: const TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            onPressed: () {
                              showDatePicker(
                                context: context,
                                initialDate: date.value,
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                              ).then((value) {
                                if (value != null) {
                                  date.value = value;
                                }
                              });
                            },
                            child: Text(
                              DateTimeHelper.getFormattedDate(date.value),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              const SizedBox(height: 8),
              TextField(
                controller: discountCtrl,
                autofocus: true,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Valor',
                  prefixText: 'R\$ ',
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            child: const Text(
              'Cancelar',
            ),
            onPressed: () {
              Get.back(result: false);
            },
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.indigo,
            ),
            onPressed: () {
              Get.back(result: true);
            },
            child: const Text(
              'Salvar',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
    if (discountCtrl.text.isNotEmpty && discount) {
      bool result = false;
      if (isDinheiro.value) {
        result = await discountOrder(double.parse(discountCtrl.text),
            PaymentMethod.dinheiro, date.value);
      } else if (isCartao.value) {
        result = await discountOrder(
            double.parse(discountCtrl.text), PaymentMethod.cartao, date.value);
      } else if (isPix.value) {
        result = await discountOrder(
            double.parse(discountCtrl.text), PaymentMethod.pix, date.value);
      }
      if (!result) {
        Get.snackbar(
          'Erro',
          'Valor maior que o restante a pagar',
          backgroundColor: Colors.red[100],
        );
      }
    }
  }

  showUnpaidDialog(BuildContext context) async {
    await Get.dialog(
      AlertDialog(
        backgroundColor: Get.isDarkMode ? Colors.red[900] : Colors.red,
        title: const Column(
          children: [
            Icon(
              FontAwesomeIcons.triangleExclamation,
              color: Colors.amber,
              size: 50,
            ),
            SizedBox(height: 8),
            Text(
              'Desmarcar como pago',
            ),
          ],
        ),
        content: SizedBox(
          height: 50,
          width: Get.width * 0.8,
          child: ActionSlider.standard(
            height: 50,
            sliderBehavior: SliderBehavior.stretch,
            backgroundColor: Colors.black,
            toggleColor: Colors.amber,
            action: (actionController) async {
              actionController.success();
              await Future.delayed(const Duration(milliseconds: 500));
              await markAsUnpaid();
              Get.back();
              refreshOrder(orderUpdated: order.value);
              order.refresh();
              update();
            },
            child: const Text(
              'Desmarcar',
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.amber,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
        actions: [
          TextButton(
            child: Text(
              'Voltar',
              style: TextStyle(
                  color: Get.isDarkMode ? Colors.white : Colors.black),
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
    );
  }

  Future<void> showPaidDialog(BuildContext context) async {
    RxBool isDinheiro = true.obs;
    RxBool isCartao = false.obs;
    RxBool isPix = false.obs;

    Rx<DateTime> date = DateTime.now().obs;

    await Get.dialog(
      AlertDialog(
        title: Obx(
          () => Column(
            children: [
              Icon(
                FontAwesomeIcons.dollarSign,
                color: isDinheiro.value
                    ? Colors.green
                    : isCartao.value
                        ? Colors.blue
                        : Colors.deepPurple,
                size: 50,
              ),
              const SizedBox(height: 8),
              Text(
                'Marcar como pago',
                style: TextStyle(
                  color: isDinheiro.value
                      ? Colors.green
                      : isCartao.value
                          ? Colors.blue
                          : Colors.deepPurple,
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        content: Obx(
          () => SizedBox(
            height: 260,
            width: Get.width * 0.8,
            child: Column(
              children: [
                const Text(
                  'Forma de pagamento:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 80,
                        child: ElevatedButton(
                          onPressed: () {
                            isDinheiro.value = true;
                            isCartao.value = false;
                            isPix.value = false;
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isDinheiro.value
                                ? Colors.green[700]
                                : Colors.grey[300],
                            padding: const EdgeInsets.all(2),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            alignment: Alignment.center,
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                FontAwesomeIcons.moneyBill,
                                color: isDinheiro.value
                                    ? Colors.white
                                    : Colors.grey[700],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Dinheiro',
                                style: TextStyle(
                                  color: isDinheiro.value
                                      ? Colors.white
                                      : Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: SizedBox(
                        height: 80,
                        child: ElevatedButton(
                          onPressed: () {
                            isDinheiro.value = false;
                            isCartao.value = true;
                            isPix.value = false;
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isCartao.value
                                ? Colors.blue[700]
                                : Colors.grey[300],
                            padding: const EdgeInsets.all(2),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            alignment: Alignment.center,
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                FontAwesomeIcons.creditCard,
                                color: isCartao.value
                                    ? Colors.white
                                    : Colors.grey[700],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Cartão',
                                style: TextStyle(
                                  color: isCartao.value
                                      ? Colors.white
                                      : Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: SizedBox(
                        height: 80,
                        child: ElevatedButton(
                          onPressed: () {
                            isDinheiro.value = false;
                            isCartao.value = false;
                            isPix.value = true;
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: isPix.value
                                ? Colors.deepPurple[700]
                                : Colors.grey[300],
                            padding: const EdgeInsets.all(2),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            alignment: Alignment.center,
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                FontAwesomeIcons.pix,
                                color: isPix.value
                                    ? Colors.white
                                    : Colors.grey[700],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'PIX',
                                style: TextStyle(
                                  color: isPix.value
                                      ? Colors.white
                                      : Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                if (user.value != null && user.value!.admin)
                  Column(children: [
                    const SizedBox(height: 8),
                    const Text(
                      'Data:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.indigo[50],
                              foregroundColor: Colors.indigo,
                              textStyle: const TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            onPressed: () {
                              showDatePicker(
                                context: context,
                                initialDate: date.value,
                                firstDate: DateTime(2020),
                                lastDate: DateTime.now(),
                              ).then((value) {
                                if (value != null) {
                                  date.value = value;
                                }
                              });
                            },
                            child: Text(
                              DateTimeHelper.getFormattedDate(date.value),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ]),
                const SizedBox(height: 8),
                ActionSlider.standard(
                  height: 50,
                  sliderBehavior: SliderBehavior.stretch,
                  backgroundColor: Colors.black,
                  toggleColor: isDinheiro.value
                      ? Colors.green
                      : isCartao.value
                          ? Colors.blue
                          : Colors.deepPurple,
                  action: (actionController) async {
                    actionController.loading();
                    await Future.delayed(const Duration(milliseconds: 300));
                    if (isDinheiro.value) {
                      await markAsPaid(PaymentMethod.dinheiro, date.value);
                    } else if (isCartao.value) {
                      await markAsPaid(PaymentMethod.cartao, date.value);
                    } else if (isPix.value) {
                      await markAsPaid(PaymentMethod.pix, date.value);
                    }
                    await Future.delayed(const Duration(milliseconds: 300));
                    actionController.success();
                    Get.back();
                    //reload order
                    refreshOrder(orderUpdated: order.value);
                  },
                  child: Text(
                    'Marcar',
                    style: TextStyle(
                        fontSize: 16,
                        color: isDinheiro.value
                            ? Colors.green
                            : isCartao.value
                                ? Colors.blue
                                : Colors.deepPurple,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            child: const Text(
              'Voltar',
              style: TextStyle(
                color: Colors.green,
              ),
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
    );
  }

  Future<void> showJoinedDialog(BuildContext context) async {
    RxBool jaJuntou = false.obs;
    await Get.dialog(
      AlertDialog(
        title: Column(
          children: [
            Icon(
              FontAwesomeIcons.fileImport,
              color: Colors.orange[900]!,
              size: 50,
            ),
            const SizedBox(height: 8),
            Text(
              'Juntou com outro pedido?',
              style: TextStyle(color: Colors.orange[900]!),
            ),
          ],
        ),
        content: Obx(
          () => jaJuntou.value
              ? SizedBox(
                  height: 50,
                  width: Get.width * 0.8,
                  child: ActionSlider.standard(
                    height: 50,
                    sliderBehavior: SliderBehavior.stretch,
                    backgroundColor: Colors.black,
                    toggleColor: Colors.orange,
                    action: (actionController) async {
                      actionController.success();
                      await Future.delayed(const Duration(milliseconds: 500));
                      await markAsJoined(orderJoined: null);
                      Get.back();
                    },
                    child: const Text(
                      'Marcar',
                      style: TextStyle(
                          fontSize: 16,
                          color: Colors.orange,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                )
              : Row(
                  children: [
                    Expanded(
                      child: SizedBox(
                        height: 100,
                        child: ElevatedButton(
                          onPressed: () {
                            jaJuntou.value = true;
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[200],
                            foregroundColor: Colors.indigo,
                          ),
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                FontAwesomeIcons.check,
                                color: Colors.indigo,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Já Juntou!',
                                style: TextStyle(
                                  color: Colors.indigo,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: SizedBox(
                        height: 100,
                        child: ElevatedButton(
                          onPressed: () async {
                            Get.back();
                            isLoading(true);
                            try {
                              List<OrderModel> orders =
                                  await Get.find<ClientService>()
                                      .getOrdersFromClient(
                                          order.value.clientId);
                              var ordersToPay = orders
                                  .where((e) => !e.isPaid && !e.isJoined)
                                  .toList();
                              ordersToPay
                                  .removeWhere((e) => e.id == order.value.id);
                              ordersToPay.sort(
                                  (a, b) => a.date.compareTo(b.date) * -1);

                              await Get.dialog(
                                AlertDialog(
                                  title: const Text('Pedidos para juntar'),
                                  contentPadding: const EdgeInsets.all(8),
                                  content: SizedBox(
                                    height: 300,
                                    width: 300,
                                    child: ordersToPay.isNotEmpty
                                        ? ListView.builder(
                                            itemCount: ordersToPay.length,
                                            itemBuilder: (ctx, index) {
                                              var orderTemp =
                                                  ordersToPay[index];
                                              return Card(
                                                child: ListTile(
                                                  onTap: () async {
                                                    Get.toNamed('/view-order',
                                                        arguments: orderTemp);
                                                  },
                                                  contentPadding:
                                                      const EdgeInsets.all(8),
                                                  title: orderTemp.isToday
                                                      ? const Text(
                                                          'Pedido de Hoje',
                                                          style: TextStyle(
                                                            color:
                                                                Color.fromRGBO(
                                                                    76,
                                                                    175,
                                                                    80,
                                                                    1),
                                                            fontWeight:
                                                                FontWeight.bold,
                                                          ),
                                                        )
                                                      : Text(
                                                          'Pedido ${DateTimeHelper.getFormattedDate(orderTemp.date)}',
                                                        ),
                                                  subtitle: Text(
                                                    'Valor: R\$ ${orderTemp.getTotalPending().toStringAsFixed(2)}',
                                                  ),
                                                  trailing: ElevatedButton(
                                                    onPressed: () async {
                                                      isLoading(true);
                                                      try {
                                                        await markAsJoined(
                                                            orderJoined:
                                                                orderTemp);
                                                        Get.back();
                                                      } catch (e) {
                                                        Get.showSnackbar(
                                                          const GetSnackBar(
                                                            message:
                                                                'Erro ao juntar pedidos',
                                                            backgroundColor:
                                                                Colors.red,
                                                          ),
                                                        );
                                                      } finally {
                                                        isLoading(false);
                                                      }
                                                    },
                                                    style: ElevatedButton
                                                        .styleFrom(
                                                      backgroundColor:
                                                          Colors.indigo,
                                                      foregroundColor:
                                                          Colors.white,
                                                    ),
                                                    child: const Column(
                                                      mainAxisSize:
                                                          MainAxisSize.min,
                                                      children: [
                                                        Text('JUNTAR',
                                                            textAlign: TextAlign
                                                                .center),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          )
                                        : const Center(
                                            child: Text(
                                                'Nenhum pedido disponivel para juntar'),
                                          ),
                                  ),
                                  actions: [
                                    TextButton(
                                      child: Text(
                                        'Voltar',
                                        style: TextStyle(
                                            color: Get.isDarkMode
                                                ? Colors.white
                                                : Colors.black),
                                      ),
                                      onPressed: () {
                                        Get.back();
                                      },
                                    ),
                                  ],
                                ),
                              );
                            } catch (e) {
                              Get.showSnackbar(const GetSnackBar(
                                message: 'Erro exibir pedidos para juntar',
                                backgroundColor: Colors.red,
                              ));
                            } finally {
                              isLoading(false);
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.indigo,
                            foregroundColor: Colors.white,
                          ),
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                FontAwesomeIcons.fileImport,
                                color: Colors.white,
                              ),
                              SizedBox(height: 8),
                              Text('SELECIONAR PEDIDO',
                                  textAlign: TextAlign.center),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        ),
        actions: [
          TextButton(
            child: Text(
              'Voltar',
              style: TextStyle(
                  color: Get.isDarkMode ? Colors.white : Colors.black),
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
    );
  }

  Future<void> showUnjoinedDialog(BuildContext context) async {
    await Get.dialog(
      AlertDialog(
        backgroundColor: Colors.red[600],
        title: const Column(
          children: [
            Icon(
              FontAwesomeIcons.xmark,
              color: Colors.white,
              size: 50,
            ),
            SizedBox(height: 8),
            Text(
              'Desmarcar como junto com de outro pedido',
              style: TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            Text(
              'Lembre-se de remover o valor do pedido que foi juntado',
              style: TextStyle(color: Colors.white, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        content: SizedBox(
          height: 50,
          width: Get.width * 0.8,
          child: ActionSlider.standard(
            height: 50,
            sliderBehavior: SliderBehavior.stretch,
            backgroundColor: Colors.black,
            toggleColor: Colors.white,
            action: (actionController) async {
              actionController.success();
              await Future.delayed(const Duration(milliseconds: 500));
              await markAsUnjoined();
              Get.back();
            },
            child: const Text(
              'Desmarcar',
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
        actions: [
          TextButton(
            child: Text(
              'Voltar',
              style: TextStyle(
                  color: Get.isDarkMode ? Colors.white : Colors.black),
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ],
      ),
    );
  }

  void goToJoinedOrder() async {
    if (order.value.joinedInOrderId != null) {
      OrderModel? orderJoined =
          await _orderRepository.getOrder(order.value.joinedInOrderId!);
      await Get.toNamed('/view-order',
          arguments: orderJoined, preventDuplicates: false);
    }
  }

  void restoreOrder() async {
    order.value = await _orderRepository.restoreOrder(order.value);
    order.refresh();
  }
}
