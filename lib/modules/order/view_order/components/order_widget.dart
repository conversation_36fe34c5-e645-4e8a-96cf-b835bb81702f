import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher_string.dart';

class OrderWidget extends StatelessWidget {
  const OrderWidget(
    this.order, {
    super.key,
    this.showLogo = true,
    this.showClientButton = true,
    this.showClientPhone = true,
    this.showClientMap = true,
  });

  final OrderModel order;
  final bool showLogo;
  final bool showClientButton;
  final bool showClientPhone;
  final bool showClientMap;

  List<Widget> get getItems {
    List<Widget> items = [];
    bool painted = true;

    for (var element in order.products) {
      items.add(
        Container(
          padding: const EdgeInsets.all(8),
          color: painted
              ? (Get.isDarkMode
                  ? Colors.indigo[600]!.withOpacity(0.8)
                  : Colors.indigo.withOpacity(0.2))
              : (Get.isDarkMode
                  ? Colors.indigo[900]!.withOpacity(0.2)
                  : Colors.white),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${element.quantity} x ${element.name}',
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              Text(
                NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$ ')
                    .format(element.total),
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
      );
      painted = !painted;
    }

    if (order.remaining != 0) {
      items.add(
        Container(
          padding: const EdgeInsets.all(8),
          color: painted
              ? Colors.indigo.withOpacity(0.2)
              : (Get.isDarkMode ? Colors.grey[900] : Colors.white),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Restante',
                style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
              ),
              Text(
                NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$ ')
                    .format(order.remaining),
                style:
                    const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
              ),
            ],
          ),
        ),
      );
      painted = !painted;
    }

    if (order.payments.isNotEmpty) {
      for (var payment in order.payments) {
        String? paymentType = payment.paymentMethod?.name;
        Color backgroundColor = paymentType == 'dinheiro'
            ? Colors.green[700]!
            : paymentType == 'cartao'
                ? Colors.blue[700]!
                : paymentType == 'pix'
                    ? Colors.deepPurple[700]!
                    : Colors.green.withOpacity(0.2);

        Color textColor = paymentType == null ? Colors.black : Colors.white;

        items.add(
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: backgroundColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Text(
                      DateTimeHelper.getFormattedDate(payment.date),
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: textColor,
                      ),
                    ),
                    if (paymentType != null)
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(width: 5),
                          Text(
                            '-',
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: textColor,
                            ),
                          ),
                          const SizedBox(width: 5),
                          Icon(
                            paymentType == 'dinheiro'
                                ? FontAwesomeIcons.moneyBill
                                : paymentType == 'cartao'
                                    ? FontAwesomeIcons.creditCard
                                    : FontAwesomeIcons.pix,
                            color: textColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            paymentType,
                            style: TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: textColor,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                Text(
                  '- R\$ ${payment.value.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    return items;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Get.isDarkMode ? Colors.grey[900] : Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Get.isDarkMode ? Colors.indigo[900] : Colors.indigo[100],
            ),
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                const Text(
                  'Pedido',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 20,
                  ),
                ),
                Text(
                  DateFormat('dd/MM/yyyy HH:mm').format(order.date),
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Table(
              columnWidths: Map.from({0: const FixedColumnWidth(100)}),
              children: [
                TableRow(
                  children: [
                    TableCell(
                      child: Text(
                        'Cliente: ',
                        style: TextStyle(
                          color: Get.isDarkMode
                              ? Colors.grey[200]
                              : Colors.black87,
                          fontSize: 19,
                        ),
                      ),
                    ),
                    TableCell(
                      child: Row(
                        children: [
                          if (showClientButton)
                            Expanded(
                              child: Material(
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(5),
                                  onTap: () async {
                                    var cliente =
                                        await Get.find<ClientService>()
                                            .getClient(order.clientId);
                                    if (cliente != null) {
                                      Get.toNamed('/client_details',
                                          arguments: cliente);
                                    } else {
                                      Get.snackbar(
                                          'Erro', 'Cliente não encontrado');
                                    }
                                  },
                                  child: Text(
                                    order.clientName,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      fontSize: 20,
                                      color: Get.isDarkMode
                                          ? Colors.indigo[50]
                                          : Colors.indigo[900],
                                    ),
                                    overflow: TextOverflow.clip,
                                  ),
                                ),
                              ),
                            ),
                          if (!showClientButton)
                            Expanded(
                              child: InkWell(
                                onTap: () async {
                                  var cliente = await Get.find<ClientService>()
                                      .getClient(order.clientId);
                                  if (cliente != null) {
                                    Get.toNamed('/client_details',
                                        arguments: cliente);
                                  } else {
                                    Get.snackbar(
                                        'Erro', 'Cliente não encontrado');
                                  }
                                },
                                child: Text(
                                  order.clientName,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 20,
                                  ),
                                  overflow: TextOverflow.clip,
                                ),
                              ),
                            ),
                        ],
                      ),
                    )
                  ],
                ),
                //endereco
                TableRow(
                  children: [
                    TableCell(
                      child: Text(
                        'Endereço: ',
                        style: TextStyle(
                          color: Get.isDarkMode
                              ? Colors.grey[200]
                              : Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    TableCell(
                      child: Text(
                        '${order.clientAddress}${order.clientNumber != null && order.clientNumber != "" ? ', nº${order.clientNumber}' : ''}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
                //descricao local
                TableRow(
                  children: [
                    TableCell(
                      child: Text(
                        'Descrição: ',
                        style: TextStyle(
                          color: Get.isDarkMode
                              ? Colors.grey[200]
                              : Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    TableCell(
                      child: Text(
                        order.clientLocalDescription,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),

                if (order.clientPhone != null && order.clientPhone!.isNotEmpty)
                  TableRow(
                    children: [
                      TableCell(
                        child: Text(
                          'Telefone: ',
                          style: TextStyle(
                            color: Get.isDarkMode
                                ? Colors.grey[200]
                                : Colors.black87,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      TableCell(
                        child: Text(
                          order.clientPhone!,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),

                if (order.routeName != null)
                  TableRow(
                    children: [
                      TableCell(
                        child: Text(
                          'Rota: ',
                          style: TextStyle(
                            color: Get.isDarkMode
                                ? Colors.grey[200]
                                : Colors.black87,
                            fontSize: 16,
                          ),
                        ),
                      ),
                      TableCell(
                        child: Text(
                          order.routeName!,
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                //vendedor
                TableRow(
                  children: [
                    TableCell(
                      child: Text(
                        'Vendedor: ',
                        style: TextStyle(
                          color: Get.isDarkMode
                              ? Colors.grey[200]
                              : Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    TableCell(
                      child: Text(
                        order.sellerName,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if ((showClientMap &&
                  (order.clientLatitude != null) &&
                  (order.clientLongitude != null)) ||
              (showClientPhone &&
                  (order.clientPhone != null) &&
                  (order.clientPhone!.isNotEmpty)))
            Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (showClientMap &&
                      (order.clientLatitude != null) &&
                      (order.clientLongitude != null))
                    Expanded(
                      child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.indigo,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () async {
                          var client = await Get.find<ClientService>()
                              .getClient(order.clientId);
                          if (client != null) {
                            Get.toNamed('/client_location', arguments: client);
                          } else {
                            Get.showSnackbar(const GetSnackBar(
                                title: 'Erro',
                                message: 'Cliente não encontrado'));
                          }
                        },
                        icon: const Icon(Icons.map),
                        label: const Text('Mapa'),
                      ),
                    ),
                  if ((showClientMap &&
                          (order.clientLatitude != null) &&
                          (order.clientLongitude != null)) &&
                      (showClientPhone &&
                          (order.clientPhone != null) &&
                          (order.clientPhone!.isNotEmpty)))
                    const SizedBox(width: 8),
                  if (showClientPhone &&
                      (order.clientPhone != null) &&
                      (order.clientPhone!.isNotEmpty))
                    Expanded(
                      child: ElevatedButton.icon(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.indigo,
                          foregroundColor: Colors.white,
                        ),
                        onPressed: () async {
                          //Ligar para o cliente
                          String url = "tel:${order.clientPhone}";
                          if (await canLaunchUrlString(url)) {
                            await launchUrlString(url);
                          } else {
                            Get.showSnackbar(const GetSnackBar(
                                title: 'Erro',
                                message: 'Erro ao ligar para o cliente'));
                          }
                        },
                        icon: const Icon(Icons.phone),
                        label: const Text('Ligar'),
                      ),
                    ),
                ],
              ),
            ),
          ...getItems,
          const Divider(thickness: 1.2),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 18,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    color: Colors.indigo,
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Text(
                    NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$ ')
                        .format(order.calculateTotal()),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.95),
                      fontWeight: FontWeight.w600,
                      fontSize: 18,
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (order.payments.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        '- ${order.payments.fold(0.0, (previousValue, payment) => previousValue + payment.value).toStringAsFixed(2)}',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Restante a pagar',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                        ),
                      ),
                      Text(
                        'R\$ ${(order.calculateTotal() - order.payments.fold(0, (previousValue, payment) => previousValue + payment.value)).toStringAsFixed(2)}',
                        style: TextStyle(
                          color: Get.isDarkMode
                              ? Colors.grey[200]
                              : Colors.black.withOpacity(0.95),
                          fontWeight: FontWeight.w600,
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          const Divider(thickness: 1.2),
          if (showLogo)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 80, vertical: 5),
              child: Image.asset(
                Get.isDarkMode
                    ? 'assets/images/logo_branca_horizontal.png'
                    : 'assets/images/logo_azul_horizontal.png',
                height: 60,
                cacheHeight: 80,
                cacheWidth: 300,
              ),
            ),
          Text(
            'Id: ${order.id ?? 'Sem Id'}',
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (!DateTimeHelper.isSameDateDateTime(
              order.date, order.lastModified))
            Text(
              'Modificado em: ${DateFormat('dd/MM/yyyy HH:mm').format(order.lastModified)}',
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
          if (order.routeSaleId != null)
            Text(
              'Rota de venda: ${Get.find<SalesRoutesService>().getRouteById(order.routeSaleId!)?.name ?? order.routeId}',
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }
}
