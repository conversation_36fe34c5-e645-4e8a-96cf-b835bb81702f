import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/modules/order/order_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FilterOptionsBottomSheet extends StatelessWidget {
  final OrderController controller;

  const FilterOptionsBottomSheet({super.key, required this.controller});

  String _getPaymentFilterTypeName(PaymentFilterType type) {
    switch (type) {
      case PaymentFilterType.all:
        return 'Todos';
      case PaymentFilterType.paid:
        return 'Pagos';
      case PaymentFilterType.unpaid:
        return 'Não Pagos';
      default:
        return '';
    }
  }

  Future<void> _selectDate(
      BuildContext context, Rx<DateTime> dateVariable) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: dateVariable.value,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != dateVariable.value) {
      dateVariable.value = picked;
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Filtros',
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),

              // Filtro de Rota
              DropdownButtonFormField<String>(
                value: controller.routeId.value == ""
                    ? null
                    : controller.routeId.value,
                decoration: const InputDecoration(labelText: 'Rota'),
                isExpanded: true,
                items: [
                  const DropdownMenuItem<String>(
                    value: null, // Representa "Todas as rotas"
                    child: Text('Todas as Rotas'),
                  ),
                  ...controller.routes.map((SaleRouteModel route) {
                    return DropdownMenuItem<String>(
                      value: route.id,
                      child: Text(route.name),
                    );
                  }),
                ],
                onChanged: (String? newValue) {
                  controller.routeId.value = newValue ?? "";
                },
              ),
              const SizedBox(height: 16),

              // Filtro de Vendedor
              DropdownButtonFormField<String>(
                value: controller.sallerId.value == ""
                    ? null
                    : controller.sallerId.value,
                decoration: const InputDecoration(labelText: 'Vendedor'),
                isExpanded: true,
                items: [
                  const DropdownMenuItem<String>(
                    value: null, // Representa "Todos os vendedores"
                    child: Text('Todos os Vendedores'),
                  ),
                  ...controller.users.map((UserModel user) {
                    return DropdownMenuItem<String>(
                      value: user.id,
                      child: Text(user.name),
                    );
                  }),
                ],
                onChanged: (String? newValue) {
                  controller.sallerId.value = newValue ?? "";
                },
              ),
              const SizedBox(height: 16),

              // Filtro de Status de Pagamento
              Text('Status do Pagamento',
                  style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: 8),
              SegmentedButton<PaymentFilterType>(
                segments: PaymentFilterType.values.map((type) {
                  return ButtonSegment<PaymentFilterType>(
                    value: type,
                    label: Text(_getPaymentFilterTypeName(type)),
                    icon: Icon(type == PaymentFilterType.all
                        ? Icons.list_alt_rounded
                        : type == PaymentFilterType.paid
                            ? Icons.check_circle_outline_rounded
                            : Icons.hourglass_empty_rounded),
                  );
                }).toList(),
                selected: <PaymentFilterType>{controller.paymentFilter.value},
                onSelectionChanged: (Set<PaymentFilterType> newSelection) {
                  controller.paymentFilter.value = newSelection.first;
                },
                style: SegmentedButton.styleFrom(
                  minimumSize: const Size(0, 40), // Ajuste de altura
                ),
              ),
              const SizedBox(height: 16),

              // Filtro "Para Entrega"
              SwitchListTile(
                title: Text('Apenas pedidos para entrega',
                    style: Theme.of(context)
                        .textTheme
                        .titleMedium
                        ?.copyWith(fontWeight: FontWeight.normal)),
                value: controller.showToDelivery.value,
                onChanged: (bool value) {
                  controller.showToDelivery.value = value;
                },
                contentPadding: EdgeInsets.zero,
              ),
              const SizedBox(height: 16),

              // Filtro de Data
              Text('Data do Pedido',
                  style: Theme.of(context).textTheme.titleMedium),
              SwitchListTile(
                title: Text('Usar intervalo de datas',
                    style: Theme.of(context)
                        .textTheme
                        .titleMedium
                        ?.copyWith(fontWeight: FontWeight.normal)),
                value: controller.isDateRange.value,
                onChanged: (bool value) {
                  controller.isDateRange.value = value;
                },
                contentPadding: EdgeInsets.zero,
              ),
              if (controller.isDateRange.value)
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.calendar_today),
                        label: Text(DateTimeHelper.getFormattedDate(
                            controller.initialDate.value)),
                        onPressed: () =>
                            _selectDate(context, controller.initialDate),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const Text("até"),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.calendar_today),
                        label: Text(DateTimeHelper.getFormattedDate(
                            controller.finalDate.value)),
                        onPressed: () =>
                            _selectDate(context, controller.finalDate),
                      ),
                    ),
                  ],
                )
              else
                ElevatedButton.icon(
                  icon: const Icon(Icons.calendar_today),
                  label: Text(
                      'Data: ${DateTimeHelper.getFormattedDate(controller.selectedDate.value)}'),
                  onPressed: () =>
                      _selectDate(context, controller.selectedDate),
                  style: ElevatedButton.styleFrom(
                      minimumSize: const Size(double.infinity, 36)),
                ),
              const SizedBox(height: 24),

              // Botões de Ação
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    child: const Text('Limpar Filtros'),
                    onPressed: () {
                      controller.clearFilters();
                      Get.back(); // Fecha o BottomSheet
                    },
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.check_rounded),
                    label: const Text('Aplicar Filtros'),
                    onPressed: () {
                      controller.filterOrders();
                      Get.back(); // Fecha o BottomSheet
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
