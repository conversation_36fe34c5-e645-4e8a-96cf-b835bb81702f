import 'dart:async';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/modules/order/sale_billing/sale_billing_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class BillingByMapController extends GetxController {
  final _salesBillingController = Get.find<SaleBillingController>();

  final Completer<GoogleMapController> _mapController = Completer();
  String? _darkMapStyle;
  final RxBool isCameraPositionLoaded = false.obs;
  RxBool isSatelliteView = false.obs;
  CameraPosition? cameraPosition;
  final RxDouble currentZoom = 20.0.obs;
  final RxDouble currentTilt = 0.0.obs;
  final RxSet<Marker> markers = <Marker>{}.obs;

  final RxBool isFollowUserLocation = false.obs;

  StreamSubscription<Position>? userLocationStream;
  double lastBearing = 0.0;

  SaleBillingController get salesBillingController => _salesBillingController;

  @override
  void onInit() {
    super.onInit();
    _loadMapStyles();
    updateMap();
  }

  @override
  void onClose() {
    userLocationStream?.cancel();
    super.onClose();
  }

  Future<void> _loadMapStyles() async {
    // Caso deseje usar um estilo customizado para modo dark
    try {
      _darkMapStyle = await rootBundle.loadString('assets/json/map_dark.json');
    } catch (e) {
      // ignore
    }
  }

  void onMapCreated(GoogleMapController controller) {
    if (!_mapController.isCompleted) {
      _mapController.complete(controller);
    }
    // Se desejar aplicar o estilo dark ao mapa
    if (_darkMapStyle != null && Get.isDarkMode) {
      controller.setMapStyle(_darkMapStyle!);
    }
  }

  Future<void> updateMap() async {
    final pendingOrdersClient =
        salesBillingController.groupedOrders.entries.toList();

    markers.clear();

    for (final client in pendingOrdersClient) {
      var order = client.value.first;

      if (order.clientLatitude == null || order.clientLongitude == null) {
        continue;
      }

      final hasDebt = !order.isPaid && !order.isJoined;

      final BitmapDescriptor icon = await _createMarkerIcon(
        order.clientName,
        hasDebt,
      );

      markers.add(
        Marker(
          markerId: MarkerId(order.clientId),
          position: LatLng(order.clientLatitude!.toDouble(),
              order.clientLongitude!.toDouble()),
          icon: icon,
          infoWindow: InfoWindow(
            title: order.clientName,
            snippet: order.fullAddress,
            onTap: () async {
              if (client.value.length == 1) {
                await salesBillingController.goToOrderDetails(order);
                updateMarker(order.clientId);
              } else {
                var client = salesBillingController.allClientsInRoutes
                    .firstWhereOrNull(
                        (element) => element.id == order.clientId);
                if (client != null) {
                  await salesBillingController.goToClientDetails(client);
                  updateMarker(client.id!);
                }
              }
            },
          ),
        ),
      );
    }

    // adiciona os clientes que não tem pedidos
    var allClients = salesBillingController.allClientsInRoutes;
    for (var client in allClients) {
      if (pendingOrdersClient.any((element) => element.key == client.id)) {
        continue;
      }
      if (client.latitude == null || client.longitude == null) {
        continue;
      }

      const hasDebt = false;
      final BitmapDescriptor icon = await _createMarkerIcon(
        client.name,
        hasDebt,
      );

      markers.add(
        Marker(
          markerId: MarkerId(client.id!),
          position:
              LatLng(client.latitude!.toDouble(), client.longitude!.toDouble()),
          icon: icon,
          infoWindow: InfoWindow(
            title: client.name,
            snippet: client.fullAddress,
            onTap: () async {
              await salesBillingController.goToClientDetails(client);
              updateMarker(client.id!);
            },
          ),
        ),
      );
    }

    // Se quiser centralizar o mapa na posição média ou no local do usuário:
    // 1) Tentar usar a localização atual do usuário
    // 2) Se não tiver, usar a média dos pedidos
    final userLocation = _salesBillingController.location;
    if (userLocation != null) {
      cameraPosition = CameraPosition(
        target: LatLng(userLocation.latitude, userLocation.longitude),
        zoom: currentZoom.value,
        tilt: currentTilt.value,
      );
    } else {
      final avgLatLng = _calculateAveragePosition(salesBillingController
          .groupedOrders.values
          .expand((e) => e)
          .toList());
      if (avgLatLng != null) {
        cameraPosition =
            CameraPosition(target: avgLatLng, zoom: currentZoom.value);
      } else {
        // Fallback para algo fixo, ex. São Paulo
        cameraPosition = CameraPosition(
          target: const LatLng(-23.5505, -46.6333),
          zoom: currentZoom.value,
          tilt: currentTilt.value,
        );
      }
    }

    // Indicar que a posição da câmera está carregada
    isCameraPositionLoaded.value = true;
  }

  void updateMarker(String clientId) async {
    final marker = markers
        .toList()
        .firstWhereOrNull((element) => element.markerId.value == clientId);
    if (marker != null) {
      bool contains =
          salesBillingController.groupedOrders.containsKey(clientId);
      var name =
          salesBillingController.groupedOrders[clientId]?.first.clientName;
      var newMarker = Marker(
        markerId: marker.markerId,
        position: marker.position,
        icon: await _createMarkerIcon(name ?? '', contains),
        infoWindow: InfoWindow(
          title: name,
          snippet: marker.infoWindow.snippet,
          onTap: marker.infoWindow.onTap,
        ),
      );

      var oldMarkers = markers.toList();
      oldMarkers.remove(marker);
      markers.clear();
      markers.addAll(oldMarkers);
      markers.add(newMarker);
    }
  }

  Future<BitmapDescriptor> _createMarkerIcon(String text, bool hasDebt) async {
    final Color color = hasDebt ? Colors.red : Colors.blue;
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // Definir o tamanho do triângulo (setinha):
    const double arrowHeight = 10;
    const double arrowWidth = 16;

    // Pintura de fundo
    final backgroundPaint = Paint()..color = color;

    // Borda arredondada para o retângulo
    final borderRadius = BorderRadius.circular(10);

    // Largura e altura do ícone (sem contar a setinha)
    double iconWidth;
    double iconHeight;

    if (hasDebt) {
      // Calcula tamanho baseado no texto
      final textPainter = TextPainter(
        text: TextSpan(
          text: text,
          style: const TextStyle(
            fontSize: 24,
            color: Colors.white,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();

      iconWidth = textPainter.width + 20;
      iconHeight = textPainter.height + 20;

      // A altura total do ícone é a soma da caixa + a setinha
      final totalHeight = iconHeight + arrowHeight;

      // Desenhar o retângulo arredondado (sem a parte da setinha)
      final rRect = RRect.fromRectAndCorners(
        Rect.fromLTWH(0, 0, iconWidth, iconHeight),
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );
      canvas.drawRRect(rRect, backgroundPaint);

      // Desenhar o texto
      textPainter.paint(
        canvas,
        Offset(10, (iconHeight - textPainter.height) / 2),
      );

      // Desenhar a setinha na parte de baixo
      final arrowPath = Path()
        ..moveTo(iconWidth / 2 - arrowWidth / 2, iconHeight)
        ..lineTo(iconWidth / 2, totalHeight)
        ..lineTo(iconWidth / 2 + arrowWidth / 2, iconHeight)
        ..close();

      canvas.drawPath(arrowPath, backgroundPaint);

      // Encerrar a imagem usando o 'totalHeight'
      final picture = recorder.endRecording();
      final image = await picture.toImage(
        iconWidth.toInt(),
        totalHeight.toInt(),
      );
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      return BitmapDescriptor.fromBytes(byteData!.buffer.asUint8List());
    } else {
      // Para o caso sem texto
      iconWidth = 30;
      iconHeight = 30;
      final totalHeight = iconHeight + arrowHeight;

      // Desenhar o retângulo arredondado
      final rRect = RRect.fromRectAndCorners(
        Rect.fromLTWH(0, 0, iconWidth, iconHeight),
        topLeft: borderRadius.topLeft,
        topRight: borderRadius.topRight,
        bottomLeft: borderRadius.bottomLeft,
        bottomRight: borderRadius.bottomRight,
      );
      canvas.drawRRect(rRect, backgroundPaint);

      // Desenhar a setinha na parte de baixo
      final arrowPath = Path()
        ..moveTo(iconWidth / 2 - arrowWidth / 2, iconHeight)
        ..lineTo(iconWidth / 2, totalHeight)
        ..lineTo(iconWidth / 2 + arrowWidth / 2, iconHeight)
        ..close();

      canvas.drawPath(arrowPath, backgroundPaint);

      // Encerrar a imagem usando o 'totalHeight'
      final picture = recorder.endRecording();
      final image = await picture.toImage(
        iconWidth.toInt(),
        totalHeight.toInt(),
      );
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      return BitmapDescriptor.fromBytes(byteData!.buffer.asUint8List());
    }
  }

  double calculateDistanceSync(OrderModel order, Position userLocation) {
    if (order.clientLatitude == null || order.clientLongitude == null) {
      return double.maxFinite;
    }
    final distance = Geolocator.distanceBetween(
      userLocation.latitude,
      userLocation.longitude,
      order.clientLatitude!.toDouble(),
      order.clientLongitude!.toDouble(),
    );
    return distance;
  }

  void updateCurrentZoom(double zoom) {
    currentZoom.value = zoom;
  }

  void updateCurrentTilt(double tilt) {
    currentTilt.value = tilt;
  }

  void zoomIn() {
    currentZoom.value = currentZoom.value + 1;
    _updateCameraZoom();
  }

  void zoomOut() {
    if (currentZoom.value > 1) {
      currentZoom.value = currentZoom.value - 1;
      _updateCameraZoom();
    }
  }

  void _updateCameraZoom() {
    final cameraPos = CameraPosition(
      target: cameraPosition!.target,
      zoom: currentZoom.value,
      tilt: currentTilt.value,
    );
    _mapController.future.then(
      (controller) =>
          controller.animateCamera(CameraUpdate.newCameraPosition(cameraPos)),
    );
  }

  void updateCameraTilt(double value) {
    currentTilt.value = value;
    _updateCameraTilt();
  }

  void _updateCameraTilt() {
    final cameraPos = CameraPosition(
      target: cameraPosition!.target,
      zoom: currentZoom.value,
      tilt: currentTilt.value,
    );
    _mapController.future.then(
      (controller) =>
          controller.animateCamera(CameraUpdate.newCameraPosition(cameraPos)),
    );
  }

  /// Calcula a posição média (centroide) de todos os pedidos
  LatLng? _calculateAveragePosition(List<OrderModel> orders) {
    if (orders.isEmpty) return null;

    double sumLatitude = 0.0;
    double sumLongitude = 0.0;
    int count = 0;

    for (var order in orders) {
      if (order.clientLatitude != null && order.clientLongitude != null) {
        sumLatitude += order.clientLatitude!;
        sumLongitude += order.clientLongitude!;
        count++;
      }
    }

    if (count == 0) return null;

    return LatLng(sumLatitude / count, sumLongitude / count);
  }

  /// Lógica de "seguir" a localização do usuário
  void toggleFollowUserLocation() async {
    isFollowUserLocation.value = !isFollowUserLocation.value;
    Position? lastPosition;
    if (isFollowUserLocation.value) {
      final stream = Geolocator.getPositionStream().listen((position) {
        if (isFollowUserLocation.value) {
          double bearing = lastBearing;
          if (lastPosition != null) {
            final distance = Geolocator.distanceBetween(
              lastPosition!.latitude,
              lastPosition!.longitude,
              position.latitude,
              position.longitude,
            );
            if (distance > 1) {
              bearing = calculateBearing(
                lastPosition!.latitude,
                lastPosition!.longitude,
                position.latitude,
                position.longitude,
              );
              lastBearing = bearing;
            }
          }

          final cameraPos = CameraPosition(
            target: LatLng(position.latitude, position.longitude),
            zoom: currentZoom.value,
            tilt: currentTilt.value,
            bearing: bearing,
          );
          _mapController.future.then(
            (controller) => controller
                .animateCamera(CameraUpdate.newCameraPosition(cameraPos)),
          );
          lastPosition = position;
        }
      });
      userLocationStream = stream;
    } else {
      userLocationStream?.cancel();
    }
  }

  double calculateBearing(
      double lat1, double long1, double lat2, double long2) {
    double dLon = (long2 - long1);
    double y = sin(dLon) * cos(lat2);
    double x = cos(lat1) * sin(lat2) -
        sin(lat1) *
            cos(lat2) *
            cos(dLon); // cos(lat1)*sin(lat2) - sin(lat1)*cos(lat2)*cos(dLon)
    double brng = atan2(y, x); // atan2(y, x)
    brng = brng * 180 / pi;
    brng = (brng + 360) % 360;
    brng = 360 -
        brng; // count degrees counter-clockwise - remove to make clockwise
    return brng;
  }

  Future<double> calculateDistance(OrderModel order) async {
    final userLocation = _salesBillingController.location;
    if (userLocation == null ||
        order.clientLatitude == null ||
        order.clientLongitude == null) {
      return 0.0;
    }
    final distance = Geolocator.distanceBetween(
      userLocation.latitude,
      userLocation.longitude,
      order.clientLatitude!.toDouble(),
      order.clientLongitude!.toDouble(),
    );

    return distance;
  }

  void goToClientPosition(OrderModel order) {
    if (order.clientLatitude == null || order.clientLongitude == null) {
      return;
    }
    final clientPosition = LatLng(
        order.clientLatitude!.toDouble(), order.clientLongitude!.toDouble());
    final cameraPos = CameraPosition(
      target: clientPosition,
      zoom: currentZoom.value,
      tilt: currentTilt.value,
    );
    _mapController.future.then(
      (controller) =>
          controller.animateCamera(CameraUpdate.newCameraPosition(cameraPos)),
    );
  }
}
