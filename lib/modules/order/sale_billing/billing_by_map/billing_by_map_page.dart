import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import './billing_by_map_controller.dart';

class BillingByMapPage extends GetView<BillingByMapController> {
  const BillingByMapPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          title: const Text('Mapa de Pedidos Pendentes'),
          actions: [
            IconButton(
              onPressed: () => controller.isSatelliteView.toggle(),
              icon: Icon(
                controller.isSatelliteView.value ? Icons.map : Icons.satellite,
              ),
            ),
            IconButton(
              onPressed: controller.toggleFollowUserLocation,
              icon: Icon(
                controller.isFollowUserLocation.value
                    ? Icons.navigation_rounded
                    : Icons.navigation_outlined,
                color: controller.isFollowUserLocation.value
                    ? Colors.blue
                    : Colors.grey,
              ),
            ),
          ],
        ),
        body: Stack(
          children: [
            controller.isCameraPositionLoaded.value
                ? Obx(
                    () => GoogleMap(
                      onMapCreated: controller.onMapCreated,
                      initialCameraPosition: CameraPosition(
                        target: controller.cameraPosition!.target,
                        zoom: controller.currentZoom.value,
                      ),
                      mapType: controller.isSatelliteView.value
                          ? MapType.satellite
                          : MapType.normal,
                      markers: controller.markers.value,
                      myLocationEnabled: true,
                      myLocationButtonEnabled: true,
                      zoomControlsEnabled: false,
                      zoomGesturesEnabled: true,
                      onCameraMove: (cameraPosition) {
                        controller.updateCurrentZoom(cameraPosition.zoom);
                        controller.updateCurrentTilt(cameraPosition.tilt);
                      },
                    ),
                  )
                : const Center(
                    child: CircularProgressIndicator(),
                  ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 8,
              child: SizedBox(
                height: 120,
                child: Obx(
                  () => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount:
                        controller.salesBillingController.groupedOrders.length,
                    itemBuilder: (context, index) {
                      final order = controller
                          .salesBillingController.groupedOrders.entries
                          .toList()[index]
                          .value
                          .first;

                      if (order.clientLatitude == null ||
                          order.clientLongitude == null) {
                        return const SizedBox.shrink();
                      }
                      return InkWell(
                        onTap: () => controller.goToClientPosition(order),
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: BoxDecoration(
                            color: Get.theme.scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Get.theme.colorScheme.primary
                                    .withAlpha(100),
                                blurRadius: 2,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            border: Border.all(
                              color:
                                  Get.theme.colorScheme.primary.withAlpha(100),
                              width: 1,
                            ),
                          ),
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                order.clientName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 15,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '${order.clientAddress}${order.clientNumber != null && order.clientNumber != '' ? ', ${order.clientNumber}' : ''}',
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w500),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                order.clientLocalDescription,
                                textAlign: TextAlign.center,
                                style: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w500),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 8),
                              if (order.clientLatitude != null &&
                                  order.clientLongitude != null)
                                FutureBuilder<double>(
                                  future: controller.calculateDistance(order),
                                  builder: (context, snapshot) {
                                    if (snapshot.hasData) {
                                      return Text(
                                        '${snapshot.data!.toStringAsFixed(0)} m',
                                        style: const TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500),
                                      );
                                    } else {
                                      return const Text('Calculando...',
                                          style: TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500));
                                    }
                                  },
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
