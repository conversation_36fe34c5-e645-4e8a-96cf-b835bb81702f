import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_map/billing_by_map_bindings.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_map/billing_by_map_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class BillingByMapModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/billing-by-map',
      page: () => const BillingByMapPage(),
      binding: BillingByMapBindings(),
    ),
  ];
}
