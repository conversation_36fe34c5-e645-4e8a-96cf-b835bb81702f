import 'dart:async';
import 'dart:developer';

import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/modules/order/sale_billing/sale_billing_page.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

class SaleBillingController extends GetxController {
  final ClientRepository _clientRepository;
  final OrderRepository _orderRepository;

  SaleBillingController({
    required ClientRepository clientRepository,
    required OrderRepository orderRepository,
  })  : _clientRepository = clientRepository,
        _orderRepository = orderRepository;

  AppState appState = Get.arguments as AppState;

  SaleRouteModel? saleRoute = Get.find<SalesRoutesService>()
      .getRouteById((Get.arguments as AppState).isSellingRouteId!);

  Rxn<UserModel?> user = Rxn<UserModel?>();

  RxList<MapEntry<ClientModel, List<OrderModel>>> clientsOrdersList =
      <MapEntry<ClientModel, List<OrderModel>>>[].obs;

  RxList<MapEntry<ClientModel, List<OrderModel>>> clientsFiltered =
      <MapEntry<ClientModel, List<OrderModel>>>[].obs;

  RxList<String> clientsArchive = <String>[].obs;
  Box? boxArchive;

  RxString search = ''.obs;
  RxBool isSearching = false.obs;
  FocusNode searchFocus = FocusNode();

  RxBool isLoading = false.obs;

  TextEditingController searchController = TextEditingController();
  final workers = <Worker>[];

  Position? location;

  RxDouble receivedValue = 0.0.obs;
  RxDouble receivedValueDinheiro = 0.0.obs;
  RxDouble receivedValueCartao = 0.0.obs;
  RxDouble receivedValuePix = 0.0.obs;

  RxList<MapEntry<DateTime, OrderModel>> ordersDinheiro =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersCartao =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersPix =
      <MapEntry<DateTime, OrderModel>>[].obs;

  // Valores para pedidos antigos (antes de 15/05/25)
  RxDouble oldReceivedValue = 0.0.obs;
  RxDouble oldReceivedValueDinheiro = 0.0.obs;
  RxDouble oldReceivedValueCartao = 0.0.obs;
  RxDouble oldReceivedValuePix = 0.0.obs;

  RxList<MapEntry<DateTime, OrderModel>> oldOrdersDinheiro =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> oldOrdersCartao =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> oldOrdersPix =
      <MapEntry<DateTime, OrderModel>>[].obs;

  // Valores para pedidos novos (depois de 15/05/25)
  RxDouble newReceivedValue = 0.0.obs;
  RxDouble newReceivedValueDinheiro = 0.0.obs;
  RxDouble newReceivedValueCartao = 0.0.obs;
  RxDouble newReceivedValuePix = 0.0.obs;

  RxList<MapEntry<DateTime, OrderModel>> newOrdersDinheiro =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> newOrdersCartao =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> newOrdersPix =
      <MapEntry<DateTime, OrderModel>>[].obs;

  RxMap<String, List<OrderModel>> groupedOrders = RxMap({});

  @override
  void onInit() {
    super.onInit();
    init();
  }

  init() async {
    user.value = await Get.find<UserService>().getUserAuthenticated();
    refreshPending();
    refreshClientsInRoutes();
    loadReceivedValue();

    startTimer();
    boxArchive = await Hive.openBox('saleClientsArchive');
    var dateArchive = boxArchive!.get('dateArchive');
    if (dateArchive == null) {
      await boxArchive!.clear();
      await boxArchive!.put('dateArchive', DateTime.now());
    } else if (dateArchive != null &&
        !DateTimeHelper.isSameDay(dateArchive, DateTime.now())) {
      await boxArchive!.clear();
      await boxArchive!.put('dateArchive', DateTime.now());
    } else {
      clientsArchive.value =
          boxArchive!.get('clientsArchive', defaultValue: <String>[]);
    }
  }

  void markClientToNotShow(String? id) {
    if (id != null) {
      clientsArchive.add(id);
      boxArchive!.put('clientsArchive', clientsArchive.toList());
      log('ordersArchive: $clientsArchive');
    }
  }

  void markClientToShow(String? id) {
    if (id != null) {
      clientsArchive.remove(id);
      boxArchive!.put('clientsArchive', clientsArchive.toList());
      log('clientsArchive: $clientsArchive');
    }
  }

  void openModalClientsArchive() async {
    // open bottom sheet with ordersArchive
    await Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          border: Border(
            top: BorderSide(
              color: Get.theme.colorScheme.primary.withOpacity(0.8),
              width: 3,
            ),
          ),
        ),
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'Clientes separados:',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: Obx(
                () => ListView.builder(
                  itemCount: groupedOrders.entries
                      .where((e) => clientsArchive.contains(e.key))
                      .length,
                  itemBuilder: (context, index) {
                    var clientGroup = groupedOrders.entries
                        .where((e) => clientsArchive.contains(e.key))
                        .toList()[index];
                    var order = clientGroup.value.first;

                    return Slidable(
                        closeOnScroll: true,
                        startActionPane: ActionPane(
                          motion: const ScrollMotion(),
                          children: [
                            SlidableAction(
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(10),
                                topLeft: Radius.circular(10),
                              ),
                              icon: Icons.visibility,
                              label: 'Mostrar',
                              onPressed: (context) {
                                markClientToShow(order.clientId);
                                if (groupedOrders.entries
                                    .where(
                                        (e) => clientsArchive.contains(e.key))
                                    .isEmpty) {
                                  Get.back();
                                }
                              },
                              backgroundColor:
                                  Theme.of(context).colorScheme.secondary,
                              foregroundColor: Colors.white,
                            ),
                          ],
                        ),
                        child: SaleBillingClientItem(
                          order: order,
                          controller: this,
                          orders: clientGroup.value,
                          location: location,
                          onTap: () async => onClientTap(order),
                        ));
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchClient(),
        time: const Duration(milliseconds: 500),
      ),
    );
    super.onReady();
  }

  @override
  void dispose() {
    _timer?.cancel();
    for (var worker in workers) {
      worker.dispose();
    }
    super.dispose();
  }

  Timer? _timer;

  void startTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer.periodic(const Duration(seconds: 30), (timer) {
      refreshPending();
      loadReceivedValue();
    });
  }

  void stopTimer() {
    _timer?.cancel();
  }

  Future<void> refreshPending() async {
    isLoading.value = true;
    await refreshLocation();
    DateTime now = DateTime.now();
    now = DateTime(now.year, now.month, now.day);
    List<OrderModel> orders =
        await _orderRepository.getOrdersFromCache(routeId: saleRoute!.id);

    orders.removeWhere((order) =>
        order.isPaid ||
        order.isJoined ||
        order.isToday ||
        order.dayMarkingItems.any(
          (element) =>
              element.active &&
              (DateTimeHelper.isSameDay(element.createdAt, DateTime.now()) ||
                  (element.dayToVisit?.isAfter(DateTime.now()) != null &&
                      element.dayToVisit!.isAfter(DateTime.now()))),
        ));

    final geolocationService = Get.find<GeolocationService>();
    final location = await geolocationService.getCurrentLocation();
    orders.sort((a, b) {
      if ((a.clientLongitude == null || a.clientLatitude == null) &&
          (b.clientLongitude == null || b.clientLatitude == null)) {
        return 0;
      } else if ((a.clientLongitude == null || a.clientLatitude == null) &&
          (b.clientLongitude != null && b.clientLatitude != null)) {
        return -1;
      } else if ((a.clientLongitude != null && a.clientLatitude != null) &&
          (b.clientLongitude == null || b.clientLatitude == null)) {
        return 1;
      }

      final distanceA = a.distanceTo(location);
      final distanceB = b.distanceTo(location);
      if (distanceA == null || distanceB == null) {
        return -1;
      }
      return distanceA.compareTo(distanceB);
    });

    final Map<String, List<OrderModel>> map = {};

    for (var order in orders) {
      map.putIfAbsent(order.clientId, () => []).add(order);
    }
    groupedOrders.assignAll(map);

    isLoading(false);
    loadReceivedValue();
  }

  Future<void> loadReceivedValue() async {
    List<String> routes = saleRoute?.id != null ? [saleRoute!.id!] : [];
    var ordersReceived = await _orderRepository.getOrdersReceived(
      routes: routes,
      date: DateTime.now(),
    );

    // Limpar todos os valores
    receivedValue.value = 0.0;
    receivedValueDinheiro.value = 0.0;
    receivedValueCartao.value = 0.0;
    receivedValuePix.value = 0.0;

    oldReceivedValue.value = 0.0;
    oldReceivedValueDinheiro.value = 0.0;
    oldReceivedValueCartao.value = 0.0;
    oldReceivedValuePix.value = 0.0;

    newReceivedValue.value = 0.0;
    newReceivedValueDinheiro.value = 0.0;
    newReceivedValueCartao.value = 0.0;
    newReceivedValuePix.value = 0.0;

    // Limpar listas
    ordersDinheiro.clear();
    ordersCartao.clear();
    ordersPix.clear();

    oldOrdersDinheiro.clear();
    oldOrdersCartao.clear();
    oldOrdersPix.clear();

    newOrdersDinheiro.clear();
    newOrdersCartao.clear();
    newOrdersPix.clear();

    for (var orderTemp in ordersReceived) {
      for (var payment in orderTemp.payments) {
        if (payment.paymentMethod != null) {
          // Verificar se é pagamento de hoje para valores totais
          if (DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
            receivedValue.value += payment.value;
            if (payment.paymentMethod == PaymentMethod.dinheiro) {
              receivedValueDinheiro.value += payment.value;
              if (!ordersDinheiro.map((e) => e.value).contains(orderTemp)) {
                ordersDinheiro.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.cartao) {
              receivedValueCartao.value += payment.value;
              if (!ordersCartao.map((e) => e.value).contains(orderTemp)) {
                ordersCartao.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.pix) {
              receivedValuePix.value += payment.value;
              if (!ordersPix.map((e) => e.value).contains(orderTemp)) {
                ordersPix.add(MapEntry(payment.date, orderTemp));
              }
            }
          }

          // Separar por data de referência usando a propriedade isOlder do modelo
          if (orderTemp.isOlder) {
            // Pedidos antigos (antes de 15/05/25)
            if (DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
              oldReceivedValue.value += payment.value;
              if (payment.paymentMethod == PaymentMethod.dinheiro) {
                oldReceivedValueDinheiro.value += payment.value;
                if (!oldOrdersDinheiro
                    .map((e) => e.value)
                    .contains(orderTemp)) {
                  oldOrdersDinheiro.add(MapEntry(payment.date, orderTemp));
                }
              } else if (payment.paymentMethod == PaymentMethod.cartao) {
                oldReceivedValueCartao.value += payment.value;
                if (!oldOrdersCartao.map((e) => e.value).contains(orderTemp)) {
                  oldOrdersCartao.add(MapEntry(payment.date, orderTemp));
                }
              } else if (payment.paymentMethod == PaymentMethod.pix) {
                oldReceivedValuePix.value += payment.value;
                if (!oldOrdersPix.map((e) => e.value).contains(orderTemp)) {
                  oldOrdersPix.add(MapEntry(payment.date, orderTemp));
                }
              }
            }
          } else {
            // Pedidos novos (depois de 15/05/25)
            if (DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
              newReceivedValue.value += payment.value;
              if (payment.paymentMethod == PaymentMethod.dinheiro) {
                newReceivedValueDinheiro.value += payment.value;
                if (!newOrdersDinheiro
                    .map((e) => e.value)
                    .contains(orderTemp)) {
                  newOrdersDinheiro.add(MapEntry(payment.date, orderTemp));
                }
              } else if (payment.paymentMethod == PaymentMethod.cartao) {
                newReceivedValueCartao.value += payment.value;
                if (!newOrdersCartao.map((e) => e.value).contains(orderTemp)) {
                  newOrdersCartao.add(MapEntry(payment.date, orderTemp));
                }
              } else if (payment.paymentMethod == PaymentMethod.pix) {
                newReceivedValuePix.value += payment.value;
                if (!newOrdersPix.map((e) => e.value).contains(orderTemp)) {
                  newOrdersPix.add(MapEntry(payment.date, orderTemp));
                }
              }
            }
          }
        }
      }
    }

    // Ordenar todas as listas
    ordersDinheiro.sort((a, b) => a.key.compareTo(b.key));
    ordersCartao.sort((a, b) => a.key.compareTo(b.key));
    ordersPix.sort((a, b) => a.key.compareTo(b.key));

    oldOrdersDinheiro.sort((a, b) => a.key.compareTo(b.key));
    oldOrdersCartao.sort((a, b) => a.key.compareTo(b.key));
    oldOrdersPix.sort((a, b) => a.key.compareTo(b.key));

    newOrdersDinheiro.sort((a, b) => a.key.compareTo(b.key));
    newOrdersCartao.sort((a, b) => a.key.compareTo(b.key));
    newOrdersPix.sort((a, b) => a.key.compareTo(b.key));
  }

  RxList<ClientModel> allClientsInRoutes = <ClientModel>[].obs;

  Future<void> refreshClientsInRoutes() async {
    List<ClientModel> clients = [];

    clients.addAll(await _clientRepository.getClientsFromCache(saleRoute!.id!));

    allClientsInRoutes.assignAll(clients);
  }

  void goToBillingByStreet() async {
    stopTimer();
    await Get.toNamed('/billing-by-street', arguments: appState);
    refreshPending();
    startTimer();
  }

  void goToBillingByMap() async {
    stopTimer();
    await Get.toNamed('/billing-by-map', arguments: appState);
    refreshPending();
    startTimer();
  }

  Future<void> goToClientDetails(ClientModel client) async {
    stopTimer();
    await Get.toNamed('/client_details',
        arguments: client, preventDuplicates: false);
    refreshPending();
    loadReceivedValue();
    startTimer();
  }

  void goToBillingByDate() async {
    stopTimer();
    await Get.toNamed('/billing_by_date',
        arguments: appState.isSellingRouteId!);
    refreshPending();
    loadReceivedValue();
    startTimer();
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

  goToClientesRota() async {
    await Get.toNamed('/clients', arguments: appState.isSellingRouteId!);
    refreshPending();
    loadReceivedValue();
  }

  clearSearch() {
    search.value = '';
    isSearching.value = false;
    clientsFiltered.assignAll(clientsOrdersList);
    searchController.clear();
  }

  searchClient() {
    if (search.isEmpty) {
      clientsFiltered.assignAll(clientsOrdersList);
      return;
    }
    clientsFiltered.assignAll(
      clientsOrdersList.where(
        (client) =>
            TextHelper.removeAcento(client.key.name).toLowerCase().contains(
                TextHelper.removeAcento(search.value.toLowerCase())) ||
            TextHelper.removeAcento(client.key.address)
                .toLowerCase()
                .contains(TextHelper.removeAcento(search.value.toLowerCase())),
      ),
    );
  }

  refreshLocation() async {
    location = await Get.find<GeolocationService>().getCurrentLocation();
  }

  Future<void> goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    await refreshPending();
  }

  void onClientTap(OrderModel order) async {
    var orders = groupedOrders[order.clientId]!;
    if (orders.length > 1) {
      var client = await _clientRepository.getById(order.clientId);
      if (client != null) {
        goToClientDetails(client);
        return;
      }
    }
    goToOrderDetails(order);
  }
}
