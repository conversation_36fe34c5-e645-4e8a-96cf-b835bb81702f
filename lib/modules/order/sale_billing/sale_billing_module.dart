import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_date/billing_by_date_module.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_map/billing_by_map_module.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_street/billing_by_street_module.dart';
import 'package:fl_app/modules/order/sale_billing/resell_billing/resell_billing_module.dart';
import 'package:fl_app/modules/order/sale_billing/sale_billing_bindings.dart';
import 'package:fl_app/modules/order/sale_billing/sale_billing_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SaleBillingModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/sale_billing',
      page: () => const SaleBillingPage(),
      binding: SaleBillingBindings(),
    ),
    ...BillingByStreetModule().routers,
    ...BillingByMapModule().routers,
    ...BillingByDateModule().routers,
    ...ResellBillingModule().routers,
  ];
}
