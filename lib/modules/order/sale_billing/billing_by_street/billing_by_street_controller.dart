import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BillingByStreetController extends GetxController {
  AppState appState = Get.arguments;
  final OrderRepository _orderRepository;

  BillingByStreetController({
    required OrderRepository orderRepository,
  }) : _orderRepository = orderRepository;
//List<Map<String, Map<ClientModel, List<OrderModel>>>>
  RxList<Map<String, Map<ClientModel, List<OrderModel>>>> ordersByStreet =
      <Map<String, Map<ClientModel, List<OrderModel>>>>[].obs;
  RxMap<String, bool> isExpandedStreet = <String, bool>{}.obs;

  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    getOrdersByStreet();
  }

  Future<void> getOrdersByStreet() async {
    isLoading.value = true;
    ordersByStreet.value =
        await _orderRepository.getOrdersByStreet(appState.isSellingRouteId!);
    for (var element in ordersByStreet) {
      isExpandedStreet[element.keys.first] = false;
    }
    isLoading.value = false;
  }

  List<Widget> getOrdersByStreetListWidgets() {
    List<Widget> list = [];
    for (var element in ordersByStreet) {
      list.add(
        ExpansionTile(
          title: Text(element.keys.first,
              style: TextStyle(
                color: Get.isDarkMode ? Colors.indigo[100] : Colors.indigo[900],
              )),
          children: [
            Column(
              children: [
                for (var client in element.values.first.keys)
                  Card(
                    child: ListTile(
                      title: Text(client.name),
                      subtitle: Text(
                        '${client.address}, ${client.number != null && client.number != '' ? '${client.number},' : ''} ${client.localDescription}',
                      ),
                      trailing: Text(
                        '${element.values.first[client]!.length} pedido(s)',
                      ),
                      onTap: () async {
                        await Get.toNamed(
                          '/client_details',
                          arguments: client,
                        );
                        getOrdersByStreet();
                      },
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
    }
    return list;
  }
}
