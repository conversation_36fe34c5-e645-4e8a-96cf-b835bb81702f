import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_street/billing_by_street_bindings.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_street/billing_by_street_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class BillingByStreetModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/billing-by-street',
      page: () => const BillingByStreetPage(),
      binding: BillingByStreetBindings(),
    ),
  ];
}
