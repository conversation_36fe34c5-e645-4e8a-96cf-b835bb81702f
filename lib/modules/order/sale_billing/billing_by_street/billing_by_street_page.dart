import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './billing_by_street_controller.dart';

class BillingByStreetPage extends GetView<BillingByStreetController> {
  const BillingByStreetPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pedidos por Rua'),
        actions: [
          IconButton(
            onPressed: () async {
              await controller.getOrdersByStreet();
              Get.showSnackbar(
                const GetSnackBar(
                  message: 'Atualizado!',
                  duration: Duration(seconds: 1),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(
                child: CircularProgressIndicator(
                strokeCap: StrokeCap.round,
              ))
            : SingleChildScrollView(
                child: Column(
                  children: [
                    ...controller.getOrdersByStreetListWidgets(),
                  ],
                ),
              ),
      ),
    );
  }
}
