import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_date/billing_by_date_bindings.dart';
import 'package:fl_app/modules/order/sale_billing/billing_by_date/billing_by_date_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class BillingByDateModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/billing_by_date',
      page: () => const BillingByDatePage(),
      binding: BillingByDateBindings(),
    ),
  ];
}
