import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/order_list_card.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BillingByDateController extends GetxController {
  final OrderRepository _orderRepository;

  BillingByDateController({
    required OrderRepository orderRepository,
  }) : _orderRepository = orderRepository;

  String rotaId = Get.arguments;

  RxMap<DateTime, List<OrderModel>> orders = <DateTime, List<OrderModel>>{}.obs;

  RxBool isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadOrders();
  }

  Future<void> loadOrders() async {
    isLoading.value = true;
    orders.assignAll(
      await _orderRepository.getOrdersFromRouteByDate(rotaId),
    );
    //remove today orders
    orders.removeWhere((key, value) {
      return DateTimeHelper.isSameDay(key, DateTime.now());
    });
    isLoading.value = false;
  }

  List<Widget> getOrdersByDateListWidgets() {
    List<Widget> list = [];
    for (var element in orders.entries) {
      list.add(
        ExpansionTile(
          title: Text(
              '${DateTimeHelper.getFormattedDate(element.key)} - ${element.value.length} pedidos',
              style: TextStyle(
                color: Get.isDarkMode ? Colors.indigo[100] : Colors.indigo[900],
              )),
          children: [
            Column(
              children: [
                for (var order in element.value)
                  OrderListCard(
                    order: order,
                    onTap: goToOrderDetails,
                    showDaysFromNowLeading: false,
                    showDaysFromNowBottom: true,
                  ),
              ],
            ),
          ],
        ),
      );
    }
    return list;
  }

  void goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    loadOrders();
  }
}
