import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './billing_by_date_controller.dart';

class BillingByDatePage extends GetView<BillingByDateController> {
  const BillingByDatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Pedidos por Data'),
        actions: [
          IconButton(
            onPressed: () async {
              await controller.loadOrders();
              Get.showSnackbar(
                const GetSnackBar(
                  message: 'Atualizado!',
                  duration: Duration(seconds: 1),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Obx(
        () => controller.isLoading.value
            ? const Center(
                child: CircularProgressIndicator(
                strokeCap: StrokeCap.round,
              ))
            : SingleChildScrollView(
                child: <PERSON>umn(
                  children: [
                    ...controller.getOrdersByDateListWidgets(),
                  ],
                ),
              ),
      ),
    );
  }
}
