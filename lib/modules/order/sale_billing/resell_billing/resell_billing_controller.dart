import 'dart:async';
import 'dart:developer';

import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/modules/order/sale_billing/resell_billing/resell_billing_page.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

class ResellBillingController extends GetxController {
  final ClientService _clientService;
  final OrderRepository _orderRepository;

  ResellBillingController({
    required ClientService clientService,
    required OrderRepository orderRepository,
  })  : _clientService = clientService,
        _orderRepository = orderRepository;

  AppState appState = Get.arguments as AppState;

  SaleRouteModel? saleRoute = Get.find<SalesRoutesService>()
      .getRouteById((Get.arguments as AppState).secondRouteId!);

  Rxn<UserModel?> user = Rxn<UserModel?>();

  RxList<MapEntry<ClientModel, List<OrderModel>>> clientsOrdersList =
      <MapEntry<ClientModel, List<OrderModel>>>[].obs;

  RxList<MapEntry<ClientModel, List<OrderModel>>> clientsFiltered =
      <MapEntry<ClientModel, List<OrderModel>>>[].obs;

  RxList<String> clientsArchive = <String>[].obs;
  Box? boxArchive;

  RxString search = ''.obs;
  RxBool isSearching = false.obs;
  FocusNode searchFocus = FocusNode();

  RxBool isLoading = false.obs;

  TextEditingController searchController = TextEditingController();
  final workers = <Worker>[];

  Position? location;

  RxDouble receivedValue = 0.0.obs;
  RxDouble receivedValueDinheiro = 0.0.obs;
  RxDouble receivedValueCartao = 0.0.obs;
  RxDouble receivedValuePix = 0.0.obs;

  RxList<MapEntry<DateTime, OrderModel>> ordersDinheiro =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersCartao =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersPix =
      <MapEntry<DateTime, OrderModel>>[].obs;

  @override
  void onInit() {
    super.onInit();
    init();
  }

  init() async {
    user.value = await Get.find<UserService>().getUserAuthenticated();
    refreshPending();
    loadReceivedValue();

    startTimer();
    boxArchive = await Hive.openBox('resellClientsArchive');
    var dateArchive = boxArchive!.get('dateArchive');
    if (dateArchive == null ||
        !DateTimeHelper.isSameDay(dateArchive, DateTime.now())) {
      await boxArchive!.clear();
      await boxArchive!.put('dateArchive', DateTime.now());
    } else {
      clientsArchive.value =
          boxArchive!.get('clientsArchive', defaultValue: <String>[]);
    }
  }

  void markClientToNotShow(String? id) {
    if (id != null) {
      clientsArchive.add(id);
      boxArchive!.put('clientsArchive', clientsArchive.toList());
      log('ordersArchive: $clientsArchive');
    }
  }

  void markClientToShow(String? id) {
    if (id != null) {
      clientsArchive.remove(id);
      boxArchive!.put('clientsArchive', clientsArchive.toList());
      log('clientsArchive: $clientsArchive');
    }
  }

  void openModalClientsArchive() async {
    // open bottom sheet with ordersArchive
    await Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          border: Border(
            top: BorderSide(
              color: Get.theme.colorScheme.primary.withOpacity(0.8),
              width: 3,
            ),
          ),
        ),
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'Clientes separados:',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: Obx(
                () => ListView.builder(
                  itemCount: clientsOrdersList
                      .where((e) => clientsArchive.contains(e.key.id))
                      .length,
                  itemBuilder: (context, index) {
                    var item = clientsOrdersList
                        .where((e) => clientsArchive.contains(e.key.id))
                        .elementAt(index);
                    var client = item.key;
                    var orders = item.value;
                    return Slidable(
                        closeOnScroll: true,
                        startActionPane: ActionPane(
                          motion: const ScrollMotion(),
                          children: [
                            SlidableAction(
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(10),
                                topLeft: Radius.circular(10),
                              ),
                              icon: Icons.visibility,
                              label: 'Mostrar',
                              onPressed: (context) {
                                markClientToShow(client.id);
                                if (clientsOrdersList
                                    .where((e) =>
                                        clientsArchive.contains(e.key.id))
                                    .isEmpty) {
                                  Get.back();
                                }
                              },
                              backgroundColor:
                                  Theme.of(context).colorScheme.secondary,
                              foregroundColor: Colors.white,
                            ),
                          ],
                        ),
                        child: ResellBillingClientItem(
                          client: client,
                          controller: this,
                          orders: orders,
                          onTap: () async {
                            await goToClientDetails(client);
                            if (clientsOrdersList
                                .where((e) => clientsArchive.contains(e.key.id))
                                .isEmpty) {
                              Get.back();
                            }
                          },
                        ));
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchClient(),
        time: const Duration(milliseconds: 500),
      ),
    );
    super.onReady();
  }

  @override
  void dispose() {
    _timer?.cancel();
    for (var worker in workers) {
      worker.dispose();
    }
    super.dispose();
  }

  Timer? _timer;

  void startTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer.periodic(const Duration(seconds: 30), (timer) {
      refreshPending();
      loadReceivedValue();
    });
  }

  void stopTimer() {
    _timer?.cancel();
  }

  Future<void> refreshPending() async {
    isLoading.value = true;
    await refreshLocation();
    final clientOrders =
        await _clientService.getClientsOrders([appState.secondRouteId!]);

    // clientsOrdersList é para ser ordenado pela distância do cliente
    clientsOrdersList.assignAll(clientOrders.entries);

    final geolocationService = Get.find<GeolocationService>();
    final location = await geolocationService.getCurrentLocation();
    clientsOrdersList.sort((a, b) {
      if ((a.key.longitude == null || a.key.latitude == null) &&
          (b.key.longitude == null || b.key.latitude == null)) {
        return 0;
      } else if ((a.key.longitude == null || a.key.latitude == null) &&
          (b.key.longitude != null && b.key.latitude != null)) {
        return -1;
      } else if ((a.key.longitude != null && a.key.latitude != null) &&
          (b.key.longitude == null || b.key.latitude == null)) {
        return 1;
      }
      final distanceA = a.key.distanceTo(location);
      final distanceB = b.key.distanceTo(location);
      if (distanceA == null || distanceB == null) {
        return -1;
      }
      return distanceA.compareTo(distanceB);
    });

    //remove os pedidos da ultima data
    for (var client in clientsOrdersList) {
      client.value.removeWhere((order) =>
          DateTimeHelper.isSameDay(order.date, appState.secondRouteDate!) ||
          order.isPaid ||
          order.isJoined ||
          order.isToday ||
          order.dayMarkingItems.any(
            (element) =>
                element.active &&
                // Marcado Hoje
                (DateTimeHelper.isSameDay(element.createdAt, DateTime.now()) ||
                    // Marcado no dia da segunda rota
                    DateTimeHelper.isSameDay(
                        element.createdAt, appState.secondRouteDate!) ||
                    // Marcado anteriormente para depis da data da segunda rota
                    (element.dayToVisit?.isAfter(appState.secondRouteDate!) !=
                            null &&
                        element.dayToVisit!
                            .isAfter(appState.secondRouteDate!))),
          ));
    }

    // remove os clientes que não tem pedidos
    clientsOrdersList.removeWhere((element) => element.value.isEmpty);

    clientsFiltered.assignAll(clientsOrdersList);
    searchClient();
    isLoading.value = false;
  }

  Future<void> loadReceivedValue() async {
    List<String> routes = saleRoute?.id != null ? [saleRoute!.id!] : [];
    var ordersReceived = await _orderRepository.getOrdersReceived(
      routes: routes,
      date: DateTime.now(),
    );
    receivedValue.value = 0.0;
    receivedValueDinheiro.value = 0.0;
    receivedValueCartao.value = 0.0;
    receivedValuePix.value = 0.0;

    ordersDinheiro.clear();
    ordersCartao.clear();
    ordersPix.clear();

    for (var orderTemp in ordersReceived) {
      for (var payment in orderTemp.payments) {
        if (payment.paymentMethod != null) {
          if (DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
            receivedValue.value += payment.value;
            if (payment.paymentMethod == PaymentMethod.dinheiro) {
              receivedValueDinheiro.value += payment.value;
              if (!ordersDinheiro.map((e) => e.value).contains(orderTemp)) {
                ordersDinheiro.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.cartao) {
              receivedValueCartao.value += payment.value;
              if (!ordersCartao.map((e) => e.value).contains(orderTemp)) {
                ordersCartao.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.pix) {
              receivedValuePix.value += payment.value;
              if (!ordersPix.map((e) => e.value).contains(orderTemp)) {
                ordersPix.add(MapEntry(payment.date, orderTemp));
              }
            }
          }
        }
      }
    }

    ordersDinheiro.sort((a, b) => a.key.compareTo(b.key));
    ordersCartao.sort((a, b) => a.key.compareTo(b.key));
    ordersPix.sort((a, b) => a.key.compareTo(b.key));
  }

  void goToBillingByStreet() async {
    stopTimer();
    await Get.toNamed('/billing-by-street', arguments: appState);
    refreshPending();
    startTimer();
  }

  void goToBillingByMap() async {
    stopTimer();
    await Get.toNamed('/billing-by-map', arguments: appState);
    refreshPending();
    startTimer();
  }

  Future<void> goToClientDetails(ClientModel client) async {
    stopTimer();
    await Get.toNamed('/client_details',
        arguments: client, preventDuplicates: false);
    refreshPending();
    loadReceivedValue();
    startTimer();
  }

  void goToBillingByDate() async {
    stopTimer();
    await Get.toNamed('/billing_by_date', arguments: appState.secondRouteId!);
    refreshPending();
    loadReceivedValue();
    startTimer();
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }

  goToClientesRota() async {
    await Get.toNamed('/clients', arguments: appState.secondRouteId!);
    refreshPending();
    loadReceivedValue();
  }

  clearSearch() {
    search.value = '';
    isSearching.value = false;
    clientsFiltered.assignAll(clientsOrdersList);
    searchController.clear();
  }

  searchClient() {
    if (search.isEmpty) {
      clientsFiltered.assignAll(clientsOrdersList);
      return;
    }
    clientsFiltered.assignAll(
      clientsOrdersList.where(
        (client) =>
            TextHelper.removeAcento(client.key.name).toLowerCase().contains(
                TextHelper.removeAcento(search.value.toLowerCase())) ||
            TextHelper.removeAcento(client.key.address)
                .toLowerCase()
                .contains(TextHelper.removeAcento(search.value.toLowerCase())),
      ),
    );
  }

  refreshLocation() async {
    location = await Get.find<GeolocationService>().getCurrentLocation();
  }
}
