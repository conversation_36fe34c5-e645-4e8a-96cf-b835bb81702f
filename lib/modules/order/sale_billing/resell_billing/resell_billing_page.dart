import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/valor_recebido_tile.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'resell_billing_controller.dart';

class ResellBillingPage extends GetView<ResellBillingController> {
  const ResellBillingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 100,
        centerTitle: true,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Text('Repasses em'),
            Text(
              controller.saleRoute?.name ?? '',
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
            Text(
              '(${DateTimeHelper.getFormattedDate(controller.appState.secondRouteDate!)})',
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo[100],
                      foregroundColor: Colors.black,
                    ),
                    onPressed: () {
                      controller.goToBillingByDate();
                    },
                    icon: const Icon(Icons.calendar_today),
                    label: const Text('Pedidos por Data',
                        textAlign: TextAlign.center),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => controller.goToClientesRota(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigoAccent,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.users,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Text('Clientes'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Obx(
              () => controller.user.value != null &&
                      controller.user.value!.admin
                  ? ValorRecebidoTile(
                      receivedValue: controller.receivedValue.value,
                      receivedValueDinheiro:
                          controller.receivedValueDinheiro.value,
                      receivedValueCartao: controller.receivedValueCartao.value,
                      receivedValuePix: controller.receivedValuePix.value,
                      ordersDinheiro: controller.ordersDinheiro
                          .map((e) => e.value)
                          .toList(),
                      ordersCartao:
                          controller.ordersCartao.map((e) => e.value).toList(),
                      ordersPix:
                          controller.ordersPix.map((e) => e.value).toList(),
                    )
                  : const SizedBox.shrink(),
            ),
            Obx(
              () => Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Pendentes (${controller.isLoading.value ? '...' : controller.clientsFiltered.where((e) => !controller.clientsArchive.contains(e.key.id)).length})',
                    style: const TextStyle(
                      fontSize: 18,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          controller.isSearching.value =
                              !controller.isSearching.value;
                          if (!controller.isSearching.value) {
                            controller.clearSearch();
                          } else {
                            controller.searchFocus.requestFocus();
                          }
                        },
                        icon: Icon(controller.isSearching.value
                            ? Icons.close
                            : Icons.search),
                      ),
                      IconButton(
                        onPressed: () {
                          controller.refreshPending();
                          controller.loadReceivedValue();
                        },
                        icon: const Icon(Icons.refresh),
                      ),
                    ],
                  )
                ],
              ),
            ),
            Obx(
              () => controller.isSearching.value
                  ? SearchBar(
                      controller: controller.searchController,
                      hintText: 'Pesquisar pendentes',
                      leading: const Icon(Icons.search),
                      onChanged: (value) => controller.search(value),
                      elevation: WidgetStateProperty.all(2),
                      trailing: [
                        if (controller.search.isNotEmpty)
                          IconButton(
                            onPressed: () => controller.clearSearch(),
                            icon: const Icon(Icons.clear),
                          ),
                      ],
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                          side: const BorderSide(
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      focusNode: controller.searchFocus,
                      backgroundColor: WidgetStateProperty.all(Colors.grey[50]),
                    )
                  : const SizedBox.shrink(),
            ),
            Obx(
              () => Row(
                children: [
                  if (controller.clientsFiltered
                      .where(
                          (e) => controller.clientsArchive.contains(e.key.id))
                      .isNotEmpty)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          controller.openModalClientsArchive();
                        },
                        label: Text(
                            'Pendentes separados (${controller.clientsFiltered.where((e) => controller.clientsArchive.contains(e.key.id)).length})'),
                        icon: const Icon(Icons.visibility_off),
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(
                                color: Theme.of(context).colorScheme.secondary),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Expanded(
              child: Obx(
                () => controller.isLoading.value
                    ? const Center(
                        child: CircularProgressIndicator(
                          strokeCap: StrokeCap.round,
                        ),
                      )
                    : controller.clientsFiltered.isEmpty
                        ? const Center(
                            child:
                                Text('Nenhum cliente com cobrança pendente.'),
                          )
                        : RefreshIndicator(
                            onRefresh: () async {
                              await controller.refreshPending();
                            },
                            child: ListView.builder(
                              itemCount: controller.clientsFiltered
                                  .where((e) => !controller.clientsArchive
                                      .contains(e.key.id))
                                  .length,
                              itemBuilder: (context, index) {
                                var item = controller.clientsFiltered
                                    .where((e) => !controller.clientsArchive
                                        .contains(e.key.id))
                                    .elementAt(index);
                                final client = item.key;
                                final orders = item.value;
                                return Slidable(
                                  closeOnScroll: true,
                                  startActionPane: ActionPane(
                                    motion: const ScrollMotion(),
                                    children: [
                                      SlidableAction(
                                        autoClose: true,
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(10),
                                          topLeft: Radius.circular(10),
                                        ),
                                        icon: Icons.visibility_off,
                                        label: 'Separar',
                                        onPressed: (context) {
                                          controller
                                              .markClientToNotShow(client.id);
                                          if (controller.clientsOrdersList
                                              .where((e) => controller
                                                  .clientsArchive
                                                  .contains(e.key.id))
                                              .isEmpty) {
                                            Get.back();
                                          }
                                        },
                                        backgroundColor: Theme.of(context)
                                            .colorScheme
                                            .secondary,
                                        foregroundColor: Colors.white,
                                      ),
                                    ],
                                  ),
                                  child: ResellBillingClientItem(
                                      client: client,
                                      controller: controller,
                                      orders: orders,
                                      onTap: () async {
                                        await controller
                                            .goToClientDetails(client);
                                      }),
                                );
                              },
                            ),
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ResellBillingClientItem extends StatelessWidget {
  const ResellBillingClientItem({
    super.key,
    required this.client,
    required this.controller,
    required this.orders,
    required this.onTap,
  });

  final ClientModel client;
  final ResellBillingController controller;
  final List<OrderModel> orders;
  final Future<void> Function() onTap;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text(client.name,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            )),
        subtitle: Text(
          '${client.address}, ${client.number != null && client.number != '' ? '${client.number},' : ''} ${client.localDescription}',
        ),
        leading: client.latitude == null || client.longitude == null
            ? Icon(Icons.location_off, color: Colors.red[300]!)
            : controller.location != null
                ? Text(
                    '${client.distanceTo(controller.location)!.toInt()}m',
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  )
                : const SizedBox.shrink(),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${orders.length} pedido(s)',
            ),
            Text(
              'R\$ ${orders.fold(0.0, (previousValue, order) => order.isPaid ? previousValue : previousValue + order.getTotalPending()).toStringAsFixed(2)}',
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }
}
