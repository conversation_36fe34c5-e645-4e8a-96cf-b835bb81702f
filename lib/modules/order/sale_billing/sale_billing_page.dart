import 'package:fl_app/application/ui/widgets/order_days_from_now.dart';
import 'package:fl_app/application/ui/widgets/valor_recebido_tile.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './sale_billing_controller.dart';

class SaleBillingPage extends GetView<SaleBillingController> {
  const SaleBillingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cobranças de Venda'),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      controller.goToBillingByStreet();
                    },
                    icon: const Icon(Icons.list),
                    label: const Text('Ruas'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo[100],
                      foregroundColor: Colors.black,
                      iconColor: Colors.black,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      controller.goToBillingByMap();
                    },
                    icon: const Icon(Icons.map),
                    label: const Text('Mapa'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo[100],
                      foregroundColor: Colors.black,
                      iconColor: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo[100],
                      foregroundColor: Colors.black,
                      iconColor: Colors.black,
                    ),
                    onPressed: () {
                      controller.goToBillingByDate();
                    },
                    icon: const Icon(Icons.calendar_today),
                    label: const Text('Pedidos por Data',
                        textAlign: TextAlign.center),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => controller.goToClientesRota(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigoAccent,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.users,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Text('Clientes'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Recibimentos antigos (antes de 17/04/25)',
            ),
            Obx(
              () => controller.user.value != null &&
                      controller.user.value!.admin
                  ? ValorRecebidoTile(
                      receivedValue: controller.oldReceivedValue.value,
                      receivedValueDinheiro:
                          controller.oldReceivedValueDinheiro.value,
                      receivedValueCartao:
                          controller.oldReceivedValueCartao.value,
                      receivedValuePix: controller.oldReceivedValuePix.value,
                      ordersDinheiro: controller.oldOrdersDinheiro
                          .map((e) => e.value)
                          .toList(),
                      ordersCartao: controller.oldOrdersCartao
                          .map((e) => e.value)
                          .toList(),
                      ordersPix:
                          controller.oldOrdersPix.map((e) => e.value).toList(),
                      title: 'Recebido Hoje (Pedidos Antigos)',
                    )
                  : const SizedBox.shrink(),
            ),
            const SizedBox(height: 8),
            const Text(
              'Recibimentos novos (depois de 17/04/25)',
            ),
            Obx(
              () => controller.user.value != null &&
                      controller.user.value!.admin
                  ? ValorRecebidoTile(
                      receivedValue: controller.newReceivedValue.value,
                      receivedValueDinheiro:
                          controller.newReceivedValueDinheiro.value,
                      receivedValueCartao:
                          controller.newReceivedValueCartao.value,
                      receivedValuePix: controller.newReceivedValuePix.value,
                      ordersDinheiro: controller.newOrdersDinheiro
                          .map((e) => e.value)
                          .toList(),
                      ordersCartao: controller.newOrdersCartao
                          .map((e) => e.value)
                          .toList(),
                      ordersPix:
                          controller.newOrdersPix.map((e) => e.value).toList(),
                      title: 'Recebido Hoje (Pedidos Novos)',
                    )
                  : const SizedBox.shrink(),
            ),
            Obx(
              () => Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Pendentes (${controller.groupedOrders.entries.where((e) => !controller.clientsArchive.contains(e.key)).length})',
                    style: const TextStyle(
                      fontSize: 18,
                    ),
                  ),
                  Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          controller.isSearching.value =
                              !controller.isSearching.value;
                          if (!controller.isSearching.value) {
                            controller.clearSearch();
                          } else {
                            controller.searchFocus.requestFocus();
                          }
                        },
                        icon: Icon(controller.isSearching.value
                            ? Icons.close
                            : Icons.search),
                      ),
                      IconButton(
                        onPressed: () {
                          controller.refreshPending();
                          controller.loadReceivedValue();
                        },
                        icon: const Icon(Icons.refresh),
                      ),
                    ],
                  )
                ],
              ),
            ),
            Obx(
              () => controller.isSearching.value
                  ? SearchBar(
                      controller: controller.searchController,
                      hintText: 'Pesquisar pendentes',
                      leading: const Icon(Icons.search),
                      onChanged: (value) => controller.search(value),
                      elevation: WidgetStateProperty.all(2),
                      trailing: [
                        if (controller.search.isNotEmpty)
                          IconButton(
                            onPressed: () => controller.clearSearch(),
                            icon: const Icon(Icons.clear),
                          ),
                      ],
                      shape: WidgetStateProperty.all(
                        RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                          side: BorderSide(
                            color: Get.isDarkMode
                                ? Colors.grey[800]!
                                : Colors.grey[50]!,
                          ),
                        ),
                      ),
                      focusNode: controller.searchFocus,
                      backgroundColor: WidgetStateProperty.all(
                          Get.isDarkMode ? Colors.grey[800] : Colors.grey[50]),
                    )
                  : const SizedBox.shrink(),
            ),
            Obx(
              () => Row(
                children: [
                  if (controller.groupedOrders.isNotEmpty &&
                      controller.clientsArchive.isNotEmpty)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () {
                          controller.openModalClientsArchive();
                        },
                        label: Text(
                            'Clientes separados (${controller.clientsArchive.length})'),
                        icon: const Icon(Icons.visibility_off),
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(
                                color: Theme.of(context).colorScheme.secondary),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            Expanded(
              child: Obx(() {
                if (controller.isLoading.value) {
                  return const Center(
                    child: CircularProgressIndicator(
                      strokeCap: StrokeCap.round,
                    ),
                  );
                }

                if (controller.groupedOrders.isEmpty) {
                  return const Center(
                    child: Text('Nenhum cliente com cobrança pendente.'),
                  );
                }

                final groupedEntries = controller.groupedOrders.entries
                    .where((e) => e.value.any((order) =>
                        order.clientName
                            .toLowerCase()
                            .contains(controller.search.value.toLowerCase()) ||
                        order.clientAddress
                            .toLowerCase()
                            .contains(controller.search.value.toLowerCase()) ||
                        order.clientLocalDescription
                            .toLowerCase()
                            .contains(controller.search.value.toLowerCase())))
                    .toList();

                return RefreshIndicator(
                  onRefresh: () async {
                    await controller.refreshPending();
                  },
                  child: ListView.builder(
                    itemCount: groupedEntries
                        .where(
                            (e) => !controller.clientsArchive.contains(e.key))
                        .length,
                    itemBuilder: (context, index) {
                      final orders = groupedEntries
                          .where(
                              (e) => !controller.clientsArchive.contains(e.key))
                          .toList()[index]
                          .value;
                      final order = orders.last;
                      return Slidable(
                        closeOnScroll: true,
                        startActionPane: ActionPane(
                          motion: const ScrollMotion(),
                          children: [
                            SlidableAction(
                              autoClose: true,
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(10),
                                topLeft: Radius.circular(10),
                              ),
                              icon: Icons.visibility_off,
                              label: 'Separar',
                              onPressed: (context) {
                                controller.markClientToNotShow(order.clientId);
                              },
                              backgroundColor:
                                  Theme.of(context).colorScheme.secondary,
                              foregroundColor: Colors.white,
                            ),
                          ],
                        ),
                        child: SaleBillingClientItem(
                            order: order,
                            controller: controller,
                            orders: orders,
                            location: controller.location,
                            onTap: () async {
                              controller.onClientTap(order);
                            }),
                      );
                    },
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class SaleBillingClientItem extends StatelessWidget {
  const SaleBillingClientItem({
    super.key,
    required this.order,
    required this.controller,
    required this.orders,
    required this.onTap,
    this.location,
  });

  final OrderModel order;
  final SaleBillingController controller;
  final List<OrderModel> orders;
  final Future<void> Function() onTap;
  final Position? location;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        title: Text(order.clientName,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            )),
        subtitle: Text(
          '${order.clientAddress}, ${order.clientNumber != null && order.clientNumber != '' ? '${order.clientNumber},' : ''} ${order.clientLocalDescription}',
        ),
        leading: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          spacing: 4,
          children: [
            OrderDaysFromNow(order: order),
            order.clientLatitude == null || order.clientLongitude == null
                ? Icon(Icons.location_off, color: Colors.red[300]!, size: 18)
                : location != null
                    ? Text(
                        '${order.distanceTo(location)!.toInt()}m',
                        style: const TextStyle(
                          fontSize: 12,
                        ),
                      )
                    : const SizedBox.shrink(),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${orders.length} pedido(s)',
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
            Text(
              'R\$ ${orders.fold(0.0, (previousValue, order) => order.isPaid ? previousValue : previousValue + order.getTotalPending()).toStringAsFixed(2)}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
        onTap: onTap,
      ),
    );
  }
}
