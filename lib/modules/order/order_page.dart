import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/modules/order/widgets/filter_options_bottom_sheet.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../../application/ui/widgets/order_list_card.dart';
import './order_controller.dart';

class OrderPage extends GetView<OrderController> {
  const OrderPage({super.key});

  Widget _buildSearchField(BuildContext context) {
    return Obx(() {
      return TextField(
        controller: controller.searchController,
        focusNode: controller.searchFocus,
        decoration: InputDecoration(
          hintText: 'Pesquisar pedidos',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: controller.search.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: controller.clearSearch,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          fillColor: Theme.of(context).cardColor,
          filled: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        ),
        onChanged: controller.search.call,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Pedidos (${controller.isFiltering.value ? controller.filteredOrders.length : controller.orders.length})',
                style: const TextStyle(
                  fontSize: 18,
                ),
              ),
              if (controller.rotaName != null && controller.rotaName != '')
                Text(
                  '${controller.rotaName}',
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                ),
            ],
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: _buildSearchField(context),
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                backgroundColor:
                    Colors.transparent, // Tornar o fundo do modal transparente
                builder: (ctx) => Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(
                          20), // Aumentar o raio para um visual mais suave
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: FilterOptionsBottomSheet(
                      controller: controller), // Usar o novo widget
                ),
              );
            },
            icon: const Icon(Icons.filter_list_rounded),
          ),
          PopupMenuButton(
            itemBuilder: (context) {
              return [
                PopupMenuItem(
                  value: 0,
                  child: const Text('Ver Pedidos Apagados',
                      textAlign: TextAlign.center),
                  onTap: () {
                    controller.goToDeletedOrders();
                  },
                ),
              ];
            },
          ),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Obx(() {
          final orders = controller.filteredOrders;

          return Column(
            children: [
              if (controller.isFiltering.value)
                Container(
                  width: Get.width,
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.indigo,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Text(
                    controller.filterText.value,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    if (orders.isEmpty && controller.isLoading.value)
                      const Center(
                        child: CircularProgressIndicator(
                          strokeCap: StrokeCap.round,
                        ),
                      ),
                    if (orders.isEmpty && !controller.isLoading.value)
                      const Center(
                        child: Text('Nenhum pedido encontrado'),
                      ),
                    if (orders.isNotEmpty && !controller.isLoading.value)
                      Expanded(
                        child: RefreshIndicator(
                          onRefresh: () async {
                            await controller.refreshOrders();
                          },
                          child: Scrollbar(
                            child: ListView.builder(
                              itemCount: orders.length,
                              itemBuilder: (context, index) {
                                final order = orders[index];
                                return OrderListCard(
                                    order: order,
                                    onTap: controller.goToOrderDetails);
                              },
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
}
