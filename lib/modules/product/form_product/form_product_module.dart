import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/product/form_product/form_product_bindings.dart';
import 'package:fl_app/modules/product/form_product/form_product_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class FormProductModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/form_product',
      page: () => FormProductPage(),
      binding: FormProductBindings(),
    ),
  ];
}
