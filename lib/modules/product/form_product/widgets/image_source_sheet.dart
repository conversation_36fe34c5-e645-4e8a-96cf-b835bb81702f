import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

class ImageSourceSheet extends StatelessWidget {
  final ImagePicker picker = ImagePicker();

  ImageSourceSheet({super.key, required this.onImageSelect});

  final Function(CroppedFile) onImageSelect;

  Future<void> editImage(String path) async {
    final CroppedFile? croppedFile =
        await ImageCropper().cropImage(sourcePath: path, uiSettings: [
      AndroidUiSettings(
        toolbarTitle: "Cortar imagem",
        backgroundColor: Get.isDarkMode ? Colors.black : Colors.white,
        toolbarColor: Colors.white,
        statusBarColor: Colors.transparent,
      )
    ]);
    if (croppedFile != null) {
      onImageSelect(croppedFile);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
      onClosing: () {
        Get.back();
      },
      builder: (_) => Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ElevatedButton(
            onPressed: () async {
              final XFile? file =
                  await picker.pickImage(source: ImageSource.camera);
              if (file != null) {
                editImage(file.path);
              }
            },
            child: const Text(
              'Câmera',
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              final XFile? file =
                  await picker.pickImage(source: ImageSource.gallery);
              if (file != null) {
                editImage(file.path);
              }
            },
            child: const Text(
              'Galeria',
            ),
          ),
        ],
      ),
    );
  }
}
