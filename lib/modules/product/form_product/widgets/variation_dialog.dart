import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/modules/product/form_product/widgets/image_source_sheet.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:uuid/uuid.dart';

class VariationDialog extends StatefulWidget {
  final VariationModel? initialVariation;
  final double? initialPrice;

  const VariationDialog({
    super.key,
    this.initialVariation,
    this.initialPrice,
  });

  @override
  State<VariationDialog> createState() => _VariationDialogState();
}

class _VariationDialogState extends State<VariationDialog> {
  final _formKey = GlobalKey<FormState>();

  final _nameController = TextEditingController();
  final _priceController = TextEditingController();

  CroppedFile? _croppedImageFile;

  @override
  void initState() {
    super.initState();
    if (widget.initialVariation != null) {
      _nameController.text = widget.initialVariation!.name;
      _priceController.text = widget.initialVariation!.price.toString();

      // Se a variação já tem um localPath, podemos carregar a imagem local
      if (widget.initialVariation!.localPath != null &&
          widget.initialVariation!.localPath!.isNotEmpty) {
        _croppedImageFile = CroppedFile(widget.initialVariation!.localPath!);
      }
    }

    if (widget.initialPrice != null) {
      _priceController.text = widget.initialPrice.toString();
    }
  }

  void _showImageSourceSheet() {
    showModalBottomSheet(
      context: context,
      builder: (_) => ImageSourceSheet(
        onImageSelect: (cropped) {
          setState(() {
            _croppedImageFile = cropped;
          });
          Navigator.of(context).pop();
        },
      ),
    );
  }

  Widget _buildImagePreview() {
    // Se uma nova imagem foi selecionada no diálogo
    if (_croppedImageFile != null) {
      return ExtendedImage.file(
        File(_croppedImageFile!.path),
        fit: BoxFit.fitHeight,
      );
    }

    // Se a variação tem um localPath antigo
    if (widget.initialVariation?.localPath != null &&
        widget.initialVariation!.localPath!.isNotEmpty) {
      return ExtendedImage.file(
        File(widget.initialVariation!.localPath!),
        fit: BoxFit.fitHeight,
      );
    }

    // Se a variação tem uma imageUrl remota
    if (widget.initialVariation?.imageUrl != null &&
        widget.initialVariation!.imageUrl!.isNotEmpty) {
      return ExtendedImage.network(
        widget.initialVariation!.imageUrl!,
        fit: BoxFit.fitHeight,
        height: 160,
        cache: true,
        loadStateChanged: (ExtendedImageState state) {
          switch (state.extendedImageLoadState) {
            case LoadState.loading:
              return const Center(child: CircularProgressIndicator());
            case LoadState.completed:
              return null; // Usa o padrão
            case LoadState.failed:
              return const Center(child: Icon(Icons.broken_image, size: 50));
          }
        },
      );
    }

    // Placeholder caso não haja imagem
    return const Icon(
      Icons.add_photo_alternate_outlined,
      size: 50,
      color: Colors.grey,
    );
  }

  void _onSavePressed() {
    if (_formKey.currentState!.validate()) {
      final variation = VariationModel(
        id: widget.initialVariation?.id ?? const Uuid().v7(),
        name: _nameController.text,
        price: double.parse(_priceController.text.replaceAll(',', '.')),
        imageUrl: widget.initialVariation?.imageUrl ?? '',
        localPath: _croppedImageFile != null
            ? _croppedImageFile!.path
            : widget.initialVariation?.localPath,
      );
      Navigator.of(context).pop(variation);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.initialVariation == null ? 'Nova Variação' : 'Editar Variação',
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Seção para a imagem da variação
                GestureDetector(
                  onTap: _showImageSourceSheet,
                  child: Container(
                    height: 160,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: _buildImagePreview(),
                  ),
                ),
                const SizedBox(height: 8),
                // Nome da variação
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nome da variação',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Digite o nome da variação';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 8),
                // Preço da variação
                TextFormField(
                  controller: _priceController,
                  decoration: const InputDecoration(
                    labelText: 'Preço',
                    prefixText: 'R\$ ',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType:
                      const TextInputType.numberWithOptions(decimal: true),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Informe o preço da variação';
                    }
                    if (double.tryParse(value.replaceAll(',', '.')) == null) {
                      return 'Valor inválido';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          child: const Text('Cancelar'),
          onPressed: () => Navigator.of(context).pop(null),
        ),
        ElevatedButton(
          onPressed: _onSavePressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
          ),
          child: const Text('Salvar'),
        ),
      ],
    );
  }
}
