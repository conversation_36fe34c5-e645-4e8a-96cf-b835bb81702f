import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:extended_image/extended_image.dart';
import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/modules/product/form_product/form_product_controller.dart';
import 'package:fl_app/modules/product/form_product/widgets/image_source_sheet.dart';
import 'package:fl_app/modules/product/form_product/widgets/variation_dialog.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class FormProductPage extends GetView<FormProductController> {
  FormProductPage({super.key});

  final isEdit = Get.arguments != null;

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(isEdit ? 'Editar Produto' : 'Novo Produto'),
        ),
        body: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  Obx(
                    () => Card(
                      child: SizedBox(
                        height: 150,
                        width: double.infinity,
                        child: controller.produto.value?.imagem != null &&
                                controller.imagemFile.value == null
                            ? GestureDetector(
                                onTap: () => showModalBottomSheet(
                                      context: context,
                                      builder: (_) => ImageSourceSheet(
                                          onImageSelect: (image) {
                                        controller.imagemFile(image);
                                        controller.imagemMudou(true);
                                        Get.back();
                                      }),
                                    ),
                                child: CachedNetworkImage(
                                  imageUrl:
                                      controller.produto.value?.imagem ?? '',
                                  fit: BoxFit.fitHeight,
                                  height: 270,
                                  width: double.infinity,
                                  memCacheHeight: 500,
                                  memCacheWidth: 500,
                                  placeholder: (context, url) => const Center(
                                    child: CircularProgressIndicator(
                                      strokeCap: StrokeCap.round,
                                    ),
                                  ),
                                ))
                            : controller.imagemFile.value != null
                                ? GestureDetector(
                                    onTap: () => showModalBottomSheet(
                                          context: context,
                                          builder: (_) => ImageSourceSheet(
                                              onImageSelect: (image) {
                                            controller.imagemFile(image);
                                            controller.imagemMudou(true);
                                            Get.back();
                                          }),
                                        ),
                                    child: ExtendedImage.file(File(
                                        controller.imagemFile.value!.path)))
                                : TextButton(
                                    onPressed: () => showModalBottomSheet(
                                          context: context,
                                          builder: (_) => ImageSourceSheet(
                                              onImageSelect: (image) {
                                            controller.imagemFile(image);
                                            controller.imagemMudou(true);

                                            Get.back();
                                          }),
                                        ),
                                    child: const Text('Selecionar Imagem')),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    initialValue: controller.produto.value?.nome,
                    decoration: const InputDecoration(
                      labelText: 'Nome',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Nome é obrigatório';
                      }
                      return null;
                    },
                    onSaved: (value) {
                      controller.nome(value!);
                    },
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: TextFormField(
                          initialValue: controller.produto.value?.valor
                              .toStringAsFixed(2),
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'Valor',
                            border: OutlineInputBorder(),
                            prefixText: 'R\$ ',
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Preço a prazo é obrigatório';
                            }
                            return null;
                          },
                          onSaved: (value) {
                            controller.valor(
                                double.parse(value!.replaceAll(',', '.')));
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Obx(() {
                    if (controller.user.value?.admin ?? false) {
                      return Row(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Tem Comissão',
                            style: TextStyle(fontSize: 20),
                          ),
                          const SizedBox(width: 8),
                          Switch(
                            value: controller.comissao.value,
                            onChanged: (comi) {
                              controller.comissao(comi);
                            },
                          ),
                        ],
                      );
                    } else {
                      return Container();
                    }
                  }),
                  const Divider(),
                  Obx(() {
                    final variations = controller.variations;
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Variações',
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            TextButton.icon(
                              icon: const Icon(Icons.add),
                              label: const Text('Adicionar Variação'),
                              onPressed: () async {
                                // chama o diálogo para adicionar uma nova variação
                                final newVariation =
                                    await showDialog<VariationModel>(
                                  context: context,
                                  builder: (_) => VariationDialog(
                                    initialPrice: controller
                                        .produto.value?.valor
                                        .toDouble(),
                                  ),
                                );
                                if (newVariation != null) {
                                  controller.addVariation(newVariation);
                                }
                              },
                            )
                          ],
                        ),
                        const SizedBox(height: 8),
                        if (variations.isEmpty)
                          const Text('Nenhuma variação adicionada')
                        else
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: variations.length,
                            itemBuilder: (context, index) {
                              final variation = variations[index];
                              return Card(
                                child: ListTile(
                                  leading: variation.imageUrl != null &&
                                          variation.imageUrl!.isNotEmpty
                                      ? CachedNetworkImage(
                                          imageUrl: variation.imageUrl!,
                                          width: 50,
                                          height: 50,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) =>
                                              const CircularProgressIndicator(),
                                          errorWidget: (context, url, error) =>
                                              const Icon(Icons.broken_image),
                                        )
                                      : variation.localPath != null &&
                                              variation.localPath!.isNotEmpty
                                          ? ExtendedImage.file(
                                              File(variation.localPath!),
                                              width: 50,
                                              height: 50,
                                              fit: BoxFit.cover,
                                              borderRadius:
                                                  const BorderRadius.all(
                                                      Radius.circular(8)),
                                            )
                                          : const Icon(
                                              Icons.image,
                                              size: 50,
                                              color: Colors.grey,
                                            ),
                                  title: Text(variation.name),
                                  subtitle: Text(NumberFormatHelper.format(
                                      variation.price.toDouble())),
                                  trailing: IconButton(
                                    icon: const Icon(Icons.delete,
                                        color: Colors.red),
                                    onPressed: () {
                                      controller.removeVariation(variation);
                                    },
                                  ),
                                  onTap: () async {
                                    // Ao clicar, abrir o diálogo para editar a variação
                                    final result =
                                        await showDialog<VariationModel>(
                                      context: context,
                                      builder: (_) => VariationDialog(
                                        initialVariation: variation,
                                      ),
                                    );
                                    if (result != null) {
                                      controller.updateVariation(index, result);
                                    }
                                  },
                                ),
                              );
                            },
                          ),
                      ],
                    );
                  }),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).colorScheme.secondary,
                        foregroundColor:
                            Theme.of(context).colorScheme.onSecondary,
                      ),
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          _formKey.currentState!.save();
                          if (!isEdit) {
                            controller.salvar();
                          } else {
                            controller.editar();
                          }
                        }
                      },
                      child: Text(isEdit ? 'Editar' : 'Salvar'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }
}
