import 'package:fl_app/application/ui/loader/loader_mixin.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';

class FormProductController extends GetxController with LoaderMixin {
  final ProductRepository _productRepository;

  FormProductController({required ProductRepository productRepository})
      : _productRepository = productRepository;

  Rxn<CroppedFile?> imagemFile = Rxn<CroppedFile?>();

  var imagemMudou = false.obs;

  var comissao = false.obs;

  var isLoading = false.obs;

  Rxn<ProductModel> produto = Rxn<ProductModel>();

  RxList<VariationModel> variations = <VariationModel>[].obs;

  var nome = Rxn<String>();
  var valor = Rxn<num>();

  Rxn<UserModel> user = Rxn<UserModel>();

  @override
  void onInit() {
    loaderListener(isLoading);
    if (Get.arguments != null) {
      produto(Get.arguments as ProductModel);
      nome(Get.arguments!.nome);
      valor(Get.arguments!.valor);
      comissao(Get.arguments!.temComissao);

      if (produto.value != null) {
        variations.assignAll(produto.value!.variations);
      }
    }
    init();
    super.onInit();
  }

  void init() async {
    user.value = await Get.find<UserService>().getUserAuthenticated();
    user.refresh();
  }

  // Exemplo de método para criar uma nova variação
  void addVariation(VariationModel variation) {
    variations.add(variation);
  }

  // Exemplo de método para remover uma variação
  void removeVariation(VariationModel variation) {
    variations.remove(variation);
  }

  // Exemplo de método para atualizar uma variação
  void updateVariation(int index, VariationModel newVariation) {
    variations[index] = newVariation;
  }

  Future<void> salvar() async {
    try {
      isLoading(true);
      ProductModel produtoModel = ProductModel(
        nome: nome.value!,
        valor: valor.value!,
        imagem: null,
        temComissao: comissao.value,
        lastModified: DateTime.now(),
        variations: variations.toList(),
      );
      final result = await _productRepository.addProduct(produtoModel,
          imagePath: imagemFile.value?.path);
      if (result.imagem == null && imagemFile.value != null) {
        await Get.dialog(AlertDialog(
          title: const Text('Atenção'),
          content:
              const Text('Imagem não salva pois não há conexão com a internet'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('Ok'),
            ),
          ],
        ));
      }
      isLoading(false);
      Get.back();
    } catch (e) {
      isLoading(false);
      Get.snackbar('Erro', 'Erro ao salvar produto $e');
    }
  }

  Future<void> editar() async {
    try {
      isLoading(true);
      ProductModel produtoModel = ProductModel(
        id: produto.value!.id,
        nome: nome.value!,
        valor: valor.value!,
        imagem: produto.value!.imagem,
        temComissao: comissao.value,
        lastModified: DateTime.now(),
        isFavorite: produto.value!.isFavorite,
        variations: variations.toList(),
      );
      String? oldImage = produto.value!.imagem;
      final result = await _productRepository.updateProduct(produtoModel,
          imagePath: imagemMudou.value ? imagemFile.value!.path : null);
      if (result.imagem == oldImage && imagemFile.value != null) {
        await Get.dialog(AlertDialog(
          title: const Text('Atenção'),
          content:
              const Text('Imagem não salva pois não há conexão com a internet'),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('Ok'),
            ),
          ],
        ));
      }
      isLoading(false);
      Get.back();
    } catch (e) {
      isLoading(false);
      Get.snackbar('Erro', 'Erro ao editar produto $e');
    }
  }
}
