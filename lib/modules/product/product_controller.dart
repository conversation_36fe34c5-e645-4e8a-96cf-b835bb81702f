import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProductController extends GetxController {
  final ProductRepository _productRepository;

  ProductController({required ProductRepository productRepository})
      : _productRepository = productRepository;

  final RxList<ProductModel> products = <ProductModel>[].obs;
  final RxList<ProductModel> productsFiltered = <ProductModel>[].obs;

  RxString search = ''.obs;

  TextEditingController searchController = TextEditingController();

  final workers = <Worker>[].obs;

  RxBool isLoading = true.obs;

  FocusNode searchFocus = FocusNode();

  Rxn<UserModel> user = Rxn<UserModel>();

  @override
  void onInit() {
    _productRepository.getProductsFromCache().then((products) {
      this.products.assignAll(products);
      productsFiltered.assignAll(products);
      isLoading(false);
    });
    init();
    super.onInit();
  }

  void init() async {
    user.value = await Get.find<UserService>().getUserAuthenticated();
    user.refresh();
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchProduct(),
        time: const Duration(milliseconds: 500),
      ),
    );
    super.onReady();
  }

  Future<void> getProducts() async {
    _productRepository.getProductsFromCache().then((products) {
      this.products.assignAll(products);
      productsFiltered.assignAll(products);
      isLoading(false);
      if (search.isNotEmpty) {
        searchProduct();
      }
    });
  }

  Future<void> goToFormProduct() async {
    await Get.toNamed('/form_product');
    _productRepository.getProducts().then((products) {
      this.products.assignAll(products);
    });
  }

  Future<void> goToProductDetails(ProductModel product) async {
    await Get.toNamed('/product_details', arguments: product);
    getProducts();
  }

  searchProduct() {
    if (search.isEmpty) {
      productsFiltered.assignAll(products);
      return;
    }
    productsFiltered.assignAll(
      products.where(
        (prod) => TextHelper.removeAcento(prod.nome.toLowerCase())
            .contains(TextHelper.removeAcento(search.toLowerCase())),
      ),
    );
  }

  clearSearch() {
    searchController.clear();
    search('');
    productsFiltered.assignAll(products);
    searchFocus.unfocus();
  }

  void goDeletedProducts() async {
    await Get.toNamed('/deleted_products');
    getProducts();
  }
}
