import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/product/product_details/product_details_bindings.dart';
import 'package:fl_app/modules/product/product_details/product_details_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ProductDetailsModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/product_details',
      page: () => const ProductDetailsPage(),
      binding: ProductDetailsBindings(),
    ),
  ];
}
