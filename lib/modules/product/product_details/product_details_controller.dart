import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProductDetailsController extends GetxController {
  final ProductRepository _productRepository;

  ProductDetailsController({required ProductRepository productRepository})
      : _productRepository = productRepository;

  final product = (Get.arguments as ProductModel).obs;

  RxBool isFavorite = (Get.arguments as ProductModel).isFavorite.obs;

  Rxn<UserModel> user = Rxn<UserModel>();

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    user.value = await Get.find<UserService>().getUserAuthenticated();
    user.refresh();
  }

  Future<void> goToFormProductEdit() async {
    await Get.toNamed('/form_product', arguments: product.value);
    _productRepository.getProduct(product.value.id!).then((updatedProduct) {
      product(updatedProduct);
    });
  }

  Future<void> deleteProduct() async {
    final delete = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Apagar produto'),
        content:
            Text('Deseja realmente apagar o produto ${product.value.nome}?'),
        actions: [
          TextButton(
              onPressed: () {
                Get.back(result: true);
              },
              child: Text(
                'Apagar',
                style: TextStyle(color: Get.theme.colorScheme.error),
              )),
          TextButton(
              onPressed: () {
                Get.back(result: false);
              },
              child: const Text('Cancelar')),
        ],
      ),
    );

    if (delete != null && delete) {
      await _productRepository.deleteProduct(product.value);
      Get.back();
      Get.showSnackbar(
        GetSnackBar(
          title: 'Produto apagado',
          backgroundColor: Get.theme.colorScheme.error,
          messageText: Text(
            'O produto ${product.value.nome} foi apagado.',
            style: TextStyle(color: Get.theme.colorScheme.onError),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> toggleFavorite() async {
    product.value.isFavorite = !product.value.isFavorite;
    isFavorite(product.value.isFavorite);
    await _productRepository.updateProduct(product.value);
  }

  Future<void> restoreProduct() async {
    await _productRepository.restoreProduct(product.value);
    Get.back();
    Get.showSnackbar(
      GetSnackBar(
        title: 'Produto restaurado',
        message: 'O produto ${product.value.nome} foi restaurado.',
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
