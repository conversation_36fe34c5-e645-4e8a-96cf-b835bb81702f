import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './product_details_controller.dart';

class ProductDetailsPage extends GetView<ProductDetailsController> {
  const ProductDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Detalhes do Produto'),
        actions: [
          Obx(() {
            final product = controller.product.value;
            if (!product.isDeleted) {
              return Row(
                children: [
                  IconButton(
                    onPressed: () => controller.deleteProduct(),
                    icon: const Icon(Icons.delete_outline),
                    tooltip: 'Deletar Produto',
                  ),
                  Obx(() {
                    return IconButton(
                      onPressed: () => controller.toggleFavorite(),
                      icon: Icon(
                        controller.isFavorite.value
                            ? Icons.star
                            : Icons.star_border,
                        color:
                            controller.isFavorite.value ? Colors.amber : null,
                      ),
                      tooltip: controller.isFavorite.value
                          ? 'Remover dos Favoritos'
                          : 'Adicionar aos Favoritos',
                    );
                  }),
                  IconButton(
                    onPressed: () => controller.goToFormProductEdit(),
                    icon: const Icon(Icons.edit_outlined),
                    tooltip: 'Editar Produto',
                  ),
                ],
              );
            } else {
              return const SizedBox();
            }
          }),
        ],
      ),
      body: Obx(() {
        final product = controller.product.value;
        final user = controller.user.value;

        if (product.id == null) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 1) IMAGEM
                _buildProductImage(product, theme),
                const SizedBox(height: 20),

                // 2) NOME E FAVORITO
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        product.nome,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // 3) VALOR
                _buildPriceSection(product, theme),
                const SizedBox(height: 16),

                // 4) COMISSÃO
                if (product.temComissao &&
                    user != null &&
                    user.admin &&
                    !product.isDeleted)
                  _buildCommissionBadge(theme),

                if (product.temComissao &&
                    user != null &&
                    user.admin &&
                    !product.isDeleted)
                  const SizedBox(height: 16),

                // 5) VARIAÇÕES
                if (product.variations.isNotEmpty)
                  _buildVariationsSection(product, theme),

                if (product.variations.isNotEmpty) const SizedBox(height: 16),

                // 6) PRODUTO EXCLUÍDO (se for o caso)
                if (user != null && user.admin && product.isDeleted)
                  _buildDeletedProductInfo(product, theme),

                // 7) INFORMAÇÕES ADICIONAIS (Expansível)
                const SizedBox(height: 16),
                _buildAdditionalInfoSection(product, theme),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildProductImage(ProductModel product, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: Hero(
        tag: product.id!,
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: product.imagem != null && product.imagem!.isNotEmpty
              ? CachedNetworkImage(
                  imageUrl: product.imagem!,
                  fit: BoxFit.fitHeight,
                  placeholder: (_, __) =>
                      const Center(child: CircularProgressIndicator()),
                  errorWidget: (_, __, ___) => Container(
                    color: theme.colorScheme.surfaceContainerHighest,
                    child: Center(
                      child: FaIcon(
                        FontAwesomeIcons.image,
                        color: theme.colorScheme.onSurfaceVariant,
                        size: 40,
                      ),
                    ),
                  ),
                  memCacheHeight: 500,
                  memCacheWidth: 500,
                )
              : Container(
                  color: theme.colorScheme.surfaceContainerHighest,
                  child: Center(
                    child: FaIcon(
                      FontAwesomeIcons.solidImage,
                      color: theme.colorScheme.onSurfaceVariant,
                      size: 40,
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildPriceSection(ProductModel product, ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Valor:',
          style: theme.textTheme.titleMedium,
        ),
        Text(
          NumberFormatHelper.format(product.valor.toDouble()),
          style: theme.textTheme.headlineSmall?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildCommissionBadge(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.tertiaryContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.percent, color: theme.colorScheme.onTertiaryContainer),
          const SizedBox(width: 8),
          Text(
            'Produto com comissão',
            style: TextStyle(
              color: theme.colorScheme.onTertiaryContainer,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVariationsSection(ProductModel product, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Variações',
          style: theme.textTheme.titleLarge,
        ),
        const SizedBox(height: 8),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: product.variations.length,
          separatorBuilder: (_, __) => const Divider(),
          itemBuilder: (context, index) {
            final variation = product.variations[index];
            return ListTile(
              contentPadding: EdgeInsets.zero,
              leading:
                  variation.imageUrl != null && variation.imageUrl!.isNotEmpty
                      ? CircleAvatar(
                          backgroundColor: theme.colorScheme.surfaceContainer,
                          child: CachedNetworkImage(
                            imageUrl: variation.imageUrl!,
                            fit: BoxFit.fitHeight,
                            memCacheHeight: 100,
                            memCacheWidth: 100,
                            placeholder: (_, __) =>
                                Icon(Icons.image, color: Colors.grey[100]),
                            errorWidget: (_, __, ___) =>
                                const Icon(Icons.image, color: Colors.red),
                          ),
                        )
                      : CircleAvatar(
                          child: Icon(Icons.image,
                              color: theme.colorScheme.onSurfaceVariant),
                        ),
              title: Text(variation.name),
              trailing: Text(
                NumberFormatHelper.format(variation.price.toDouble()),
                style: TextStyle(color: theme.colorScheme.primary),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDeletedProductInfo(ProductModel product, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.delete_forever,
                  color: theme.colorScheme.onErrorContainer),
              const SizedBox(width: 8),
              Text(
                'Produto Excluído',
                style: TextStyle(
                  color: theme.colorScheme.onErrorContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (product.deletedByName != null)
            _buildInfoRow('Excluído por:', product.deletedByName!, theme),
          _buildInfoRow(
            'Em:',
            '${DateTimeHelper.getFormattedDate(product.lastModified)} às ${DateTimeHelper.getFormattedTime(product.lastModified)}',
            theme,
          ),
          const SizedBox(height: 12),
          Align(
            alignment: Alignment.centerRight,
            child: FilledButton.tonalIcon(
              onPressed: () => controller.restoreProduct(),
              icon: const Icon(Icons.restore_from_trash),
              label: const Text('Restaurar'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoSection(ProductModel product, ThemeData theme) {
    return ExpansionTile(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      collapsedShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: const BorderSide(color: Colors.grey),
      ),
      title: const Text('Informações Adicionais'),
      children: [
        _buildInfoTable(product, theme),
      ],
    );
  }

  Widget _buildInfoTable(ProductModel product, ThemeData theme) {
    final mapData = product.toMap();

    final rows = mapData.entries.map((entry) {
      return TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: Text(entry.key,
                style: const TextStyle(fontWeight: FontWeight.w600)),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: Text(entry.value?.toString() ?? 'N/A'),
          ),
        ],
      );
    }).toList();

    return Table(
      border: TableBorder.all(
        color: theme.dividerColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
      columnWidths: const {
        0: FixedColumnWidth(120),
        1: FlexColumnWidth(),
      },
      children: rows,
    );
  }

  Widget _buildInfoRow(String label, String value, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onPrimary,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child:
              Text(value, style: TextStyle(color: theme.colorScheme.onPrimary)),
        ),
      ]),
    );
  }
}
