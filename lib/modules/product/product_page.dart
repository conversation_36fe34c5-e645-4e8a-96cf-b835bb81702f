import 'package:fl_app/application/ui/widgets/product_grid_item.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './product_controller.dart';

class ProductPage extends GetView<ProductController> {
  const ProductPage({super.key});

  Widget _buildSearchField(BuildContext context) {
    return TextField(
      controller: controller.searchController,
      focusNode: controller.searchFocus,
      decoration: InputDecoration(
        hintText: 'Pesquisar produtos',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: controller.search.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: controller.clearSearch,
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.onSurface,
            width: 1,
          ),
        ),
        fillColor: Theme.of(context).cardColor,
        filled: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      ),
      onChanged: controller.search.call,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Produtos'),
        actions: [
          IconButton(
            onPressed: controller.goToFormProduct,
            icon: const Icon(Icons.add),
          ),
          //pop up menu with "Ver produtos excluídos"
          PopupMenuButton(
            itemBuilder: (context) {
              return [
                const PopupMenuItem(
                  value: 0,
                  child: Text('Ver produtos excluídos'),
                ),
              ];
            },
            onSelected: (value) {
              if (value == 0) {
                controller.goDeletedProducts();
              }
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: _buildSearchField(context),
          ),
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              }
              final products = controller.productsFiltered;
              if (products.isEmpty) {
                return const Center(
                  child: Text('Nenhum produto encontrado'),
                );
              }

              return RefreshIndicator(
                onRefresh: controller.getProducts,
                child: GridView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount:
                          MediaQuery.of(context).size.width < 600 ? 3 : 4,
                      childAspectRatio: 0.7,
                      crossAxisSpacing: 4,
                      mainAxisSpacing: 4),
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    return ProductGridItem(
                      product: product,
                      onTap: () => controller.goToProductDetails(product),
                      showComission: controller.user.value != null &&
                          controller.user.value!.admin,
                    );
                  },
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
