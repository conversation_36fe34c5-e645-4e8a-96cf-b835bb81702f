import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/product/deleted_products/deleted_products_bindings.dart';
import 'package:fl_app/modules/product/deleted_products/deleted_products_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class DeletedProductsModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/deleted_products',
      page: () => const DeletedProductsPage(),
      binding: DeletedProductsBindings(),
    ),
  ];
}
