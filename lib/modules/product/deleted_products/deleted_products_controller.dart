// lib/modules/product/controllers/deleted_products_controller.dart

import 'dart:developer';

import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_app/models/user_model.dart';

class DeletedProductsController extends GetxController {
  final ProductRepository _productRepository;

  DeletedProductsController({required ProductRepository productRepository})
      : _productRepository = productRepository;

  final RxList<ProductModel> products = <ProductModel>[].obs;
  final RxList<ProductModel> productsFiltered = <ProductModel>[].obs;

  RxString search = ''.obs;

  TextEditingController searchController = TextEditingController();

  RxBool isLoading = true.obs;

  FocusNode searchFocus = FocusNode();

  Rxn<UserModel> user = Rxn<UserModel>();

  @override
  void onInit() {
    super.onInit();
    getDeletedProducts();
  }

  @override
  void onReady() {
    super.onReady();
    debounce(
      search,
      (_) => searchProduct(),
      time: const Duration(milliseconds: 500),
    );
  }

  Future<void> getDeletedProducts() async {
    try {
      isLoading(true);
      List<ProductModel> allProducts =
          await _productRepository.getDeletedProductsFromCache();
      products.assignAll(allProducts);
      productsFiltered.assignAll(allProducts);
      isLoading(false);
      if (search.isNotEmpty) {
        searchProduct();
      }
    } catch (e) {
      isLoading(false);
      Get.snackbar('Erro', 'Falha ao carregar produtos excluídos.');
      log('Erro ao obter produtos excluídos: $e');
    }
  }

  void searchProduct() {
    if (search.isEmpty) {
      productsFiltered.assignAll(products);
      return;
    }
    productsFiltered.assignAll(
      products.where(
        (prod) => prod.nome.toLowerCase().contains(search.value.toLowerCase()),
      ),
    );
  }

  void clearSearch() {
    searchController.clear();
    search('');
    productsFiltered.assignAll(products);
    searchFocus.unfocus();
  }

  Future<void> goToProductDetails(ProductModel product) async {
    await Get.toNamed('/product_details', arguments: product);
    await getDeletedProducts();
  }

  Future<void> restoreProduct(ProductModel product) async {
    try {
      await _productRepository.restoreProduct(product);
      Get.snackbar('Sucesso', 'Produto restaurado com sucesso.');
      await getDeletedProducts();
    } catch (e) {
      Get.snackbar('Erro', 'Falha ao restaurar o produto.');
      log('Erro ao restaurar produto: $e');
    }
  }
}
