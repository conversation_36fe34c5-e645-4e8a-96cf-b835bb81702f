import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/product/deleted_products/deleted_products_module.dart';
import 'package:fl_app/modules/product/form_product/form_product_module.dart';
import 'package:fl_app/modules/product/product_bindings.dart';
import 'package:fl_app/modules/product/product_details/product_details_module.dart';
import 'package:fl_app/modules/product/product_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class ProductModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/products',
      page: () => const ProductPage(),
      binding: ProductBindings(),
    ),
    ...FormProductModule().routers,
    ...ProductDetailsModule().routers,
    ...DeletedProductsModule().routers,
  ];
}
