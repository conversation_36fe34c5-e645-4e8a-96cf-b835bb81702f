import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:widgets_to_image/widgets_to_image.dart';

class SaleDayDetailsController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final OrderRepository _orderRepository;
  final AppStateService _appStateService;

  SaleDayDetailsController({
    required OrderRepository orderRepository,
    required AppStateService appStateService,
  })  : _orderRepository = orderRepository,
        _appStateService = appStateService;

  Rxn<UserModel?> user = Rxn<UserModel?>();

  MapEntry<DateTime, List<OrderModel>> saleDay = Get.arguments[0];
  RxList<OrderModel> ordersUpdated = <OrderModel>[].obs;

  SaleRouteModel saleRoute = Get.arguments[1];

  RxList<Map<String, dynamic>> productsSold = <Map<String, dynamic>>[].obs;
  RxMap<String, int> sallerSales = <String, int>{}.obs;

  RxBool isLoading = false.obs;

  bool isFromSalesRoute = Get.arguments[2];

  WidgetsToImageController widgetsToImageController =
      WidgetsToImageController();

  final List<Tab> myTabs = <Tab>[
    const Tab(text: 'Pedidos'),
    const Tab(text: 'Vendedores'),
    const Tab(text: 'Produtos'),
  ];

  late TabController tabController;

  RxInt selectedTab = 0.obs;

  UniqueKey columnKey = UniqueKey();

  RxBool showOnlyCommission = false.obs;

  RxBool showVariations = false.obs;

  @override
  void onInit() {
    tabController = TabController(length: myTabs.length, vsync: this);
    Get.find<UserService>().getUserAuthenticated().then((value) {
      user.value = value;
    });
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    loadProductsSold();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  Future<void> refreshSaleOrders() async {
    isLoading(true);
    final initialDate = DateTime(
        saleDay.key.year, saleDay.key.month, saleDay.key.day, 0, 0, 0, 0, 0);
    ordersUpdated.value = await _orderRepository.getOrdersFromCache(
        routeId: saleRoute.id!, initialDate: initialDate);
    ordersUpdated.refresh();
    await loadProductsSold();
  }

  Future<void> finishSale() async {
    _appStateService.finishSaleInRoute();
  }

  Future<void> loadProductsSold() async {
    productsSold.clear();
    final ordersTemp = ordersUpdated.isNotEmpty ? ordersUpdated : saleDay.value;

    // Vamos montar um map intermediário:
    // chave = productBaseName (ou id se preferir), valor = objeto com totals + sub-lista de variações
    final Map<String, Map<String, dynamic>> groupedProducts = {};

    for (var order in ordersTemp) {
      for (var product in order.products) {
        // Descobrir se é AV (customValue != 0)
        bool isAvista = product.customValue != 0 && product.quantity != 0;

        // Descobrir a chave de agrupamento
        // Se "productBaseName" for null, usamos o product.name inteiro.
        final baseKey = product.productBaseName?.isNotEmpty == true
            ? product.productBaseName!
            : product.name;

        // Se o group ainda não existir, cria
        if (!groupedProducts.containsKey(baseKey)) {
          groupedProducts[baseKey] = {
            'productBaseName': baseKey,
            'temComissao': product.temComissao,
            'quantity_prazo': 0,
            'quantity_avista': 0,
            'variations': <Map<String, dynamic>>[],
          };
        }

        // Atualiza o total "flat"
        if (isAvista) {
          groupedProducts[baseKey]!['quantity_avista'] += product.quantity;
        } else {
          groupedProducts[baseKey]!['quantity_prazo'] += product.quantity;
        }

        if (product.variationId != null) {
          final variationName = product.name; // ou outro critério

          final variationsList = groupedProducts[baseKey]!['variations']
              as List<Map<String, dynamic>>;

          final existingVariation = variationsList.firstWhereOrNull(
            (v) => v['variationName'] == variationName,
          );

          if (existingVariation == null) {
            variationsList.add({
              'variationName': variationName,
              'quantity_avista': isAvista ? product.quantity : 0,
              'quantity_prazo': isAvista ? 0 : product.quantity,
            });
          } else {
            if (isAvista) {
              existingVariation['quantity_avista'] += product.quantity;
            } else {
              existingVariation['quantity_prazo'] += product.quantity;
            }
          }
        }
      }
    }

    // Transforma esse map em uma lista “productsSold” semelhante à anterior
    // mas agora com a chave 'variations'
    productsSold.value = groupedProducts.values.map((value) {
      // Optional: ordenar as variações dentro de cada item
      List<Map<String, dynamic>> variationsList = value['variations'];
      variationsList.sort((a, b) {
        // Exemplo: Ordenar pelo maior quantity_prazo, etc
        return (b['quantity_prazo'] as int)
            .compareTo(a['quantity_prazo'] as int);
      });

      return {
        ...value,
        'variations': variationsList,
      };
    }).toList();

    // Agora podemos ordenar a lista final
    productsSold.sort((a, b) {
      int qA = a['quantity_prazo'] as int;
      int qB = b['quantity_prazo'] as int;
      if (qA == qB) {
        return (a['productBaseName'] as String)
            .compareTo(b['productBaseName'] as String);
      } else {
        return qB.compareTo(qA);
      }
    });

    productsSold.refresh();

    await loadSallerSales();
    isLoading(false);
  }

  Future<void> shareImage() async {
    try {
      Directory directory = await getTemporaryDirectory();
      Uint8List? pngBytes = await widgetsToImageController.capture();
      if (pngBytes != null) {
        File imgFile = File('${directory.path}/saleday ${saleDay.key}.png');
        imgFile.writeAsBytes(pngBytes);

        await Share.shareXFiles(
          [
            XFile.fromData(pngBytes,
                name: 'saleday ${saleDay.key}.png',
                path: imgFile.path,
                mimeType: 'image/png')
          ],
          text:
              '${saleRoute.name} - ${DateTimeHelper.getFormattedDate(saleDay.key)}',
        );
      }
    } catch (e) {
      log('Erro ao compartilhar imagem: $e');
    }
  }

  Future<void> loadSallerSales() async {
    List<String> sellerNameList = [];
    final ordersTemp = ordersUpdated.isNotEmpty ? ordersUpdated : saleDay.value;
    for (var order in ordersTemp) {
      if (sellerNameList.isEmpty) {
        sellerNameList.add(order.sellerName);
      } else {
        bool found = false;
        for (var userName in sellerNameList) {
          if (userName == order.sellerName) {
            found = true;
            break;
          }
        }
        if (!found) {
          sellerNameList.add(order.sellerName);
        }
      }
    }

    Map<String, int> sellerSalesMap = {};

    for (var sellerName in sellerNameList) {
      int total = 0;
      for (var order in saleDay.value) {
        if (order.sellerName == sellerName) {
          for (var product in order.products) {
            if (product.temComissao == true && (product.customValue == 0)) {
              total += product.quantity;
            }
          }
        }
      }
      sellerSalesMap[sellerName] = total;
    }
    //sort seller sales by quantity
    sellerSalesMap = Map.fromEntries(sellerSalesMap.entries.toList()
      ..sort((e1, e2) => e2.value.compareTo(e1.value)));

    //remove sallerSales with 0 sales
    sellerSalesMap.removeWhere((key, value) => value == 0);

    sallerSales.addAll(sellerSalesMap);
  }

  changeTab(int value) {
    selectedTab.value = value;
  }
}
