import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/order_list_card.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:widgets_to_image/widgets_to_image.dart';
import './sale_day_details_controller.dart';

class SaleDayDetailsPage extends GetView<SaleDayDetailsController> {
  const SaleDayDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dia de Venda'),
        actions: [
          controller.isFromSalesRoute
              ? ElevatedButton.icon(
                  onPressed: () async {
                    final result = await Get.dialog<bool>(
                      AlertDialog(
                        title: const Text('Finalizar venda'),
                        content: const Text('Finalizar venda?'),
                        actions: [
                          TextButton(
                            onPressed: () {
                              Get.back(result: true);
                            },
                            child: const Text('Sim'),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              Get.back(result: false);
                            },
                            child: const Text('Não'),
                          ),
                        ],
                      ),
                    );
                    if (result == true) {
                      controller.finishSale();
                      Get.back();
                    }
                  },
                  icon: const Icon(
                    Icons.done,
                    color: Colors.indigo,
                  ),
                  label: const Text(
                    'Finalizar Venda',
                    style: TextStyle(color: Colors.indigo),
                  ),
                  style: ElevatedButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      side: const BorderSide(color: Colors.indigo),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    backgroundColor: Colors.white,
                  ),
                )
              : IconButton(
                  onPressed: () => controller.shareImage(),
                  icon: const Icon(Icons.share),
                ),
        ],
      ),
      body: SingleChildScrollView(
        child: WidgetsToImage(
          controller: controller.widgetsToImageController,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Obx(
              () => Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        controller.saleRoute.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        DateTimeHelper.getFormattedDate(controller.saleDay.key),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 2),
                  if (controller.user.value != null &&
                      controller.user.value!.admin)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Total',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'R\$ ${controller.saleDay.value.fold<double>(0, (previousValue, element) => previousValue + element.calculateTotal()).toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'P.V',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${controller.saleDay.value.fold<int>(0, (previousValue, element) => previousValue + element.products.fold<int>(0, (previousValue, element) => element.temComissao && (element.customValue == 0) ? previousValue + (element.quantity) : previousValue))}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'A.V',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${controller.saleDay.value.fold<int>(0, (previousValue, element) => previousValue + element.products.fold<int>(0, (previousValue, element) => element.temComissao && (element.customValue != 0) ? previousValue + (element.quantity) : previousValue))}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  if ((controller.user.value != null &&
                          controller.user.value!.admin) ||
                      controller.isFromSalesRoute)
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: TabBar(
                        controller: controller.tabController,
                        tabs: controller.myTabs,
                        onTap: (value) => controller.changeTab(value),
                      ),
                    ),
                  if (controller.user.value != null &&
                      !controller.user.value!.admin &&
                      !controller.isFromSalesRoute)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Vendas',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          '${controller.saleDay.value.length}',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  if ((controller.user.value != null &&
                          controller.user.value!.admin) ||
                      controller.isFromSalesRoute)
                    Obx(() {
                      if (controller.isLoading.value) {
                        return const Center(
                          child: CircularProgressIndicator(
                            strokeCap: StrokeCap.round,
                          ),
                        );
                      }
                      if (controller.selectedTab.value == 0) {
                        return Obx(
                          () => Column(
                            children: [
                              controller.ordersUpdated.isNotEmpty
                                  ? Column(
                                      children: [
                                        ...controller.ordersUpdated.map(
                                          (e) => OrderListCard(
                                            order: e,
                                            onTap: (order) async {
                                              await Get.toNamed('/view-order',
                                                  arguments: order);
                                              controller.refreshSaleOrders();
                                            },
                                          ),
                                        ),
                                      ],
                                    )
                                  : Column(
                                      children: [
                                        ...controller.saleDay.value.map(
                                          (e) => OrderListCard(
                                            order: e,
                                            onTap: (order) {
                                              Get.toNamed('/view-order',
                                                  arguments: order);
                                            },
                                          ),
                                        )
                                      ],
                                    )
                            ],
                          ),
                        );
                      } else if (controller.selectedTab.value == 1) {
                        return Column(
                          children: [
                            ...controller.sallerSales.keys.map((e) => Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(e),
                                    Text(controller.sallerSales[e].toString()),
                                  ],
                                ))
                          ],
                        );
                      } else if (controller.selectedTab.value == 2) {
                        return SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // BOTÃO / CHIP PARA FILTRAR
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Obx(
                                    () => ChoiceChip(
                                      label: const Text('Comissão'),
                                      selected:
                                          controller.showOnlyCommission.value,
                                      onSelected: (bool selected) {
                                        controller.showOnlyCommission.value =
                                            selected;
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Obx(
                                    () => ChoiceChip(
                                      label: const Text('Variações'),
                                      selected: controller.showVariations.value,
                                      onSelected: (bool selected) {
                                        controller.showVariations.value =
                                            selected;
                                      },
                                    ),
                                  ),
                                ],
                              ),

                              // A Tabela completa
                              Table(
                                columnWidths: const {
                                  0: FlexColumnWidth(3),
                                  1: FlexColumnWidth(1),
                                  2: FlexColumnWidth(1),
                                },
                                defaultVerticalAlignment:
                                    TableCellVerticalAlignment.middle,
                                border: TableBorder.symmetric(
                                  outside:
                                      BorderSide(color: Colors.grey.shade300),
                                ),
                                children: [
                                  // --- Cabeçalho da Tabela ---
                                  TableRow(
                                    decoration: BoxDecoration(
                                      color: Get.isDarkMode
                                          ? Colors.grey[700]
                                          : Colors.grey[300],
                                    ),
                                    children: const [
                                      Padding(
                                        padding: EdgeInsets.all(8.0),
                                        child: Text(
                                          'Produto',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.all(8.0),
                                        child: Text(
                                          'P.V',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.all(8.0),
                                        child: Text(
                                          'A.V',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                      ),
                                    ],
                                  ),

                                  // --- Linhas de dados ---
                                  for (var product
                                      in controller.productsSold.where((p) {
                                    // Filtrar apenas quem tem comissao, se o toggle estiver ativo
                                    if (controller.showOnlyCommission.value) {
                                      return p['temComissao'] == true;
                                    }
                                    return true;
                                  })) ...[
                                    // Linha do Produto-Base
                                    TableRow(
                                      decoration: BoxDecoration(
                                        color: Get.isDarkMode
                                            ? Colors.grey[850]
                                            : Colors.grey[50],
                                        border: Border(
                                          bottom: BorderSide(
                                              color: Colors.grey.shade300),
                                        ),
                                      ),
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Text(
                                            product['productBaseName'],
                                            style: TextStyle(
                                              fontWeight:
                                                  product['temComissao'] == true
                                                      ? FontWeight.w600
                                                      : FontWeight.normal,
                                              color:
                                                  product['temComissao'] == true
                                                      ? (Get.isDarkMode
                                                          ? Colors.blue
                                                          : Colors.blue[800])
                                                      : (Get.isDarkMode
                                                          ? Colors.white
                                                          : Colors.black),
                                            ),
                                          ),
                                        ),
                                        Center(
                                          child: Text(
                                            product['quantity_prazo']
                                                .toString(),
                                            style: TextStyle(
                                              fontWeight:
                                                  product['temComissao'] == true
                                                      ? FontWeight.w600
                                                      : FontWeight.normal,
                                            ),
                                          ),
                                        ),
                                        Center(
                                          child: Text(
                                            product['quantity_avista']
                                                .toString(),
                                            style: TextStyle(
                                              fontWeight:
                                                  product['temComissao'] == true
                                                      ? FontWeight.w600
                                                      : FontWeight.normal,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                    // Linhas das Variações (somente quando existir variação)
                                    if ((product['variations'] as List)
                                            .isNotEmpty &&
                                        controller.showVariations.value)
                                      for (var variation
                                          in product['variations'])
                                        TableRow(
                                          decoration: BoxDecoration(
                                            color: Get.isDarkMode
                                                ? Colors.grey[900]
                                                : Colors.grey[100],
                                            border: Border(
                                              bottom: BorderSide(
                                                  color: Colors.grey.shade300),
                                            ),
                                          ),
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 16.0,
                                                vertical: 8.0,
                                              ),
                                              child: Row(
                                                children: [
                                                  // Prefixo para “indentação” ou bullet
                                                  const Text('   \u{2022}  '),
                                                  // Nome da variação
                                                  Expanded(
                                                    child: Text(
                                                      variation[
                                                          'variationName'],
                                                      style: const TextStyle(
                                                          fontSize: 14),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Center(
                                              child: Text(
                                                variation['quantity_prazo']
                                                    .toString(),
                                                style: const TextStyle(
                                                    fontSize: 14),
                                              ),
                                            ),
                                            Center(
                                              child: Text(
                                                variation['quantity_avista']
                                                    .toString(),
                                                style: const TextStyle(
                                                    fontSize: 14),
                                              ),
                                            ),
                                          ],
                                        ),
                                  ],
                                ],
                              ),
                            ],
                          ),
                        );
                      } else {
                        return Container();
                      }
                    }),
                  if (controller.user.value != null &&
                      !controller.user.value!.admin &&
                      !controller.isFromSalesRoute)
                    ...controller.saleDay.value.map(
                      (e) => OrderListCard(
                        order: e,
                        onTap: (order) {
                          Get.toNamed('/view-order', arguments: order);
                        },
                      ),
                    )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
