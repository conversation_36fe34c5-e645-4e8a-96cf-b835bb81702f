import 'package:fl_app/modules/sales_routes/add_sale_route/add_sale_route_module.dart';
import 'package:fl_app/modules/sales_routes/sale_day_details/sale_day_details_module.dart';
import 'package:fl_app/modules/sales_routes/sale_details/sale_details_module.dart';
import 'package:fl_app/modules/sales_routes/sale_route_details/sale_route_details_module.dart';
import 'package:fl_app/modules/sales_routes/sales_from_route/sales_from_route_module.dart';
import 'package:fl_app/modules/sales_routes/sales_routes_bindings.dart';
import 'package:fl_app/modules/sales_routes/sales_routes_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

import '../../application/modules/module.dart';

class SalesRoutesModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/sales_routes',
      page: () => const SalesRoutesPage(),
      binding: SalesRoutesBindings(),
    ),
    ...AddSaleRouteModule().routers,
    ...SaleRouteDetailsModule().routers,
    ...SalesFromRouteModule().routers,
    ...SaleDayDetailsModule().routers,
    ...SaleDetailsModule().routers,
  ];
}
