import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/sales_routes/sales_from_route/sales_from_route_bindings.dart';
import 'package:fl_app/modules/sales_routes/sales_from_route/sales_from_route_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SalesFromRouteModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/sales_from_route',
      page: () => const SalesFromRoutePage(),
      binding: SalesFromRouteBindings(),
    ),
  ];
}
