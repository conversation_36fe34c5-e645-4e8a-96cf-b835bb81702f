import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';

import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';

class SalesFromRouteController extends GetxController {
  SaleRouteModel saleRoute = Get.arguments as SaleRouteModel;

  final OrderRepository _orderRepository;

  SalesFromRouteController({required OrderRepository orderRepository})
      : _orderRepository = orderRepository;

  RxMap<DateTime, List<OrderModel>> orders = <DateTime, List<OrderModel>>{}.obs;
  RxList<DateTime> dates = <DateTime>[].obs;

  Rxn<UserModel> user = Rxn<UserModel>();
  RxBool isLoading = false.obs;
  RxBool showOnlyRelevant = true.obs; // Por padrão mostra apenas as relevantes

  bool get isUserAdmin => user.value != null && user.value!.admin;

  @override
  void onReady() {
    super.onReady();
    isLoading.value = true;
    refreshOrders();
    Get.find<UserService>().getUserAuthenticated().then((value) {
      user.value = value;
    });
  }

  // Toggle entre mostrar todas as vendas ou apenas as relevantes
  void toggleRelevantFilter() {
    showOnlyRelevant.value = !showOnlyRelevant.value;
  }

  // Retorna as datas que devem ser exibidas com base no filtro atual
  List<DateTime> get filteredDates {
    if (!showOnlyRelevant.value) return dates;
    return dates.where((date) => (orders[date]?.length ?? 0) >= 20).toList();
  }

  Future<void> refreshOrders() async {
    isLoading.value = true;
    try {
      await _orderRepository
          .getOrdersFromRouteByDate(saleRoute.id!)
          .then((value) {
        orders.assignAll(value);
        dates.assignAll(value.keys.toList());
        dates.sort(
            (a, b) => b.compareTo(a)); // Ordena as datas de forma decrescente
      });
    } catch (e) {
      Get.snackbar(
        'Erro',
        'Não foi possível carregar os dados das vendas',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  goToSaleDayDetails(DateTime date) {
    final ordersSorted = orders[date]!;
    ordersSorted.sort((a, b) => a.date.compareTo(b.date));
    Get.toNamed('/sale_day_details', arguments: [
      MapEntry<DateTime, List<OrderModel>>(date, orders[date]!),
      saleRoute,
      false
    ]);
  }

  // Métodos para calcular estatísticas
  int getTotalOrdersCount() {
    int total = 0;
    for (var dateOrders in orders.values) {
      total += dateOrders.length;
    }
    return total;
  }

  double getTotalSalesValue() {
    double total = 0;
    for (var dateOrders in orders.values) {
      for (var order in dateOrders) {
        total += order.calculateTotal();
      }
    }
    return total;
  }

  double getTotalUnpaidSalesValue() {
    double total = 0;
    for (var dateOrders in orders.values) {
      for (var order in dateOrders) {
        if (!order.isPaid && !order.isJoined) {
          total += order.calculateTotal();
        }
      }
    }
    return total;
  }
}
