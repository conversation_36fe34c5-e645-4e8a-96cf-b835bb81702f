import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './sales_from_route_controller.dart';
import 'package:intl/intl.dart';

class SalesFromRoutePage extends GetView<SalesFromRouteController> {
  const SalesFromRoutePage({super.key});

  @override
  Widget build(BuildContext context) {
    final currencyFormat =
        NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$');

    return Scaffold(
      appBar: AppBar(
        title: Text('Vendas ${controller.saleRoute.name}'),
        actions: [
          // Botão para alternar entre mostrar todas ou apenas vendas relevantes
          Obx(() => IconButton(
                icon: Icon(
                  controller.showOnlyRelevant.value
                      ? Icons.filter_alt
                      : Icons.filter_alt_off,
                ),
                onPressed: controller.toggleRelevantFilter,
                tooltip: controller.showOnlyRelevant.value
                    ? 'Mostrar todas as vendas'
                    : 'Mostrar apenas vendas relevantes',
              )),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.refreshOrders(),
            tooltip: 'Atualizar dados',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (controller.dates.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.info_outline, size: 60, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  'Nenhuma venda encontrada',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Puxe para baixo para atualizar',
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        // Lista de datas filtradas com base na configuração atual
        final displayDates = controller.filteredDates;

        // Se não houver datas para mostrar após a filtragem
        if (displayDates.isEmpty && controller.dates.isNotEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.filter_alt_off, size: 60, color: Colors.grey),
                const SizedBox(height: 16),
                Text(
                  'Nenhuma venda relevante encontrada',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: controller.toggleRelevantFilter,
                  icon: const Icon(Icons.visibility),
                  label: const Text('Mostrar todas as vendas'),
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Estatísticas para administradores
            if (controller.isUserAdmin)
              _buildAdminStatsCard(context, currencyFormat),

            // Indicador de filtro ativo
            if (controller.showOnlyRelevant.value)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 6.0, horizontal: 12.0),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.filter_list,
                              size: 16,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Mostrando apenas vendas relevantes (≥ 20 vendas)',
                              style: TextStyle(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurfaceVariant,
                                fontSize: 13,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            // Lista de vendas
            Expanded(
              child: RefreshIndicator(
                onRefresh: controller.refreshOrders,
                child: ListView.builder(
                  padding: const EdgeInsets.all(8.0),
                  itemCount: displayDates.length,
                  itemBuilder: (context, index) {
                    final date = displayDates[index];
                    final orders = controller.orders[date]!;
                    final totalOrders = orders.length;
                    final isRelevant = totalOrders >= 20;

                    // Calcular o valor total apenas se o usuário for admin
                    final totalValue = controller.isUserAdmin
                        ? orders.fold<double>(
                            0, (prev, order) => prev + order.calculateTotal())
                        : 0.0;

                    return isRelevant
                        ? _buildDetailedCard(context, date, totalOrders,
                            totalValue, currencyFormat)
                        : _buildCompactCard(context, date, totalOrders,
                            totalValue, currencyFormat);
                  },
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  // Card de estatísticas para administradores
  Widget _buildAdminStatsCard(
      BuildContext context, NumberFormat currencyFormat) {
    final totalOrders = controller.getTotalOrdersCount();
    //final totalValue = controller.getTotalSalesValue();
    final unpaidValue = controller.getTotalUnpaidSalesValue();

    return Card(
      margin: const EdgeInsets.all(8.0),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.admin_panel_settings,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Resumo da rota',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const Divider(),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildStatItem(
                  context,
                  Icons.shopping_cart,
                  'Total de vendas',
                  '$totalOrders',
                  Colors.blue,
                ),
                // _buildStatItem(
                //   context,
                //   Icons.attach_money,
                //   'Valor total',
                //   currencyFormat.format(totalValue),
                //   Colors.green,
                // ),
                _buildStatItem(
                  context,
                  Icons.money_off,
                  'Não pago',
                  currencyFormat.format(unpaidValue),
                  Colors.red,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, IconData icon, String label,
      String value, Color color) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Card detalhado para dias com 20 ou mais vendas
  Widget _buildDetailedCard(BuildContext context, DateTime date,
      int totalOrders, double totalValue, NumberFormat currencyFormat) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 2.0),
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => controller.goToSaleDayDetails(date),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Ícone de calendário com o dia
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      DateFormat('dd').format(date),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      DateFormat('MMM', 'pt_BR').format(date).toUpperCase(),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),

              // Informações da data
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      DateTimeHelper.getFormattedDate(date),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          child: Text(
                            '$totalOrders vendas',
                            style: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .primaryContainer,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Valor total se o usuário for admin
              if (controller.isUserAdmin)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      currencyFormat.format(totalValue),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.arrow_forward,
                          size: 14,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Ver detalhes',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Card compacto para dias com menos de 20 vendas
  Widget _buildCompactCard(BuildContext context, DateTime date, int totalOrders,
      double totalValue, NumberFormat currencyFormat) {
    final dayFormat = DateFormat('dd');
    final monthFormat = DateFormat('MMM', 'pt_BR');

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 2.0),
      elevation: 2,
      child: InkWell(
        onTap: () => controller.goToSaleDayDetails(date),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          child: Row(
            children: [
              // Data compacta
              Text(
                '${dayFormat.format(date)} ${monthFormat.format(date)}',
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(width: 8),

              // Número de vendas
              Text(
                '($totalOrders)',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 13,
                ),
              ),

              const Spacer(),

              // Valor total se o usuário for admin
              if (controller.isUserAdmin)
                Text(
                  currencyFormat.format(totalValue),
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),

              const SizedBox(width: 8),

              // Ícone de acesso
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
                size: 18,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
