import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './sale_route_details_controller.dart';

class SaleRouteDetailsPage extends GetView<SaleRouteDetailsController> {
  const SaleRouteDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Detalhes da Rota'),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => controller.deleteSaleRoute(),
            icon: const Icon(Icons.delete),
            tooltip: 'Excluir Rota',
          ),
          IconButton(
            onPressed: () => controller.goToFormSaleRouteEdit(),
            icon: const Icon(Icons.edit),
            tooltip: 'Editar Rota',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Obx(
          () {
            final route = controller.saleRoute.value;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildInfoRow('Nome', route.name),
                        const Divider(),
                        _buildInfoRow(
                            'Dia da Venda', route.salesDay.toString()),
                        const Divider(),
                        _buildInfoRow(
                          'Última Modificação',
                          _formatTimestamp(route.lastModified),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => controller.goToSalesFromRoute(),
                    icon: const Icon(Icons.shopping_cart),
                    label: const Text('Ver Vendas'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      backgroundColor: Get.theme.colorScheme.secondary,
                      foregroundColor: Get.theme.colorScheme.onSecondary,
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            flex: 5,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatTimestamp(Timestamp timestamp) {
    final date = timestamp.toDate();
    return '${date.day}/${date.month}/${date.year} às ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
