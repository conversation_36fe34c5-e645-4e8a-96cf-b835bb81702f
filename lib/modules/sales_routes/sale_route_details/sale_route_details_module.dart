import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/sales_routes/sale_route_details/sale_route_details_bindings.dart';
import 'package:fl_app/modules/sales_routes/sale_route_details/sale_route_details_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SaleRouteDetailsModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/sale-route-details',
      page: () => const SaleRouteDetailsPage(),
      binding: SaleRouteDetailsBindings(),
    ),
  ];
}
