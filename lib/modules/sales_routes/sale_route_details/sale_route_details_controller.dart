import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SaleRouteDetailsController extends GetxController {
  final SalesRoutesService _salesRoutesService;

  SaleRouteDetailsController({required SalesRoutesService salesRoutesService})
      : _salesRoutesService = salesRoutesService;

  Rx<SaleRouteModel> saleRoute = (Get.arguments as SaleRouteModel).obs;

  Future<void> deleteSaleRoute() async {
    final confirm = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Excluir Rota de Venda'),
        content: const Text('Você tem certeza que deseja excluir esta rota?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text(
              'Excluir',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    if (confirm ?? false) {
      try {
        await _salesRoutesService.deleteSaleRoute(saleRoute.value);
        Get.back(result: saleRoute.value);
        Get.snackbar(
          'Sucesso',
          'Rota de venda excluída com sucesso.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.withOpacity(0.7),
          colorText: Colors.white,
        );
      } catch (e) {
        Get.snackbar(
          'Erro',
          'Falha ao excluir a rota de venda.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.7),
          colorText: Colors.white,
        );
      }
    }
  }

  goToFormSaleRouteEdit() {
    Get.toNamed('/add_sale_route', arguments: saleRoute.value)?.then((value) {
      if (value != null) {
        saleRoute.value = value as SaleRouteModel;
        saleRoute.refresh();
      }
    });
  }

  goToSalesFromRoute() {
    Get.toNamed('/sales_from_route', arguments: saleRoute.value);
  }
}
