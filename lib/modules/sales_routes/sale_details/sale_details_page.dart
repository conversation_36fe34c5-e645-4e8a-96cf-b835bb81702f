import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/order_list_card.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'sale_details_controller.dart';

class SaleDetailsPage extends GetView<SaleDetailsController> {
  const SaleDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Dia de Venda',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: ElevatedButton(
              onPressed: () async {
                final result = await Get.dialog<bool>(
                  AlertDialog(
                    title: const Text('Finalizar venda'),
                    content: const Text('Deseja realmente finalizar a venda?'),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Get.back(result: true);
                        },
                        child: const Text('Sim'),
                      ),
                      TextButton(
                        onPressed: () {
                          Get.back(result: false);
                        },
                        child: const Text('Não'),
                      ),
                    ],
                  ),
                );
                if (result == true) {
                  controller.finishSale();
                  Get.back();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('Finalizar'),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.done,
                    color: theme.colorScheme.onPrimary,
                  ),
                ],
              ),
            ),
          ),
        ],
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Informações da Rota e Data
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      controller.saleRoute?.name ?? 'Rota não encontrada',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.textTheme.bodyLarge?.color,
                      ),
                    ),
                  ),
                  Text(
                    DateTimeHelper.getFormattedDate(controller.saleDay.key),
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.textTheme.bodyLarge?.color,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Divider(
                color: theme.dividerColor,
                thickness: 2,
              ),
              const SizedBox(height: 8),

              // Informações de Total, P.V e A.V para Admin
              if (controller.user.value != null && controller.user.value!.admin)
                Column(
                  children: [
                    _buildInfoRow(
                      context,
                      'Total',
                      'R\$ ${controller.ordersUpdated.fold<double>(0, (previousValue, element) => previousValue + element.calculateTotal()).toStringAsFixed(2)}',
                    ),
                    const SizedBox(height: 4),
                  ],
                ),

              _buildInfoRow(
                context,
                'P.V',
                '${controller.ordersUpdated.fold<int>(0, (previousValue, element) => previousValue + element.products.fold<int>(0, (previousValue, element) => element.temComissao && (element.customValue == 0) ? previousValue + (element.quantity) : previousValue))}',
              ),
              const SizedBox(height: 4),
              _buildInfoRow(
                context,
                'A.V',
                '${controller.ordersUpdated.fold<int>(0, (previousValue, element) => previousValue + element.products.fold<int>(0, (previousValue, element) => element.temComissao && (element.customValue != 0) ? previousValue + (element.quantity) : previousValue))}',
              ),
              const SizedBox(height: 16),

              // Tabs
              SizedBox(
                width: double.infinity,
                child: TabBar(
                  controller: controller.tabController,
                  tabs: controller.myTabs,
                  labelColor: theme.colorScheme.primary,
                  unselectedLabelColor: theme.textTheme.bodyLarge?.color,
                  indicatorColor: theme.colorScheme.primary,
                  onTap: (value) => controller.changeTab(value),
                ),
              ),
              const SizedBox(height: 8),

              // Conteúdo das Tabs
              if (controller.isLoading.value)
                const Expanded(
                  child: Center(
                    child: CircularProgressIndicator(),
                  ),
                )
              else
                Expanded(
                  child: TabBarView(
                    controller: controller.tabController,
                    children: [
                      // Tab 1: Pedidos
                      Obx(
                        () => RefreshIndicator(
                          onRefresh: () => controller.refreshSaleOrders(),
                          child: ListView.builder(
                            itemCount: controller.ordersUpdated.length,
                            itemBuilder: (context, index) {
                              final order = controller.ordersUpdated[index];
                              return OrderListCard(
                                order: order,
                                onTap: (order) async {
                                  await Get.toNamed('/view-order',
                                      arguments: order);
                                  controller.refreshSaleOrders();
                                },
                              );
                            },
                          ),
                        ),
                      ),
                      // Tab 2: Vendedores
                      Obx(
                        () => RefreshIndicator(
                          onRefresh: () => controller.refreshSaleOrders(),
                          child: ListView.separated(
                            separatorBuilder: (context, index) => Divider(
                              color: theme.dividerColor,
                            ),
                            itemCount: controller.sallerSales.length,
                            itemBuilder: (context, index) {
                              final seller =
                                  controller.sallerSales.keys.elementAt(index);
                              final sales = controller.sallerSales[seller];
                              return ListTile(
                                title: Text(
                                  seller,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                    color: theme.textTheme.bodyLarge?.color,
                                  ),
                                ),
                                trailing: Text(
                                  sales.toString(),
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      // Tab 3: Produtos
                      // Tab 3: Produtos
                      Obx(
                        () => RefreshIndicator(
                          onRefresh: () => controller.refreshSaleOrders(),
                          child: SingleChildScrollView(
                            physics: const AlwaysScrollableScrollPhysics(),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8.0,
                              ),
                              child: Column(
                                children: [
                                  // Row com toggles de "Comissão" e "Variações"
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Obx(
                                        () => ChoiceChip(
                                          label: const Text('Comissão'),
                                          selected: controller
                                              .showOnlyCommission.value,
                                          onSelected: (bool selected) {
                                            controller.showOnlyCommission
                                                .value = selected;
                                          },
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Obx(
                                        () => ChoiceChip(
                                          label: const Text('Variações'),
                                          selected:
                                              controller.showVariations.value,
                                          onSelected: (bool selected) {
                                            controller.showVariations.value =
                                                selected;
                                          },
                                        ),
                                      ),
                                    ],
                                  ),

                                  Table(
                                    columnWidths: const {
                                      0: FlexColumnWidth(3),
                                      1: FlexColumnWidth(1),
                                      2: FlexColumnWidth(1),
                                    },
                                    border: TableBorder.symmetric(
                                      outside: BorderSide(
                                          color: Colors.grey.shade300),
                                    ),
                                    defaultVerticalAlignment:
                                        TableCellVerticalAlignment.middle,
                                    children: [
                                      // Cabeçalho
                                      TableRow(
                                        decoration: BoxDecoration(
                                          color: Theme.of(context)
                                              .colorScheme
                                              .primary
                                              .withOpacity(0.1),
                                        ),
                                        children: const [
                                          Padding(
                                            padding: EdgeInsets.all(8.0),
                                            child: Text(
                                              'Produto',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                          Padding(
                                            padding: EdgeInsets.all(8.0),
                                            child: Text(
                                              'P.V',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                          Padding(
                                            padding: EdgeInsets.all(8.0),
                                            child: Text(
                                              'A.V',
                                              textAlign: TextAlign.center,
                                              style: TextStyle(
                                                  fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                        ],
                                      ),

                                      // Linhas de dados
                                      for (var product
                                          in controller.productsSold.where((p) {
                                        // Se o toggle "Comissão" estiver ativo, filtra
                                        if (controller
                                            .showOnlyCommission.value) {
                                          return p['temComissao'] == true;
                                        }
                                        return true;
                                      })) ...[
                                        // 1) Linha do produto pai
                                        TableRow(
                                          decoration: BoxDecoration(
                                            color:
                                                product['temComissao'] == true
                                                    ? Theme.of(context)
                                                        .colorScheme
                                                        .primary
                                                        .withOpacity(0.05)
                                                    : Colors.transparent,
                                            border: Border(
                                              bottom: BorderSide(
                                                  color: Colors.grey.shade300),
                                            ),
                                          ),
                                          children: [
                                            Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Text(
                                                product['productBaseName'],
                                                textAlign: TextAlign.start,
                                                style: TextStyle(
                                                  fontWeight:
                                                      product['temComissao'] ==
                                                              true
                                                          ? FontWeight.w600
                                                          : FontWeight.normal,
                                                  color:
                                                      product['temComissao'] ==
                                                              true
                                                          ? (Get.isDarkMode
                                                              ? Colors.blue
                                                              : Colors
                                                                  .blue[800])
                                                          : (Get.isDarkMode
                                                              ? Colors.white
                                                              : Colors.black),
                                                ),
                                              ),
                                            ),
                                            Center(
                                              child: Text(
                                                product['quantity_prazo']
                                                    .toString(),
                                                style: const TextStyle(
                                                    fontSize: 14),
                                              ),
                                            ),
                                            Center(
                                              child: Text(
                                                product['quantity_avista']
                                                    .toString(),
                                                style: const TextStyle(
                                                    fontSize: 14),
                                              ),
                                            ),
                                          ],
                                        ),

                                        // 2) Se tiver variações e showVariations = true, mostra
                                        if ((product['variations'] as List)
                                                .isNotEmpty &&
                                            controller.showVariations.value)
                                          for (var variation
                                              in product['variations'])
                                            TableRow(
                                              decoration: BoxDecoration(
                                                // cor de fundo opcional para variação
                                                color: Theme.of(context)
                                                            .brightness ==
                                                        Brightness.dark
                                                    ? Colors.grey[850]
                                                    : Colors.grey[100],
                                                border: Border(
                                                  bottom: BorderSide(
                                                      color:
                                                          Colors.grey.shade300),
                                                ),
                                              ),
                                              children: [
                                                // Nome da variação (com indentação/bullet)
                                                Padding(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 16.0,
                                                    vertical: 8.0,
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      // bullet
                                                      const Text(
                                                          '   \u{2022}  '),
                                                      Expanded(
                                                        child: Text(
                                                          variation[
                                                              'variationName'],
                                                          style:
                                                              const TextStyle(
                                                                  fontSize: 14),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),

                                                // P.V
                                                Center(
                                                  child: Text(
                                                    variation['quantity_prazo']
                                                        .toString(),
                                                    style: const TextStyle(
                                                        fontSize: 14),
                                                  ),
                                                ),

                                                // A.V
                                                Center(
                                                  child: Text(
                                                    variation['quantity_avista']
                                                        .toString(),
                                                    style: const TextStyle(
                                                        fontSize: 14),
                                                  ),
                                                ),
                                              ],
                                            ),
                                      ],
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Função para construir linhas de informação
  Widget _buildInfoRow(BuildContext context, String label, String value) {
    final theme = Theme.of(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.titleMedium?.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.textTheme.bodyLarge?.color,
          ),
        ),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  // Função para construir a tabela de produtos
  Widget _buildProductTable(
    BuildContext context,
    List<Map<String, dynamic>> products,
  ) {
    return Obx(
      () => RefreshIndicator(
        onRefresh: () => controller.refreshSaleOrders(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                // Se quiser o toggle de "Comissão"
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Obx(
                      () => ChoiceChip(
                        label: const Text('Comissão'),
                        selected: controller.showOnlyCommission.value,
                        onSelected: (bool selected) {
                          controller.showOnlyCommission.value = selected;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Título da Tabela
                Text(
                  'Produtos',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),

                // --- Tabela com Table ---
                Table(
                  // Define larguras de colunas (3 colunas)
                  columnWidths: const {
                    0: FlexColumnWidth(3),
                    1: FlexColumnWidth(1),
                    2: FlexColumnWidth(1),
                  },
                  border: TableBorder.symmetric(
                    outside: BorderSide(color: Colors.grey.shade300),
                  ),
                  children: [
                    // Cabeçalho
                    TableRow(
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.1),
                      ),
                      children: const [
                        Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Text(
                            'Produto',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Text(
                            'P.V',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Text(
                            'A.V',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),

                    // Linhas de dados
                    for (var product in controller.productsSold.where((p) {
                      if (controller.showOnlyCommission.value) {
                        return p['temComissao'] == true;
                      }
                      return true;
                    }))
                      TableRow(
                        decoration: BoxDecoration(
                          color: product['temComissao'] == true
                              ? Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withOpacity(0.05)
                              : Colors.transparent,
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              product['productBaseName'],
                              textAlign: TextAlign.start,
                              style: TextStyle(
                                fontWeight: product['temComissao'] == true
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                          Center(
                            child: Text(
                              product['quantity_prazo'].toString(),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                          Center(
                            child: Text(
                              product['quantity_avista'].toString(),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
