import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:get/get.dart';

class AddSaleRouteController extends GetxController {
  final SalesRoutesService _salesRoutesService;

  AddSaleRouteController({required SalesRoutesService salesRoutesService})
      : _salesRoutesService = salesRoutesService;

  bool get isEdit => Get.arguments != null;

  @override
  void onInit() {
    if (Get.arguments != null) {
      final SaleRouteModel saleRouteModel = Get.arguments as SaleRouteModel;
      name.value = saleRouteModel.name;
      salesDay.value = saleRouteModel.salesDay;
    }
    super.onInit();
  }

  final Rxn<String> name = Rxn<String>();
  final Rxn<int> salesDay = Rxn<int>();

  Future<void> save() async {
    if (isEdit) {
      if (name.value != null && salesDay.value != null) {
        var newSaleRoute = SaleRouteModel(
          id: Get.arguments.id,
          name: name.value!,
          salesDay: salesDay.value!,
          lastModified: Timestamp.fromDate(
              DateTime.now().add(const Duration(minutes: 1))),
        );
        await _salesRoutesService.updateSaleRoute(newSaleRoute);
        Get.back(result: newSaleRoute);
      }
    } else {
      if (name.value != null && salesDay.value != null) {
        await _salesRoutesService.addSaleRoute(
          SaleRouteModel(
            name: name.value!,
            salesDay: salesDay.value!,
            lastModified: Timestamp.fromDate(
                DateTime.now().add(const Duration(minutes: 1))),
          ),
        );
        Get.back();
      }
    }
  }
}
