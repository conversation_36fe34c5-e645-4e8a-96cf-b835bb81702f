import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/sales_routes/add_sale_route/add_sale_route_bindings.dart';
import 'package:fl_app/modules/sales_routes/add_sale_route/add_sale_route_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class AddSaleRouteModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/add_sale_route',
      page: () => AddSaleRoutePage(),
      binding: AddSaleRouteBindings(),
    ),
  ];
}
