import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './add_sale_route_controller.dart';

class AddSaleRoutePage extends GetView<AddSaleRouteController> {
  AddSaleRoutePage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(controller.isEdit ? 'Editar Rota' : 'Nova Rota'),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              //Name
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Nome',
                ),
                initialValue: controller.name.value,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                validator: (String? value) {
                  if (value == null || value.isEmpty) {
                    return 'Nome é obrigatório';
                  }
                  return null;
                },
                onSaved: (String? value) {
                  controller.name(value);
                },
              ),
              const SizedBox(height: 16),
              //SalesDay -> DropdownButton with days 1 to 31
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'Dia da Venda',
                ),
                value: controller.salesDay.value,
                items: List.generate(
                  31,
                  (index) => DropdownMenuItem(
                    value: index + 1,
                    child: Text('${index + 1}'),
                  ),
                ),
                onChanged: (int? value) {
                  controller.salesDay(value);
                },
                autovalidateMode: AutovalidateMode.onUserInteraction,
                validator: (int? value) {
                  if (value == null) {
                    return 'Dia da Venda é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              //Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      _formKey.currentState!.save();
                      controller.save();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    backgroundColor: Get.theme.colorScheme.secondary,
                    foregroundColor: Get.theme.colorScheme.onSecondary,
                  ),
                  child: Text(controller.isEdit ? 'Salvar' : 'Adicionar'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
