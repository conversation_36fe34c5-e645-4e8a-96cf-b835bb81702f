import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SalesRoutesController extends GetxController {
  final SalesRoutesService _salesRoutesService;
  final AppStateService _appStateService;

  SalesRoutesController(
      {required SalesRoutesService salesRoutesService,
      required AppStateService appStateService})
      : _salesRoutesService = salesRoutesService,
        _appStateService = appStateService;

  RxList<SaleRouteModel> salesRoutes = <SaleRouteModel>[].obs;

  final isLoading = false.obs;

  Rx<AppState> appState = AppState().obs;

  final searchController = TextEditingController();
  RxList<SaleRouteModel> filteredSalesRoutes = <SaleRouteModel>[].obs;

  void onSearchChanged(String query) {
    if (query.isEmpty) {
      filteredSalesRoutes.assignAll(salesRoutes);
    } else {
      filteredSalesRoutes.assignAll(
        salesRoutes.where(
            (route) => route.name.toLowerCase().contains(query.toLowerCase())),
      );
    }
  }

  @override
  void onReady() {
    super.onReady();
    _salesRoutesService.getSalesRoutesFromCache().then((routes) {
      salesRoutes = routes;
      salesRoutes.refresh();
    });
    getSalesRoutes();
    appState(_appStateService.getAppState());
  }

  Future<void> getSalesRoutes() async {
    isLoading(true);
    _salesRoutesService.getSalesRoutes().then((value) {
      salesRoutes = value;
      salesRoutes.refresh();
    });
    isLoading(false);
  }

  Future<void> vender(String id) async {
    await _appStateService.startSaleInRoute(id);
    appState(_appStateService.getAppState());
  }

  Future<void> finalizarVenda() async {
    await _appStateService.finishSaleInRoute();
    appState(_appStateService.getAppState());
  }

  void goToSaleRouteDetails(SaleRouteModel item) async {
    await Get.toNamed('/sale-route-details', arguments: item);
    getSalesRoutes();
  }

  void goToEditSaleRoute(SaleRouteModel item) async {
    await Get.toNamed('/add_sale_route', arguments: item);
    getSalesRoutes();
  }

  void goToCreateSaleRoute() async {
    await Get.toNamed('/add_sale_route');
    getSalesRoutes();
  }

  goToSalesFromRoute(SaleRouteModel saleRoute) {
    Get.toNamed('/sales_from_route', arguments: saleRoute);
  }
}
