import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './sales_routes_controller.dart';

class SalesRoutesPage extends GetView<SalesRoutesController> {
  const SalesRoutesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Rotas de Venda'),
        actions: [
          ElevatedButton.icon(
            onPressed: () {
              controller.goToCreateSaleRoute();
            },
            icon: const Icon(Icons.add),
            label: const Text('Nova Rota'),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(
              strokeCap: StrokeCap.round,
            ),
          );
        } else if (controller.salesRoutes.isEmpty) {
          return const Center(
            child: Text('Nenhuma rota cadastrada'),
          );
        }
        return RefreshIndicator(
          onRefresh: () async {
            await controller.getSalesRoutes();
          },
          child: ListView.builder(
              itemBuilder: (context, index) {
                final item = controller.salesRoutes[index];
                return Card(
                  child: ListTile(
                    leading: Icon(Icons.route,
                        color: Theme.of(context).colorScheme.primary),
                    title: Text(item.name),
                    subtitle: Text('Dia da Venda: ${item.salesDay}'),
                    trailing: FilledButton(
                      onPressed: () {
                        controller.goToSalesFromRoute(item);
                      },
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all(
                            Theme.of(context).colorScheme.primary),
                      ),
                      child: const Text('Vendas'),
                    ),
                    onTap: () {
                      controller.goToSaleRouteDetails(item);
                    },
                  ),
                );
              },
              itemCount: controller.salesRoutes.length),
        );
      }),
    );
  }
}
