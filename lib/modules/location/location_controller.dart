import 'dart:async';
import 'dart:developer';

import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationController extends GetxController {
  final GeolocationService _geolocationService;

  LocationController({required GeolocationService geolocationService})
      : _geolocationService = geolocationService;

  late StreamSubscription<Position?> _stream;
  Rxn<Position?> currentPosition = Rxn<Position?>();

  CameraPosition? cameraPosition;
  Set<Marker> markers = {};

  final Completer<GoogleMapController> _mapController =
      Completer<GoogleMapController>();

  String? _darkMapStyle;

  @override
  void onInit() {
    _loadMapStyles();
    init();
    super.onInit();
  }

  Future _loadMapStyles() async {
    _darkMapStyle = await rootBundle.loadString('assets/json/map_dark.json');
  }

  init() async {
    final stream = await _geolocationService.getCurrentLocationStream();
    currentPosition.value = await _geolocationService.getCurrentLocation();
    _stream = stream.listen((position) {
      log('Localização atualizada: $position');
      currentPosition.value = position;

      cameraPosition = CameraPosition(
        target: LatLng(position.latitude, position.longitude),
        zoom: 19,
      );
      markers.clear();
      markers.add(Marker(
        markerId: const MarkerId('user'),
        position: LatLng(position.latitude, position.longitude),
      ));

      if (_mapController.isCompleted) {
        _mapController.future.then((controller) {
          controller
              .animateCamera(CameraUpdate.newCameraPosition(cameraPosition!));
        });
      }
    });
  }

  @override
  void onClose() {
    _stream.cancel();
    super.onClose();
  }

  void onMapCreated(GoogleMapController controller) {
    if (!_mapController.isCompleted) {
      _mapController.complete(controller);
    }
    if (_darkMapStyle != null && Get.isDarkMode) {
      controller.setMapStyle(_darkMapStyle!);
    }
  }

  void goToUserLocation(GoogleMapController controller) {
    controller.animateCamera(CameraUpdate.newCameraPosition(cameraPosition!));
  }
}
