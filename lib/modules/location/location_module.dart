import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/location/location_bindings.dart';
import 'package:fl_app/modules/location/location_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class LocationModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/location',
      page: () => const LocationPage(),
      binding: LocationBindings(),
    ),
  ];
}
