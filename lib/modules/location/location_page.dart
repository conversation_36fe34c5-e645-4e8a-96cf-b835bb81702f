import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './location_controller.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationPage extends GetView<LocationController> {
  const LocationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('GPS')),
      body: Center(
        child: Obx(
          () => Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Expanded(
                child: Container(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: GoogleMap(
                      initialCameraPosition: controller.cameraPosition ??
                          const CameraPosition(
                            target: LatLng(-9.112157, -37.121476),
                            zoom: 19,
                          ),
                      onMapCreated: controller.onMapCreated,
                      myLocationEnabled: true,
                      myLocationButtonEnabled: true,
                      zoomControlsEnabled: false,
                      markers: controller.markers,
                    ),
                  ),
                ),
              ),
              Text('Latitude: ${controller.currentPosition.value?.latitude}'),
              Text('Longitude: ${controller.currentPosition.value?.longitude}'),
            ],
          ),
        ),
      ),
    );
  }
}
