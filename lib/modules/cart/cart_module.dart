import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/cart/add_products_to_cart/add_products_to_cart_module.dart';
import 'package:fl_app/modules/cart/cart_bindings.dart';
import 'package:fl_app/modules/cart/cart_page.dart';
import 'package:fl_app/modules/cart/search_clients_to_cart/search_clients_to_cart_module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class CartModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/cart',
      page: () => const CartPage(),
      binding: CartBindings(),
    ),
    ...AddProductsToCardModule().routers,
    ...SearchClientsToCartModule().routers,
  ];
}
