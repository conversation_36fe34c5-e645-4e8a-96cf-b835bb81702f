import 'dart:async';
import 'dart:developer';

import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/order_product_model.dart';
import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';
import 'package:fl_app/services/cart/cart_service.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class CartController extends GetxController {
  final ClientService _clientService;
  final GeolocationService _geoLocatorService;
  final AppStateService _appStateService;
  final OrderRepository _orderRepository;
  final CartService _cartService;
  final ProductRepository _productRepository;
  final UserService _userService;

  Rxn<OrderModel?> orderEditing = Rxn<OrderModel>();

  CartController({
    required ClientService clientService,
    required GeolocationService geolocationService,
    required AppStateService appStateService,
    required OrderRepository orderRepository,
    required CartService cartService,
    required ProductRepository productRepository,
    required UserService userService,
  })  : _clientService = clientService,
        _geoLocatorService = geolocationService,
        _orderRepository = orderRepository,
        _cartService = cartService,
        _productRepository = productRepository,
        _userService = userService,
        _appStateService = appStateService {
    appState = _appStateService.getAppState().obs;
  }

  final isLoadingClientsNearbyFirstTime = Rx<bool>(true);

  final currentPosition = Rx<Position?>(null);
  late Stream<Position> _currentPositionStream;

  late Timer _timer;

  RxBool showClientsOnlyInRoute = false.obs;
  RxBool showMyClientsOnlyInRoute = false.obs;

  Rx<AppState> appState = AppState().obs;

  RxBool isSaving = false.obs;

  UserModel? user;
  @override
  void onInit() async {
    super.onInit();
    Map<String, dynamic> arguments = Get.arguments;
    _currentPositionStream =
        await _geoLocatorService.getCurrentLocationStream();
    currentPosition.value = await _geoLocatorService.getCurrentLocation();
    _currentPositionStream.listen((position) {
      currentPosition.value = position;
    });
    _timer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      await refreshClientsNearby();
    });
    Future.delayed(Duration.zero, () async {
      await refreshClientsNearby();
      isLoadingClientsNearbyFirstTime(false);
    });
    appState(_appStateService.getAppState());

    orderEditing.value = arguments['order'] as OrderModel?;
    if (orderEditing.value != null) {
      shoppingCart.value = await ShoppingCart.fromOrder(
        orderEditing.value!,
        _clientService,
        _productRepository,
      );
      shoppingCart.refresh();
    }

    if (arguments['client'] != null) {
      shoppingCart.value.client(arguments['client'] as ClientModel);
      saveShoppingCart();
    }
    if (shoppingCart.value.client.value != null) {
      loadClientPendingOrders();
    }

    user = await _userService.getUserAuthenticated();

    if (appState.value.isSelling) {
      showClientsOnlyInRoute(true);
    } else {
      showClientsOnlyInRoute(false);
    }

    if (user!.cobrador) {
      showMyClientsOnlyInRoute(true);
    }
  }

  @override
  void onClose() {
    super.onClose();
    _timer.cancel();
  }

  Rx<ShoppingCart> shoppingCart = Rx<ShoppingCart>(Get.arguments['cart']);
  RxList<ClientModel> clientsNearby = RxList<ClientModel>([]);

  Future<void> refreshClientsNearby() async {
    log('atualizando clientes próximos');
    try {
      bool isVendendo = appState.value.isSelling;
      bool isCobrador = user!.cobrador;
      final result = await _clientService.getClientsNearby(
        currentPosition.value,
        isVendendo && showClientsOnlyInRoute.value
            ? [appState.value.isSellingRouteId!]
            : isCobrador && showMyClientsOnlyInRoute.value
                ? user!.authorizedBillingRoutes
                : [],
      );
      if (result == clientsNearby) return;
      clientsNearby.assignAll(result);
    } catch (e, s) {
      log('Erro ao buscar clientes próximos', error: e, stackTrace: s);
      Get.showSnackbar(GetSnackBar(
        title: 'Erro ao buscar clientes próximos',
        message: '$e\n$s',
        duration: const Duration(seconds: 5),
      ));
    }
  }

  Future<void> goToSelectProduct() async {
    await Get.toNamed('/add-products-to-card', arguments: shoppingCart.value);
    saveShoppingCart();
  }

  void removeItem(CartItem item) {
    if (item.selectedVariation != null) {
      shoppingCart.value.removeVariation(item.selectedVariation!);
    } else {
      shoppingCart.value.removeProduct(item.product);
    }
  }

  void incrementItem(CartItem item) {
    if (item.selectedVariation != null) {
      shoppingCart.value.incrementVariation(item.selectedVariation!);
    } else {
      shoppingCart.value.incrementProduct(item.product);
    }
    saveShoppingCart();
  }

  void decrementItem(CartItem item) {
    if (item.selectedVariation != null) {
      shoppingCart.value.decrementVariation(item.selectedVariation!);
    } else {
      shoppingCart.value.decrementProduct(item.product);
    }
  }

  Future<void> goToAddClient() async {
    final result = await Get.toNamed('/form_client');
    if (result != null) {
      final client = result as ClientModel;
      shoppingCart.value.client(client);
      saveShoppingCart();
    }
  }

  Future<void> goToEditClient() async {
    final result = await Get.toNamed('/form_client',
        arguments: shoppingCart.value.client.value, preventDuplicates: false);
    if (result != null) {
      final client = result as ClientModel;
      shoppingCart.value.client(client);
      saveShoppingCart();
    }
  }

  Future<void> goToClientDetails() async {
    await Get.toNamed('/client_details',
        arguments: shoppingCart.value.client.value);
    final client =
        await _clientService.getClient(shoppingCart.value.client.value!.id!);

    refreshClientsNearby();
    if (client != null) {
      shoppingCart.value.client(client);
      saveShoppingCart();
    } else {
      shoppingCart.value.client.value = null;
      saveShoppingCart();
    }
  }

  Future<void> goToSearchClientsToCart(List<String> rotas) async {
    final result =
        await Get.toNamed('/search_clients_to_cart', arguments: rotas);

    if (result != null) {
      shoppingCart.value.client(result);
      loadClientPendingOrders();
      saveShoppingCart();
    }
  }

  setRestante(String value) {
    shoppingCart.value.restante(double.tryParse(value) ?? 0);
    saveShoppingCart();
  }

  bool get isEditing {
    return orderEditing.value != null;
  }

  Future<OrderModel?> finalizarVenda() async {
    if (isSaving.value) return null;
    isSaving(true);
    if (shoppingCart.value.client.value == null) {
      Get.snackbar('Erro', 'Selecione um cliente para finalizar a venda');
      return null;
    }

    List<OrderProductModel> products = [];
    for (var item in shoppingCart.value.items) {
      var name = item.product.nome;
      if (item.selectedVariation != null) {
        name += ' - ${item.selectedVariation!.name}';
      }
      products.add(OrderProductModel(
        productId: item.product.id!,
        productBaseName: item.product.nome,
        variationId: item.selectedVariation?.id,
        name: name,
        price: item.selectedVariation != null
            ? item.selectedVariation!.price.toDouble()
            : item.product.valor.toDouble(),
        quantity: item.quantity,
        quantityToDelivery: item.quantityToDelivery,
        deliveryNote: item.deliveryNote,
        temComissao: item.product.temComissao,
        total: item.customValue > 0
            ? item.customValue.toDouble() * item.quantity
            : item.selectedVariation != null
                ? item.selectedVariation!.price.toDouble() * item.quantity
                : item.product.valor.toDouble() * item.quantity,
        customValue: item.customValue.toDouble(),
      ));
    }

    String? routeSaleId;
    if (appState.value.isSelling && orderEditing.value == null) {
      routeSaleId = appState.value.isSellingRouteId;
    }

    if (isEditing) {
      OrderModel newOrder = OrderModel(
        id: orderEditing.value!.id,
        date: orderEditing.value!.date,
        products: products,
        remaining: shoppingCart.value.restante.value,
        payments: orderEditing.value!.payments,
        isPaid: orderEditing.value!.isPaid,
        clientId: shoppingCart.value.client.value!.id!,
        clientName: shoppingCart.value.client.value!.name.trim(),
        clientAddress: shoppingCart.value.client.value!.address.trim(),
        clientLocalDescription:
            shoppingCart.value.client.value!.localDescription.trim(),
        clientLatitude: shoppingCart.value.client.value!.latitude,
        clientLongitude: shoppingCart.value.client.value!.longitude,
        clientNumber: shoppingCart.value.client.value!.number,
        clientPhone: shoppingCart.value.client.value!.phoneNumber,
        routeId: shoppingCart.value.client.value!.routeId,
        routeName: Get.find<SalesRoutesRepository>()
            .getSaleRouteName(shoppingCart.value.client.value!.routeId),
        userId: orderEditing.value!.userId,
        sellerName: orderEditing.value!.sellerName,
        toDelivery: shoppingCart.value.toDelivery.value,
        lastModified: DateTime.now(),
        dayMarkingItems: orderEditing.value!.dayMarkingItems,
        routeSaleId: orderEditing.value!.routeSaleId,
      );

      try {
        var order = await _orderRepository.updateOrder(newOrder);
        isSaving(false);
        return order;
      } catch (e) {
        Get.snackbar('Erro', '$e');
        isSaving(false);
        return null;
      }
    } else {
      final userAuth = await _userService.getUserAuthenticated();
      OrderModel newOrder = OrderModel(
        date: DateTime.now(),
        products: products,
        remaining: shoppingCart.value.restante.value,
        payments: [],
        isPaid: false,
        clientId: shoppingCart.value.client.value!.id!,
        clientName: shoppingCart.value.client.value!.name,
        clientAddress: shoppingCart.value.client.value!.address.trim(),
        clientLocalDescription:
            shoppingCart.value.client.value!.localDescription,
        clientLatitude: shoppingCart.value.client.value!.latitude,
        clientLongitude: shoppingCart.value.client.value!.longitude,
        clientNumber: shoppingCart.value.client.value!.number,
        clientPhone: shoppingCart.value.client.value!.phoneNumber,
        routeId: shoppingCart.value.client.value!.routeId,
        routeName: Get.find<SalesRoutesRepository>()
            .getSaleRouteName(shoppingCart.value.client.value!.routeId),
        userId: userAuth!.id,
        sellerName: userAuth.name,
        toDelivery: shoppingCart.value.toDelivery.value,
        lastModified: DateTime.now(),
        dayMarkingItems: [],
        routeSaleId: routeSaleId,
      );

      try {
        var order = await _orderRepository.addOrder(newOrder);
        _cartService.removeShoppingCart(shoppingCart.value);
        isSaving(false);
        return order;
      } catch (e) {
        Get.snackbar('Erro', '$e');
        isSaving(false);
        return null;
      }
    }
  }

  switchIsToDelivery(bool value) {
    shoppingCart.value.toDelivery(value);
    saveShoppingCart();
  }

  void editValue(CartItem item) async {
    num customValue = item.customValue;
    final index = shoppingCart.value.items.indexOf(item);
    FocusNode focusNode = FocusNode();
    Get.defaultDialog(
      title: 'Valor personalizado',
      content: Column(
        children: [
          Text(
              'Valor original: ${NumberFormat.currency(locale: 'pt_BR', symbol: 'R\$').format(item.getOriginalPrice())}'),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextFormField(
              initialValue:
                  item.customValue == 0 ? '' : item.customValue.toString(),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                customValue = num.tryParse(value) ?? 0;
              },
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'Valor personalizado',
                prefixText: 'R\$ ',
              ),
              focusNode: focusNode,
            ),
          ),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                customValue = 0;
                Get.back(result: customValue);
              },
              style: ElevatedButton.styleFrom(
                shadowColor: Theme.of(Get.context!).colorScheme.primary,
              ),
              child: const Text('Limpar'),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: () {
                  Get.back();
                },
                style: ElevatedButton.styleFrom(
                  shadowColor: Theme.of(Get.context!).colorScheme.primary,
                ),
                child: const Text('Cancelar'),
              ),
              ElevatedButton(
                onPressed: () {
                  Get.back();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Salvar'),
              ),
            ],
          ),
        ],
      ),
    ).then((value) {
      if (customValue != item.customValue) {
        shoppingCart.value.items[index] =
            item.copyWith(customValue: customValue);
        saveShoppingCart();
      }
    });
    focusNode.requestFocus();
  }

  void saveShoppingCart() {
    if (orderEditing.value == null) {
      _cartService.saveShoppingCart(shoppingCart.value);
    }
  }

  void addProductToDelivery(CartItem item) async {
    num quantityToDelivery =
        item.quantityToDelivery != 0 ? item.quantityToDelivery : item.quantity;
    String deliveryNote = item.deliveryNote;
    final index = shoppingCart.value.items.indexOf(item);
    FocusNode focusNode = FocusNode();
    final result = await Get.defaultDialog(
      title: 'Produto para entrega',
      content: Column(
        children: [
          Text(
            item.product.nome,
            style: const TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextFormField(
              initialValue: quantityToDelivery.toString(),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                quantityToDelivery = num.tryParse(value) ?? 0;
              },
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
              ],
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'Quantidade para entrega',
              ),
              focusNode: focusNode,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextFormField(
              initialValue: deliveryNote,
              onChanged: (value) {
                deliveryNote = value;
              },
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                labelText: 'Observação para entrega',
              ),
            ),
          ),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Get.back(result: [0, '']);
              },
              style: ElevatedButton.styleFrom(
                shadowColor: Theme.of(Get.context!).colorScheme.primary,
              ),
              child: const Text('Limpar'),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ElevatedButton(
                onPressed: () {
                  Get.back();
                },
                style: ElevatedButton.styleFrom(
                  shadowColor: Theme.of(Get.context!).colorScheme.primary,
                ),
                child: const Text('Cancelar'),
              ),
              ElevatedButton(
                onPressed: () {
                  Get.back(result: [quantityToDelivery, deliveryNote]);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Salvar'),
              ),
            ],
          ),
        ],
      ),
    );
    if (result != null) {
      if (quantityToDelivery > item.quantity) {
        Get.showSnackbar(
          const GetSnackBar(
            message: 'A quantidade para entrega não pode ser maior que a '
                'quantidade total do produto',
            duration: Duration(seconds: 3),
          ),
        );
        return;
      }
      shoppingCart.value.items[index] =
          item.copyWith(quantityToDelivery: result[0], deliveryNote: result[1]);
      saveShoppingCart();
    }
    focusNode.requestFocus();
  }

  RxList<OrderModel> pendingOrdersClient = <OrderModel>[].obs;

  void loadClientPendingOrders() async {
    pendingOrdersClient.clear();
    var orders = await _clientService
        .getOrdersFromClient(shoppingCart.value.client.value!.id!);

    orders.removeWhere((element) => element.isPaid || element.isJoined);
    orders.sort((a, b) => a.date.compareTo(b.date) * -1);
    pendingOrdersClient.assignAll(orders);
  }

  void goToViewOrder(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    loadClientPendingOrders();
  }

  void selectClient(ClientModel client) {
    shoppingCart.value.client(client);
    loadClientPendingOrders();
    saveShoppingCart();
  }

  void removeClient() {
    shoppingCart.value.client.value = null;
    pendingOrdersClient.clear();
    saveShoppingCart();
  }
}
