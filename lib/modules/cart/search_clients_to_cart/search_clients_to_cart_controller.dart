import 'package:diacritic/diacritic.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SearchClientsToCartController extends GetxController {
  final ClientService _clientService;
  final rotasIdArg = Get.arguments as List<String>;
  SearchClientsToCartController(
      {required ClientService clientService,
      required SalesRoutesService salesRoutesService})
      : _clientService = clientService;

  var search = ''.obs;

  final FocusNode searchFocusNode = FocusNode();

  final clients = RxList<ClientModel>([]);
  final oldClients = RxList<ClientModel>([]);

  final workers = RxList<Worker>([]);

  @override
  void onInit() {
    super.onInit();
    searchFocusNode.requestFocus();
    _getClients();
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchClient(),
        time: const Duration(milliseconds: 500),
      ),
    );
    super.onReady();
  }

  @override
  void onClose() {
    for (var worker in workers) {
      worker.dispose();
    }
    super.onClose();
  }

  Future<void> _getClients() async {
    List<ClientModel> result = [];
    if (rotasIdArg.isEmpty) {
      result = await _clientService.getClientsFromCache(null);
    } else {
      for (var rotaId in rotasIdArg) {
        final clientsTemp = await _clientService.getClientsFromCache(rotaId);
        result.addAll(clientsTemp);
      }
    }

    clients.assignAll(result);
    oldClients.assignAll(result);
  }

  void searchClient() {
    if (search.isEmpty) {
      clients.assignAll(oldClients);
      return;
    }
    clients.assignAll(
      oldClients.where(
        (client) =>
            removeDiacritics(client.name.toLowerCase())
                .contains(removeDiacritics(search.toLowerCase())) ||
            removeDiacritics(client.address.toLowerCase())
                .contains(removeDiacritics(search.toLowerCase())),
      ),
    );
  }
}
