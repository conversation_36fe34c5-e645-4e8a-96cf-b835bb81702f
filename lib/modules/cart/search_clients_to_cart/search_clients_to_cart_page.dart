import 'package:fl_app/application/helpers/sales_routes_helper.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './search_clients_to_cart_controller.dart';

class SearchClientsToCartPage extends GetView<SearchClientsToCartController> {
  const SearchClientsToCartPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        title: SearchBar(
          hintText: 'Procurar Cliente',
          elevation: WidgetStateProperty.all(1),
          focusNode: controller.searchFocusNode,
          onChanged: (value) {
            controller.search(value);
          },
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Obx(() => ListView.builder(
              itemCount: controller.clients.length,
              itemBuilder: (context, index) {
                final client = controller.clients[index];
                return Card(
                  child: ListTile(
                    title: Text(
                      client.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${client.address}${client.number != null ? ', nº ${client.number!}' : ''}, ${client.localDescription}',
                          style: const TextStyle(
                            fontSize: 15,
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                                color: Get.isDarkMode
                                    ? Colors.grey[500]!
                                    : Colors.black),
                            borderRadius: BorderRadius.circular(10),
                            color: Get.isDarkMode
                                ? Colors.grey[900]
                                : Colors.grey[200],
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 5, vertical: 2),
                          child: Text(
                            SalesRoutesHelper.getSaleRouteName(
                                    client.routeId) ??
                                'Rota não encontrada',
                            style: const TextStyle(
                              fontSize: 15,
                            ),
                          ),
                        ),
                      ],
                    ),
                    onTap: () {
                      Get.back(result: client);
                    },
                    trailing:
                        client.latitude == null || client.longitude == null
                            ? Icon(Icons.location_off, color: Colors.red[300])
                            : null,
                  ),
                );
              },
            )),
      ),
    );
  }
}
