import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/cart/search_clients_to_cart/search_clients_to_cart_bindings.dart';
import 'package:fl_app/modules/cart/search_clients_to_cart/search_clients_to_cart_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class SearchClientsToCartModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/search_clients_to_cart',
      page: () => const SearchClientsToCartPage(),
      binding: SearchClientsToCartBindings(),
    ),
  ];
}
