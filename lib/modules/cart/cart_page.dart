import 'package:action_slider/action_slider.dart';
import 'package:fl_app/modules/cart/widgets/cart_item_widget.dart';
import 'package:fl_app/modules/cart/widgets/total_restante_bar.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './cart_controller.dart';
import 'package:fl_app/modules/cart/widgets/client_selection_bottom_sheet.dart';

class CartPage extends GetView<CartController> {
  const CartPage({super.key});

  Widget _buildDeliveryBottomSheet(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border(
          top: BorderSide(
            color: Get.theme.colorScheme.primary,
            width: 2,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Marcar para entrega',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ),
              Tooltip(
                message: 'Ativar para marcar este pedido como entrega',
                child: Obx(() {
                  return Switch.adaptive(
                    value: controller.shoppingCart.value.toDelivery.value,
                    onChanged: (value) => controller.switchIsToDelivery(value),
                    activeColor: Theme.of(context).colorScheme.primary,
                  );
                }),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Campo "Restante"
          Obx(() {
            final restante = controller.shoppingCart.value.restante.value;
            final isError = restante < 0;

            return TextFormField(
              keyboardType: TextInputType.number,
              initialValue: restante != 0 ? restante.toStringAsFixed(2) : '',
              decoration: InputDecoration(
                labelText: 'Valor Restante',
                hintText: '0,00',
                prefixText: 'R\$ ',
                errorText:
                    isError ? 'O valor restante não pode ser negativo.' : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(
                    color: isError
                        ? Theme.of(context).colorScheme.error
                        : Theme.of(context).dividerColor,
                  ),
                ),
              ),
              onChanged: (value) =>
                  controller.setRestante(value.replaceAll(',', '.')),
            );
          }),
          const SizedBox(height: 24),

          // Botão "Voltar"
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Get.back();
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor:
                    Theme.of(context).colorScheme.secondaryContainer,
                foregroundColor:
                    Theme.of(context).colorScheme.onSecondaryContainer,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 3,
              ),
              child: const Text('Voltar'),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(
          () => Text(
            controller.orderEditing.value != null
                ? 'Editar Pedido'
                : 'Novo Pedido',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ),
        toolbarHeight: 50,
        actions: [
          Obx(
            () => controller.appState.value.isSelling
                ? Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: ChoiceChip(
                      label: const Text('Clientes da Rota'),
                      selected: controller.showClientsOnlyInRoute.value,
                      onSelected: (value) {
                        controller.showClientsOnlyInRoute.toggle();
                      },
                    ),
                  )
                : controller.user != null && controller.user!.cobrador
                    ? Padding(
                        padding: const EdgeInsets.only(right: 16.0),
                        child: ChoiceChip(
                          label: const Text('Minhas rotas'),
                          selected: controller.showMyClientsOnlyInRoute.value,
                          onSelected: (value) {
                            controller.showMyClientsOnlyInRoute.toggle();
                          },
                        ),
                      )
                    : Container(),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => ElevatedButton.icon(
                      onPressed: () {
                        Get.bottomSheet(
                          ClientSelectionBottomSheet(controller: controller),
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(10),
                              topRight: Radius.circular(10),
                            ),
                          ),
                          isScrollControlled: true,
                        );
                      },
                      icon: controller.shoppingCart.value.client.value != null
                          ? const Icon(Icons.person, size: 20)
                          : const Icon(Icons.person_add, size: 20),
                      label: Text(
                        controller.shoppingCart.value.client.value != null
                            ? controller.shoppingCart.value.client.value!.name
                            : 'Selecionar Cliente',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            controller.shoppingCart.value.client.value != null
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.surface,
                        foregroundColor:
                            controller.shoppingCart.value.client.value != null
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurface,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        side: BorderSide(
                          color:
                              controller.shoppingCart.value.client.value != null
                                  ? Colors.transparent
                                  : Theme.of(context).dividerColor,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      controller.goToSelectProduct();
                    },
                    style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.tertiary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        )),
                    child: Text(
                      'Adicionar Produtos',
                      style: TextStyle(
                          color: Theme.of(context).colorScheme.onTertiary),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Obx(() {
                return Scrollbar(
                  trackVisibility: true,
                  thumbVisibility: true,
                  child: ListView.builder(
                    itemCount: controller.shoppingCart.value.items.length,
                    itemBuilder: (context, index) {
                      final item = controller.shoppingCart.value.items[index];
                      return CartItemWidget(item, controller: controller);
                    },
                  ),
                );
              }),
            ),
            TotalRestanteBar(controller: controller),
            Row(
              children: [
                IconButton(
                  onPressed: () {
                    Get.bottomSheet(
                      _buildDeliveryBottomSheet(context),
                      isScrollControlled: true,
                    );
                  },
                  icon: const Icon(Icons.menu_rounded),
                ),
                Expanded(
                  child: Obx(() {
                    return IgnorePointer(
                      ignoring: controller.shoppingCart.value.client.value ==
                              null ||
                          (controller.shoppingCart.value.items.isEmpty &&
                              controller.shoppingCart.value.restante.value ==
                                  0),
                      child: ActionSlider.standard(
                        height: 50,
                        sliderBehavior: SliderBehavior.stretch,
                        backgroundColor: controller
                                        .shoppingCart.value.client.value ==
                                    null ||
                                (controller.shoppingCart.value.items.isEmpty &&
                                    controller.shoppingCart.value.restante
                                            .value ==
                                        0)
                            ? controller.orderEditing.value != null
                                ? Colors.grey[700]
                                : Colors.indigo[300]
                            : controller.orderEditing.value != null
                                ? Colors.grey[900]
                                : Colors.indigo[700],
                        toggleColor:
                            controller.shoppingCart.value.client.value ==
                                        null ||
                                    controller.shoppingCart.value.items.isEmpty
                                ? Colors.amberAccent[100]
                                : Colors.amberAccent,
                        icon: Icon(
                          controller.shoppingCart.value.client.value == null ||
                                  controller.shoppingCart.value.items.isEmpty
                              ? Icons.warning_rounded
                              : Icons.check,
                          color: Colors.black,
                        ),
                        action: (actionController) async {
                          actionController.loading();
                          if (controller.isSaving.value) {
                            actionController.reset();
                            return;
                          }
                          try {
                            final result = await controller.finalizarVenda();
                            if (result != null) {
                              actionController.success();
                              await Future.delayed(const Duration(seconds: 1));
                              if (controller.isEditing) {
                                Get.back(result: result);
                              } else {
                                Get.offNamed('/view-order', arguments: result);
                              }
                            } else {
                              actionController.reset();
                            }
                          } catch (e) {
                            actionController.failure();
                            Get.showSnackbar(
                              GetSnackBar(
                                title: 'Erro',
                                message: 'Erro ao finalizar venda: $e',
                                duration: const Duration(seconds: 5),
                                backgroundColor: Colors.red,
                              ),
                            );
                            await Future.delayed(const Duration(seconds: 1));
                            actionController.reset();
                          }
                        },
                        child: Text(
                          controller.orderEditing.value != null
                              ? 'Editar Pedido'
                              : 'Finalizar Venda',
                          style: TextStyle(
                              fontSize: 20,
                              color:
                                  controller.shoppingCart.value.client.value ==
                                              null ||
                                          controller
                                              .shoppingCart.value.items.isEmpty
                                      ? Colors.amberAccent[100]
                                      : Colors.amberAccent,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    );
                  }),
                ),
                const SizedBox(width: 8),
              ],
            ),
            const SizedBox(height: 4),
          ],
        ),
      ),
    );
  }
}
