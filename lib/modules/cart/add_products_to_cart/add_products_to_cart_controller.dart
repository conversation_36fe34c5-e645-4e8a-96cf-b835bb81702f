import 'package:diacritic/diacritic.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/modules/cart/add_products_to_cart/widgets/select_variations_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddProductsToCartController extends GetxController {
  final ProductRepository _productRepository;

  AddProductsToCartController({
    required ProductRepository productRepository,
  }) : _productRepository = productRepository;

  ShoppingCart shoppingCart = Get.arguments;

  var search = ''.obs;
  TextEditingController searchController = TextEditingController();
  var searchFocus = FocusNode();

  final workers = <Worker>[].obs;

  final products = <ProductModel>[].obs;
  final oldProducts = <ProductModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    getProducts();
  }

  @override
  void onReady() {
    workers.add(
      debounce(
        search,
        (_) => searchProduct(),
        time: const Duration(milliseconds: 500),
      ),
    );
  }

  Future<void> getProducts() async {
    products.assignAll(await _productRepository.getProductsFromCache());
    oldProducts.assignAll(products);
  }

  Future<void> addProductToCart(ProductModel product) async {
    // Se NÃO tiver variações, adiciona direto:
    if (product.variations.isEmpty) {
      shoppingCart.incrementProduct(product);
      update([product.id!]);
      return;
    }

    for (int i = 0; i < product.variations.length; i++) {
      final variation = product.variations[i];
      variation.product = product;
    }

    if (product.variations.length > 6) {
      await Get.dialog(SelectVariationsWidget(
        product: product,
        onTap: addVariationToCart,
        onLongPress: removeVariationFromCart,
      ));
    } else {
      await showModalBottomSheet(
        context: Get.context!,
        builder: (_) => SelectVariationsWidget(
          product: product,
          onTap: addVariationToCart,
          onLongPress: removeVariationFromCart,
        ),
      );
    }
  }

  addVariationToCart(VariationModel variation) {
    shoppingCart.incrementVariation(variation);
    update([variation.product!.id!]);
    update([variation.product!.id! + variation.id]);
  }

  removeVariationFromCart(VariationModel variation) {
    shoppingCart.removeVariation(variation);
    update([variation.product!.id!]);
    update([variation.product!.id! + variation.id]);
  }

  void searchProduct() {
    if (search.isEmpty) {
      products.assignAll(oldProducts);
      return;
    }
    products.assignAll(
      oldProducts.where(
        (product) => removeDiacritics(product.nome.toLowerCase())
            .contains(removeDiacritics(search.toLowerCase())),
      ),
    );
  }

  int getProductQuantity(String productId) {
    int quantity = 0;
    for (var item in shoppingCart.items) {
      if (item.product.id == productId) {
        quantity += item.quantity;
      }
    }

    return quantity;
  }

  int getVariationQuantity(String variationId, String productId) {
    if (variationId == productId) {
      final item = shoppingCart.items.firstWhereOrNull((item) {
        return item.product.id == productId && item.selectedVariation == null;
      });
      return item?.quantity ?? 0;
    }
    final item = shoppingCart.items
        .firstWhereOrNull((item) => item.selectedVariation?.id == variationId);
    return item?.quantity ?? 0;
  }

  @override
  void onClose() {
    for (var worker in workers) {
      worker.dispose();
    }
    super.onClose();
  }

  clearSearch() {
    search('');
    searchController.clear();
  }

  removeProductFromCart(ProductModel product) {
    shoppingCart.removeProduct(product);
    for (var variation in product.variations) {
      shoppingCart.removeVariation(variation);
    }
    update([product.id!]);
  }
}
