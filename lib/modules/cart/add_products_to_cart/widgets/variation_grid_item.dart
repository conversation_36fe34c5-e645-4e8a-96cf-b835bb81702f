import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class VariationGridItem extends StatelessWidget {
  final VariationModel variation;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  const VariationGridItem({
    super.key,
    required this.variation,
    this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        clipBehavior:
            Clip.hardEdge, // Garante que o InkWell seja cortado pelo Card
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: variation.imageUrl != null &&
                              variation.imageUrl!.isNotEmpty
                          ? CachedNetworkImage(
                              imageUrl: variation.imageUrl!,
                              fit: BoxFit.cover,
                              memCacheHeight: 500,
                              memCacheWidth: 500,
                              progressIndicatorBuilder:
                                  (context, url, downloadProgress) => Center(
                                child: CircularProgressIndicator(
                                  value: downloadProgress.progress,
                                  color: Get.isDarkMode
                                      ? Colors.indigo[400]
                                      : Colors.indigo[900],
                                  strokeCap: StrokeCap.round,
                                ),
                              ),
                              useOldImageOnUrlChange: true,
                              errorWidget: (context, url, error) =>
                                  GestureDetector(
                                onTap: onTap,
                                child: Column(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        width: double.maxFinite,
                                        height: double.maxFinite,
                                        color: Colors.grey[300],
                                        child: Center(
                                          child: FaIcon(
                                            FontAwesomeIcons.image,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onSurface,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                          : Container(
                              color: Theme.of(context).colorScheme.surface,
                              child: Center(
                                child: FaIcon(
                                  FontAwesomeIcons.image,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                ),
                              ),
                            ),
                    ),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                variation.name,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                NumberFormatHelper.format(
                                    variation.price.toDouble()),
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color!
                                          .withOpacity(0.8),
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
