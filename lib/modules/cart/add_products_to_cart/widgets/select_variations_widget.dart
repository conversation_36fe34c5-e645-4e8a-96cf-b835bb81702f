import 'package:fl_app/modules/cart/add_products_to_cart/widgets/variation_grid_item.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/modules/cart/add_products_to_cart/add_products_to_cart_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SelectVariationsWidget extends StatelessWidget {
  final ProductModel product;
  final Function(VariationModel) onTap;
  final Function(VariationModel) onLongPress;
  const SelectVariationsWidget({
    super.key,
    required this.product,
    required this.onTap,
    required this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.maxFinite,
      height: product.variations.length > 6 ? Get.height * 0.8 : null,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              'Variações de ${product.nome}',
              style: Theme.of(context).textTheme.titleLarge,
            ),
          ),
          Expanded(
            child: GridView.builder(
              padding: EdgeInsets.zero,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                childAspectRatio: 0.7,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: product.variations.length,
              itemBuilder: (context, index) {
                final item = product.variations[index];

                return GetBuilder<AddProductsToCartController>(
                  id: product.id! + item.id,
                  builder: (controller) {
                    int quantity =
                        controller.getVariationQuantity(item.id, product.id!);

                    return Stack(
                      children: [
                        VariationGridItem(
                          variation: item,
                          onTap: () => onTap(item),
                          onLongPress: () => onLongPress(item),
                        ),
                        if (quantity > 0)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: CircleAvatar(
                              radius: 14,
                              backgroundColor: Theme.of(context)
                                  .colorScheme
                                  .onSecondaryFixedVariant,
                              child: Text(
                                '$quantity',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 62,
            width: double.maxFinite,
            child: Padding(
              padding: const EdgeInsets.only(
                left: 8,
                right: 8,
                bottom: 8,
              ),
              child: ElevatedButton(
                onPressed: Get.back,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.secondary,
                  foregroundColor: Theme.of(context).colorScheme.onSecondary,
                ),
                child: const Text('Fechar'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
