import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/cart/add_products_to_cart/add_products_to_cart_bindings.dart';
import 'package:fl_app/modules/cart/add_products_to_cart/add_products_to_cart_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class AddProductsToCardModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/add-products-to-card',
      page: () => const AddProductsToCartPage(),
      binding: AddProductsToCartBindings(),
    ),
  ];
}
