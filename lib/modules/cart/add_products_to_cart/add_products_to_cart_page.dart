import 'package:fl_app/application/ui/widgets/product_grid_item.dart';
import 'package:fl_app/modules/cart/add_products_to_cart/add_products_to_cart_controller.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class AddProductsToCartPage extends GetView<AddProductsToCartController> {
  const AddProductsToCartPage({super.key});

  Widget _buildSearchField(BuildContext context) {
    return Obx(() {
      return TextField(
        controller: controller.searchController,
        focusNode: controller.searchFocus,
        decoration: InputDecoration(
          hintText: 'Pesquisar produtos',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: controller.search.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: controller.clearSearch,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          fillColor: Theme.of(context).cardColor,
          filled: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        ),
        onChanged: controller.search.call,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Adicionar ao Carrinho'),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: _buildSearchField(context),
          ),
        ),
      ),
      body: Obx(() {
        final products = controller.products;

        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: RefreshIndicator(
            onRefresh: () async {
              await controller.getProducts();
            },
            child: GridView.builder(
              padding: EdgeInsets.zero,
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: MediaQuery.of(context).size.width < 600 ? 3 : 4,
                childAspectRatio: 0.7,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: products.length,
              itemBuilder: (context, index) {
                final product = products[index];

                return GetBuilder<AddProductsToCartController>(
                  id: product.id!,
                  builder: (controller) {
                    final quantity = controller.getProductQuantity(product.id!);
                    return Stack(
                      children: [
                        ProductGridItem(
                          product: product,
                          onTap: () => controller.addProductToCart(product),
                          onLongPress: () =>
                              controller.removeProductFromCart(product),
                        ),
                        if (quantity > 0)
                          Positioned(
                            top: 8,
                            right: 8,
                            child: CircleAvatar(
                              radius: 14,
                              backgroundColor: Theme.of(context)
                                  .colorScheme
                                  .onSecondaryFixedVariant,
                              child: Text(
                                '$quantity',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        );
      }),
    );
  }
}
