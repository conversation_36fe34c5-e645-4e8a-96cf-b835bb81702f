import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/modules/cart/cart_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TotalRestanteBar extends StatelessWidget {
  final CartController controller;

  const TotalRestanteBar({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Obx(
        () => controller.shoppingCart.value.restante.value != 0
            ? Column(
                children: [
                  _buildRow(
                      'Total', controller.shoppingCart.value.getTotalCart()),
                  _buildRow(
                      'Restante', controller.shoppingCart.value.restante.value),
                  const Divider(),
                  _buildRow(
                    'Total a pagar',
                    controller.shoppingCart.value.getTotalCart() +
                        controller.shoppingCart.value.restante.value,
                    isBold: true,
                  ),
                ],
              )
            : _buildRow(
                'Total',
                controller.shoppingCart.value.getTotalCart(),
                isBold: true,
              ),
      ),
    );
  }

  Widget _buildRow(String label, double value, {bool isBold = false}) {
    return Row(
      children: [
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 17,
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ),
        Text(
          NumberFormatHelper.format(value),
          style: TextStyle(
            fontSize: 17,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
