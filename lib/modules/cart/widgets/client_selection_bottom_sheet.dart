import 'package:fl_app/application/ui/widgets/order_list_card.dart';
import 'package:fl_app/modules/cart/cart_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ClientSelectionBottomSheet extends StatelessWidget {
  const ClientSelectionBottomSheet({
    super.key,
    required this.controller,
  });

  final CartController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        height: controller.shoppingCart.value.client.value == null
            ? Get.height * 0.85
            : controller.pendingOrdersClient.isNotEmpty
                ? Get.height * 0.7
                : Get.height * 0.4,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
          border: Border(
            top: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
        ),
        child: Obx(() {
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: controller.isLoadingClientsNearbyFirstTime.value
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : controller.clientsNearby.isEmpty
                    ? NoClientsFoundWidget(controller: controller)
                    : controller.shoppingCart.value.client.value == null
                        ? ClientsListWidget(controller: controller)
                        : ClientDetailsWidget(controller: controller),
          );
        }),
      );
    });
  }
}

class NoClientsFoundWidget extends StatelessWidget {
  const NoClientsFoundWidget({
    super.key,
    required this.controller,
  });

  final CartController controller;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.location_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'Nenhum cliente próximo encontrado',
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: controller.goToAddClient,
              icon: const Icon(Icons.person_add),
              label: const Text('Adicionar novo cliente'),
            ),
            TextButton.icon(
              onPressed: controller.refreshClientsNearby,
              icon: const Icon(Icons.refresh),
              label: const Text('Atualizar'),
            ),
          ],
        ),
      ),
    );
  }
}

class ClientsListWidget extends StatelessWidget {
  const ClientsListWidget({
    super.key,
    required this.controller,
  });

  final CartController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(
            context,
            controller.appState.value.isSelling &&
                    controller.showClientsOnlyInRoute.value
                ? [controller.appState.value.isSellingRouteId!]
                : []),
        Expanded(
          child: RefreshIndicator(
            onRefresh: controller.refreshClientsNearby,
            child: ListView.builder(
              itemCount: controller.clientsNearby.length,
              itemBuilder: (context, index) {
                final client = controller.clientsNearby[index];
                return ClientListTile(
                  client: client,
                  onTap: () => controller.selectClient(client),
                  distance: client.latitude != null && client.longitude != null
                      ? controller.currentPosition.value != null
                          ? client
                              .distanceTo(controller.currentPosition.value)!
                              .toInt()
                          : null
                      : null,
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader(BuildContext context, List<String> routes) {
    return ListTile(
      title: Text(
        'Clientes próximos',
        style: Theme.of(context).textTheme.titleLarge,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              controller.goToSearchClientsToCart(routes);
            },
          ),
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: controller.goToAddClient,
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Get.back(),
          ),
        ],
      ),
    );
  }
}

class ClientListTile extends StatelessWidget {
  const ClientListTile({
    super.key,
    required this.client,
    required this.onTap,
    this.distance,
  });

  final dynamic client;
  final VoidCallback onTap;
  final int? distance;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primaryContainer,
          child: Text(
            client.name.substring(0, 1).toUpperCase(),
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimaryContainer,
            ),
          ),
        ),
        title: Text(
          client.name,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        subtitle: Text(
          '${client.address}, ${client.number ?? ''}\n${client.localDescription}',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        onTap: onTap,
        trailing: distance != null
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.location_on,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  Text(
                    '${distance}m',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              )
            : Icon(
                Icons.location_off,
                color: Theme.of(context).colorScheme.error,
              ),
      ),
    );
  }
}

class ClientDetailsWidget extends StatelessWidget {
  const ClientDetailsWidget({
    super.key,
    required this.controller,
  });

  final CartController controller;

  @override
  Widget build(BuildContext context) {
    final client = controller.shoppingCart.value.client.value!;
    return Column(
      children: [
        _buildHeader(context),
        _buildClientInfo(context, client),
        if (controller.pendingOrdersClient.isNotEmpty)
          _buildPendingOrders(context),
        _buildBackButton(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return ListTile(
      title: Text(
        'Cliente Selecionado',
        style: Theme.of(context).textTheme.titleLarge,
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.swap_horiz),
            onPressed: () {
              controller.removeClient();
            },
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Get.back(),
          ),
        ],
      ),
    );
  }

  Widget _buildClientInfo(BuildContext context, client) {
    return GestureDetector(
      onTap: controller.goToClientDetails,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              child: Text(
                client.name.substring(0, 1).toUpperCase(),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    client.name,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text('${client.address}, ${client.number ?? ''}'),
                  Text(client.localDescription),
                  if (client.phoneNumber != null && client.phoneNumber != '')
                    Text('Telefone: ${client.phoneNumber}'),
                  if (client.latitude == null || client.longitude == null)
                    Row(
                      children: [
                        Icon(
                          Icons.location_off,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Sem localização',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.error,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: controller.goToEditClient,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingOrders(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Pedidos Pendentes (${controller.pendingOrdersClient.length})',
                style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: controller.pendingOrdersClient.length,
                itemBuilder: (context, index) {
                  final order = controller.pendingOrdersClient[index];
                  return OrderListCard(
                    order: order,
                    onTap: (order) => controller.goToViewOrder(order),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackButton(BuildContext context) {
    Widget backButton = Padding(
      padding: const EdgeInsets.all(16.0),
      child: ElevatedButton(
        onPressed: () => Get.back(),
        style: ElevatedButton.styleFrom(
          minimumSize: const Size(double.infinity, 48),
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
        ),
        child: const Text('Voltar'),
      ),
    );

    if (controller.pendingOrdersClient.isEmpty) {
      return Expanded(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [backButton],
        ),
      );
    } else {
      return backButton;
    }
  }
}
