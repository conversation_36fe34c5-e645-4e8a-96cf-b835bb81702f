import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/modules/cart/cart_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class CartItemWidget extends StatelessWidget {
  const CartItemWidget(this.item, {super.key, required this.controller});

  final CartItem item;
  final CartController controller;

  @override
  Widget build(BuildContext context) {
    return Slidable(
      key: ValueKey(item.product.id), // Garantir unicidade
      closeOnScroll: true,
      startActionPane: ActionPane(
        motion: const DrawerMotion(),
        children: [
          SlidableAction(
            onPressed: (context) => controller.editValue(item),
            backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            padding: const EdgeInsets.all(2),
            icon: Icons.edit,
            label: 'Editar',
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(10),
              bottomLeft: Radius.circular(10),
            ),
          ),
          SlidableAction(
            onPressed: (context) => controller.addProductToDelivery(item),
            backgroundColor: Theme.of(context).colorScheme.secondary,
            foregroundColor: Theme.of(context).colorScheme.onSecondaryContainer,
            padding: const EdgeInsets.all(2),
            icon: Icons.delivery_dining,
            label: 'Entregar',
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(10),
              bottomRight: Radius.circular(10),
            ),
          ),
        ],
      ),
      endActionPane: ActionPane(
        motion: const DrawerMotion(),
        children: [
          SlidableAction(
            onPressed: (context) => controller.removeItem(item),
            backgroundColor: Theme.of(context).colorScheme.error,
            foregroundColor: Theme.of(context).colorScheme.onError,
            icon: Icons.delete,
            label: 'Excluir',
            borderRadius: BorderRadius.circular(10),
          ),
        ],
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        child: ListTile(
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          leading: _buildProductImage(context),
          title: Text(
            item.selectedVariation != null
                ? '${item.product.nome} - ${item.selectedVariation!.name}'
                : item.product.nome,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          subtitle: _buildSubtitle(context),
          trailing: _buildQuantityControls(context),
        ),
      ),
    );
  }

  Widget _buildProductImage(BuildContext context) {
    return SizedBox(
      width: 60,
      height: 60,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: item.selectedVariation != null
            ? item.selectedVariation!.imageUrl != null &&
                    item.selectedVariation!.imageUrl!.isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: item.selectedVariation!.imageUrl!,
                    fit: BoxFit.fitHeight,
                    placeholder: (context, url) => Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: const Icon(
                        FontAwesomeIcons.image,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : Container(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Center(
                      child: FaIcon(
                        FontAwesomeIcons.image,
                        color: Colors.grey,
                      ),
                    ),
                  )
            : item.product.imagem != null
                ? CachedNetworkImage(
                    imageUrl: item.product.imagem!,
                    fit: BoxFit.fitHeight,
                    placeholder: (context, url) => Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: const Icon(
                        FontAwesomeIcons.image,
                        color: Colors.grey,
                      ),
                    ),
                  )
                : Container(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    child: const Center(
                      child: FaIcon(
                        FontAwesomeIcons.image,
                        color: Colors.grey,
                      ),
                    ),
                  ),
      ),
    );
  }

  Widget _buildSubtitle(BuildContext context) {
    var price = item.customValue != 0
        ? item.customValue
        : item.selectedVariation != null
            ? item.selectedVariation!.price
            : item.product.valor;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Quantidade e Valor
        Row(
          children: [
            Text(
              '${item.quantity} x ',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              NumberFormatHelper.format(price.toDouble()),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        // Entregas
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.quantityToDelivery > 0)
              Container(
                decoration: BoxDecoration(
                  color:
                      Theme.of(context).colorScheme.secondary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.delivery_dining,
                      size: 16,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${item.quantityToDelivery} ${item.deliveryNote.isNotEmpty ? '- ${item.deliveryNote}' : ''}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.secondary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ),
            if (item.customValue != 0)
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                margin: const EdgeInsets.only(left: 4),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.edit,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Valor alterado',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
              ),
          ],
        ),
        if (item.product.isDeleted)
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.error.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.delete,
                  size: 16,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(width: 4),
                Text(
                  'Produto excluído',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildQuantityControls(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Botão de Decremento
        IconButton(
          onPressed:
              item.quantity > 1 ? () => controller.decrementItem(item) : null,
          icon: const Icon(Icons.remove),
          color: item.quantity > 1
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).disabledColor,
        ),
        // Quantidade
        Text(
          item.quantity.toString(),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        // Botão de Incremento
        IconButton(
          onPressed: () => controller.incrementItem(item),
          icon: const Icon(Icons.add),
          color: Theme.of(context).colorScheme.primary,
        ),
      ],
    );
  }
}
