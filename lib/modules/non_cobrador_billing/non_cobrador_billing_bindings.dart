import 'package:get/get.dart';
import './non_cobrador_billing_controller.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:fl_app/services/client_archive/client_archive_service.dart';
import 'package:fl_app/services/client_archive/client_archive_service_impl.dart';

class NonCobradorBillingBindings implements Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ClientArchiveService>(
      () => ClientArchiveServiceImpl(
        orderRepository: Get.find<OrderRepository>(),
      ),
    );

    Get.lazyPut(
      () => NonCobradorBillingController(
        orderRepository: Get.find<OrderRepository>(),
        userService: Get.find<UserService>(),
        clientRepository: Get.find<ClientRepository>(),
        clientArchiveService: Get.find<ClientArchiveService>(),
      ),
    );
  }
}
