import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/non_cobrador_billing/non_cobrador_map/non_cobrador_map_bindings.dart';
import 'package:fl_app/modules/non_cobrador_billing/non_cobrador_map/non_cobrador_map_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class NonCobradorMapModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/non_cobrador_map',
      page: () => const NonCobradorMapPage(),
      binding: NonCobradorMapBindings(),
    ),
  ];
}
