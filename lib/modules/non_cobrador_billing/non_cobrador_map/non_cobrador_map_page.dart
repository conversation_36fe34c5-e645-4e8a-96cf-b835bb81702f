import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import './non_cobrador_map_controller.dart';

class NonCobradorMapPage extends GetView<NonCobradorMapController> {
  const NonCobradorMapPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          title: const Text('Mapa Cobrança'),
          actions: [
            IconButton(
              onPressed: () => controller.isSatelliteView.toggle(),
              icon: Icon(
                controller.isSatelliteView.value ? Icons.map : Icons.satellite,
              ),
            ),
            IconButton(
              onPressed: controller.toggleFollowUserLocation,
              icon: Icon(
                controller.isFollowUserLocation.value
                    ? Icons.navigation_rounded
                    : Icons.navigation_outlined,
                color: controller.isFollowUserLocation.value
                    ? Colors.blue
                    : Colors.grey,
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(30),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Center(
                child: Row(
                  spacing: 8,
                  children: [
                    Obx(
                      () => Container(
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        child: Text(
                            '${controller.nonCobradorController.activeOrders.length} Pendentes',
                            style: Get.textTheme.titleMedium?.copyWith(
                              color: Colors.white,
                            )),
                      ),
                    ),
                    Obx(
                      () => Container(
                        decoration: BoxDecoration(
                          color: Colors.indigo,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        child: Text(
                          '${controller.nonCobradorController.archivedOrders.length} Separados',
                          style: Get.textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        body: Stack(
          children: [
            controller.isCameraPositionLoaded.value
                ? Obx(
                    () => GoogleMap(
                      onMapCreated: controller.onMapCreated,
                      initialCameraPosition: CameraPosition(
                        target: controller.cameraPosition!.target,
                        zoom: controller.currentZoom.value,
                      ),
                      mapType: controller.isSatelliteView.value
                          ? MapType.satellite
                          : MapType.normal,
                      markers: controller.markers.value,
                      myLocationEnabled: true,
                      myLocationButtonEnabled: true,
                      zoomControlsEnabled: false,
                      zoomGesturesEnabled: true,
                      onCameraMove: (cameraPosition) {
                        controller.updateCurrentZoom(cameraPosition.zoom);
                        controller.updateCurrentTilt(cameraPosition.tilt);
                        controller.updateCurrentBearing(cameraPosition.bearing);
                      },
                    ),
                  )
                : const Center(
                    child: CircularProgressIndicator(),
                  ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 8,
              child: SizedBox(
                height: 120,
                child: Obx(
                  () => ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount:
                        controller.nonCobradorController.groupedOrders.length,
                    itemBuilder: (context, index) {
                      final order = controller
                          .nonCobradorController.groupedOrders.entries
                          .toList()[index]
                          .value
                          .first;
                      bool isArchived = controller
                          .nonCobradorController.archivedOrders
                          .where((element) => element.key == order.clientId)
                          .isNotEmpty;
                      return InkWell(
                        onTap: () => controller.goToClientPosition(order),
                        onLongPress: () async {
                          var ordersClient = controller
                              .nonCobradorController.groupedOrders.entries
                              .toList()[index]
                              .value;

                          if (ordersClient.length == 1) {
                            await controller.nonCobradorController
                                .goToOrderDetails(order);
                            controller.updateMarker(order.clientId);
                          } else {
                            var client = controller
                                .nonCobradorController.allClientsInRoutes
                                .firstWhereOrNull(
                                    (element) => element.id == order.clientId);
                            if (client != null) {
                              await controller.nonCobradorController
                                  .goToClientDetails(client);
                              controller.updateMarker(client.id!);
                            }
                          }
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: BoxDecoration(
                            color: Get.theme.scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Get.theme.colorScheme.primary
                                    .withAlpha(100),
                                blurRadius: 2,
                                offset: const Offset(0, 2),
                              ),
                            ],
                            border: Border.all(
                              color:
                                  Get.theme.colorScheme.primary.withAlpha(100),
                              width: 1,
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          child: Column(
                            spacing: 2,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              if (isArchived)
                                Container(
                                  decoration: BoxDecoration(
                                    color: Colors.indigo,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 2),
                                  child: const Text(
                                    'Separado',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 11,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              Text(
                                order.clientName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                '${order.clientAddress}${order.clientNumber != null && order.clientNumber != '' ? ', ${order.clientNumber}' : ''}',
                                textAlign: TextAlign.center,
                                style: const TextStyle(fontSize: 12),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              Text(
                                order.clientLocalDescription,
                                textAlign: TextAlign.center,
                                style: const TextStyle(fontSize: 12),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              FutureBuilder<double>(
                                future: controller.calculateDistance(order),
                                builder: (context, snapshot) {
                                  if (snapshot.hasData) {
                                    return Text(
                                      '${snapshot.data!.toStringAsFixed(0)} m',
                                      style: const TextStyle(fontSize: 12),
                                    );
                                  } else {
                                    return const Text('Calculando...',
                                        style: TextStyle(fontSize: 12));
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
