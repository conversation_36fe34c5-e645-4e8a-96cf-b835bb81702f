import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/non_cobrador_billing/non_cobrador_billing_bindings.dart';
import 'package:fl_app/modules/non_cobrador_billing/non_cobrador_billing_page.dart';
import 'package:fl_app/modules/non_cobrador_billing/non_cobrador_map/non_cobrador_map_module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class NonCobradorBillingModule extends Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/non_cobrador_billing',
      page: () => NonCobradorBillingPage(),
      binding: NonCobradorBillingBindings(),
    ),
    ...NonCobradorMapModule().routers,
  ];
}
