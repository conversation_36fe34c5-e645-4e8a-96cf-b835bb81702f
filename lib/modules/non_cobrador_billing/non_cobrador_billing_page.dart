import 'package:fl_app/application/ui/widgets/valor_recebido_tile.dart';
import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './non_cobrador_billing_controller.dart';

class NonCobradorBillingPage extends GetView<NonCobradorBillingController> {
  NonCobradorBillingPage({super.key});

  final _showSearch = false.obs;

  Widget _buildSearchField(BuildContext context) {
    return TextField(
      controller: controller.searchController,
      focusNode: controller.searchFocus,
      decoration: InputDecoration(
        hintText: 'Pesquisar',
        prefixIcon: const Icon(Icons.search),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        fillColor: Theme.of(context).cardColor,
        filled: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      ),
      onChanged: controller.search.call,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Text(
                "Cobrança em",
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              Text(controller.routesName.value),
            ],
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          children: [
            Obx(
              () => ValorRecebidoTile(
                receivedValue: controller.receivedValue.value,
                receivedValueDinheiro: controller.receivedValueDinheiro.value,
                receivedValueCartao: controller.receivedValueCartao.value,
                receivedValuePix: controller.receivedValuePix.value,
                ordersDinheiro:
                    controller.ordersDinheiro.map((e) => e.value).toList(),
                ordersCartao:
                    controller.ordersCartao.map((e) => e.value).toList(),
                ordersPix: controller.ordersPix.map((e) => e.value).toList(),
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => controller.goToVendasAnteriores(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.calendar,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Text('Vendas por Data'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => controller.goToClientesRota(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigoAccent,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.users,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Text('Clientes'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => controller.goToMap(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo,
                      foregroundColor: Colors.white,
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          FontAwesomeIcons.map,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Text('Mapa'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Obx(
              () => Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  children: [
                    if (!_showSearch.value) ...[
                      Expanded(
                        child: Text(
                          'Pendentes (${controller.activeOrders.length})',
                          textAlign: TextAlign.start,
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                      ),
                    ] else ...[
                      Expanded(child: _buildSearchField(context)),
                    ],
                    IconButton(
                      icon: Icon(
                        _showSearch.value ? Icons.close : Icons.search,
                      ),
                      onPressed: () {
                        _showSearch.value = !_showSearch.value;
                        if (!_showSearch.value) {
                          controller.clearSearch();
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
            Obx(
              () => Visibility(
                visible: !_showSearch.value,
                child: Row(
                  children: [
                    if (controller.groupedOrders.isNotEmpty &&
                        controller.archivedOrders.isNotEmpty)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            controller.openModalOrdersArchive();
                          },
                          label: Text(
                              'Clientes separados (${controller.archivedOrders.length})'),
                          icon: const Icon(Icons.visibility_off),
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                  color:
                                      Theme.of(context).colorScheme.secondary),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Obx(
                () {
                  if (controller.isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(
                        strokeCap: StrokeCap.round,
                      ),
                    );
                  }

                  if (controller.groupedOrders.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text('Nenhum pedido pendente.'),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: () async {
                              controller.isLoading(true);
                              await Future.delayed(
                                  const Duration(milliseconds: 500));
                              controller.refreshOrders();
                            },
                            child: const Text('Atualizar'),
                          ),
                        ],
                      ),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      controller.refreshOrders();
                    },
                    child: ListView.builder(
                      itemCount: controller.activeOrders
                          .where((e) => e.value.any((order) =>
                              order.clientName.toLowerCase().contains(
                                  controller.search.value.toLowerCase()) ||
                              order.clientAddress.toLowerCase().contains(
                                  controller.search.value.toLowerCase()) ||
                              order.clientLocalDescription
                                  .toLowerCase()
                                  .contains(
                                      controller.search.value.toLowerCase())))
                          .length,
                      itemBuilder: (context, index) {
                        final entry = controller.activeOrders
                            .where((e) => e.value.any((order) =>
                                order.clientName.toLowerCase().contains(
                                    controller.search.value.toLowerCase()) ||
                                order.clientAddress.toLowerCase().contains(
                                    controller.search.value.toLowerCase()) ||
                                order.clientLocalDescription
                                    .toLowerCase()
                                    .contains(
                                        controller.search.value.toLowerCase())))
                            .toList()[index];
                        final orders = entry.value;
                        final order = orders.first;
                        return Slidable(
                          closeOnScroll: true,
                          startActionPane: ActionPane(
                            motion: const ScrollMotion(),
                            children: [
                              SlidableAction(
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(10),
                                  topLeft: Radius.circular(10),
                                ),
                                icon: Icons.visibility_off,
                                label: 'Separar',
                                onPressed: (context) {
                                  controller
                                      .markClientToNotShow(order.clientId);
                                },
                                backgroundColor:
                                    Theme.of(context).colorScheme.secondary,
                                foregroundColor: Colors.white,
                              ),
                            ],
                          ),
                          child: BillingOrderListTile(
                            order: order,
                            onTap: (orderModel) =>
                                controller.onClientTap(orderModel),
                            location: controller.location,
                            showRoute: controller.routesId.isNotEmpty &&
                                controller.routesId.length > 1,
                            bottomWidgets: orders.length > 1
                                ? [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary
                                            .withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      margin: const EdgeInsets.only(top: 4),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            '${orders.length} pedidos',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodySmall
                                                ?.copyWith(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .primary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                : null,
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
