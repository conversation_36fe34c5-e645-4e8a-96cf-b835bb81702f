import 'dart:async';
import 'dart:developer';

import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/services/client_archive/client_archive_service.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';

class NonCobradorBillingController extends GetxController {
  final OrderRepository _orderRepository;
  final UserService _userService;
  final ClientRepository _clientRepository;
  final ClientArchiveService _clientArchiveService;

  RxInt selectedTabIndex = 0.obs;

  RxList<MapEntry<String, List<OrderModel>>> get activeOrders =>
      _clientArchiveService.getActiveClientsWithOrders();

  RxList<MapEntry<String, List<OrderModel>>> get archivedOrders =>
      _clientArchiveService.getArchivedClientsWithOrders();

  RxMap<String, List<OrderModel>> get groupedOrders =>
      _clientArchiveService.getClientsWithOrders();

  NonCobradorBillingController({
    required OrderRepository orderRepository,
    required UserService userService,
    required ClientRepository clientRepository,
    required ClientArchiveService clientArchiveService,
  })  : _orderRepository = orderRepository,
        _userService = userService,
        _clientRepository = clientRepository,
        _clientArchiveService = clientArchiveService;

  List<String> routesId = Get.arguments[0];
  RxString routesName = (Get.arguments[1] as String).obs;

  RxBool isLoading = false.obs;

  RxList<ClientModel> allClientsInRoutes = <ClientModel>[].obs;

  RxDouble receivedValue = 0.0.obs;
  RxDouble receivedValueDinheiro = 0.0.obs;
  RxDouble receivedValueCartao = 0.0.obs;
  RxDouble receivedValuePix = 0.0.obs;

  RxList<MapEntry<DateTime, OrderModel>> ordersDinheiro =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersCartao =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersPix =
      <MapEntry<DateTime, OrderModel>>[].obs;

  Position? location;

  Rxn<UserModel> user = Rxn<UserModel>();

  RxString search = ''.obs;

  TextEditingController searchController = TextEditingController();

  final workers = <Worker>[].obs;

  FocusNode searchFocus = FocusNode();

  clearSearch() {
    search.value = '';
    searchController.clear();
    searchFocus.unfocus();
  }

  @override
  void onInit() {
    init();
    super.onInit();
  }

  @override
  void onClose() {
    stopTimer();
    super.onClose();
  }

  Timer? _timer;

  void startTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer.periodic(const Duration(seconds: 10), (timer) {
      refreshOrders();
    });
  }

  void stopTimer() {
    _timer?.cancel();
  }

  void init() async {
    user.value = await _userService.getUserAuthenticated();
    user.refresh();

    await _clientArchiveService.initialize('nonCobradorArchiveBox');
    refreshOrders();
    refreshClientsInRoutes();
    startTimer();
  }

  void refreshClientsInRoutes() async {
    List<ClientModel> clients = [];
    for (var routeId in routesId) {
      clients.addAll(await _clientRepository.getClientsFromCache(routeId));
    }
    allClientsInRoutes.assignAll(clients);
  }

  Future<void> markClientToNotShow(String? clientId) async {
    if (clientId != null) {
      await _clientArchiveService.archiveClient(clientId);
    }
  }

  Future<void> markClientToShow(String? clientId) async {
    if (clientId != null) {
      await _clientArchiveService.unarchiveClient(clientId);
    }
  }

  void openModalOrdersArchive() async {
    await Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          border: Border(
            top: BorderSide(
              color: Get.theme.colorScheme.primary.withValues(
                alpha: 0.8,
              ),
              width: 3,
            ),
          ),
        ),
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'Clientes separados:',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: Obx(
                () => archivedOrders.isEmpty
                    ? Center(
                        child: Column(
                          children: [
                            const Text('Nenhum cliente separado'),
                            const SizedBox(height: 20),
                            ElevatedButton(
                              onPressed: () {
                                Get.back();
                              },
                              child: const Text('Fechar'),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: archivedOrders.length,
                        itemBuilder: (context, index) {
                          var clientGroup = archivedOrders[index];
                          var order = clientGroup.value.first;
                          return Column(
                            children: [
                              Slidable(
                                closeOnScroll: true,
                                startActionPane: ActionPane(
                                  motion: const ScrollMotion(),
                                  children: [
                                    SlidableAction(
                                      borderRadius: const BorderRadius.only(
                                        bottomLeft: Radius.circular(10),
                                        topLeft: Radius.circular(10),
                                      ),
                                      icon: Icons.visibility,
                                      label: 'Mostrar',
                                      onPressed: (context) async {
                                        await markClientToShow(order.clientId);
                                        if (archivedOrders.isEmpty) {
                                          Get.back();
                                        }
                                      },
                                      backgroundColor: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      foregroundColor: Colors.white,
                                    ),
                                  ],
                                ),
                                child: BillingOrderListTile(
                                  order: order,
                                  onTap: (orderModel) =>
                                      onClientTap(orderModel),
                                  location: location,
                                  showRoute: routesId.isNotEmpty &&
                                      routesId.length > 1,
                                  bottomWidgets: clientGroup.value.length > 1
                                      ? [
                                          Container(
                                            decoration: BoxDecoration(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .primary
                                                  .withOpacity(0.2),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 6, vertical: 2),
                                            margin:
                                                const EdgeInsets.only(top: 4),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  '${clientGroup.value.length} pedidos',
                                                  style: Theme.of(context)
                                                      .textTheme
                                                      .bodySmall
                                                      ?.copyWith(
                                                        color: Theme.of(context)
                                                            .colorScheme
                                                            .primary,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ]
                                      : null,
                                ),
                              )
                            ],
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> refreshOrders() async {
    isLoading(true);
    await refreshLocation();
    await _clientArchiveService.refreshOrders(routesId);
    isLoading(false);
    refreshReceivedValue();
  }

  Future<void> refreshReceivedValue() async {
    var ordersReceived = await _orderRepository.getOrdersReceived(
      routes: routesId,
      date: DateTime.now(),
    );
    receivedValue.value = 0.0;
    receivedValueDinheiro.value = 0.0;
    receivedValueCartao.value = 0.0;
    receivedValuePix.value = 0.0;

    ordersDinheiro.clear();
    ordersCartao.clear();
    ordersPix.clear();

    for (var orderTemp in ordersReceived) {
      for (var payment in orderTemp.payments) {
        if (payment.userId != null &&
            payment.userId == user.value!.id &&
            payment.paymentMethod != null) {
          if (DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
            receivedValue.value += payment.value;
            if (payment.paymentMethod == PaymentMethod.dinheiro) {
              receivedValueDinheiro.value += payment.value;
              if (!ordersDinheiro.map((e) => e.value).contains(orderTemp)) {
                ordersDinheiro.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.cartao) {
              receivedValueCartao.value += payment.value;
              if (!ordersCartao.map((e) => e.value).contains(orderTemp)) {
                ordersCartao.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.pix) {
              receivedValuePix.value += payment.value;
              if (!ordersPix.map((e) => e.value).contains(orderTemp)) {
                ordersPix.add(MapEntry(payment.date, orderTemp));
              }
            }
          }
        }
      }
    }

    ordersDinheiro.sort((a, b) => a.key.compareTo(b.key));
    ordersCartao.sort((a, b) => a.key.compareTo(b.key));
    ordersPix.sort((a, b) => a.key.compareTo(b.key));
  }

  Future<void> goToClientDetails(ClientModel client) async {
    await Get.toNamed('/client_details', arguments: client);
    await refreshOrders();
  }

  Future<void> goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    await refreshOrders();
  }

  void goToVendasAnteriores() async {
    List<SaleRouteModel> routes = [];
    var salesRoutesService = Get.find<SalesRoutesService>();
    for (var routeId in routesId) {
      SaleRouteModel? rota = salesRoutesService.getRouteById(routeId);
      if (rota != null) {
        routes.add(rota);
      }
    }
    if (routes.length == 1) {
      await Get.toNamed('/sales_from_route', arguments: routes[0]);
    } else {
      Get.dialog(
        AlertDialog(
          title: const Text('Selecione a rota'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: routes
                .map(
                  (route) => ListTile(
                    title: Text(route.name),
                    onTap: () {
                      Get.back();
                      Get.toNamed('/sales_from_route', arguments: route);
                    },
                  ),
                )
                .toList(),
          ),
        ),
      );
    }
  }

  void goToClientesRota() async {
    List<SaleRouteModel> routes = [];
    var salesRoutesService = Get.find<SalesRoutesService>();
    for (var routeId in routesId) {
      SaleRouteModel? rota = salesRoutesService.getRouteById(routeId);
      if (rota != null) {
        routes.add(rota);
      }
    }
    if (routes.length == 1) {
      await Get.toNamed('/clients', arguments: routes[0].id);
    } else {
      Get.dialog(
        AlertDialog(
          title: const Text('Selecione a rota'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: routes
                .map(
                  (route) => ListTile(
                    title: Text(route.name),
                    onTap: () {
                      Get.back();
                      Get.toNamed('/clients', arguments: route.id);
                    },
                  ),
                )
                .toList(),
          ),
        ),
      );
    }
  }

  refreshLocation() async {
    location = await Get.find<GeolocationService>().getCurrentLocation();
  }

  goToMap() {
    Get.toNamed('/non_cobrador_map', arguments: routesId);
  }

  void onClientTap(OrderModel order) async {
    var orders = groupedOrders[order.clientId]!;
    if (orders.length > 1) {
      var client = await _clientRepository.getById(order.clientId);
      if (client != null) {
        goToClientDetails(client);
        return;
      }
    }
    goToOrderDetails(order);
  }
}
