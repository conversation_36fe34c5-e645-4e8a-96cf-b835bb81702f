import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'billing_other_month_controller.dart';

class BillingOtherMonthPage extends GetView<BillingOtherMonthController> {
  const BillingOtherMonthPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marcadas para Outro Mês'),
        backgroundColor: Colors.amber,
      ),
      body: Obx(
        () => ListView.builder(
          itemCount: controller.orders.length,
          itemBuilder: (context, index) {
            final order = controller.orders[index];
            return BillingOrderListTile(
              order: order,
              showDayMarking: true,
              onTap: (order) => controller.goToOrderDetails(order),
            );
          },
        ),
      ),
    );
  }
}
