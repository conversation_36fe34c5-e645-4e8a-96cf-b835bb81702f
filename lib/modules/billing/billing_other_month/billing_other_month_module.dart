import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/billing/billing_other_month/billing_other_month_bindings.dart';
import 'package:fl_app/modules/billing/billing_other_month/billing_other_month_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class BillingOtherMonthModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/billing_other_month',
      page: () => const BillingOtherMonthPage(),
      binding: BillingOtherMonthBindings(),
    ),
  ];
}
