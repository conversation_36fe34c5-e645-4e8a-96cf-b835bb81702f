import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/billing/billing_after/billing_after_bindings.dart';
import 'package:fl_app/modules/billing/billing_after/billing_after_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class BillingAfterModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/billing_after',
      page: () => const BillingAfterPage(),
      binding: BillingAfterBindings(),
    ),
  ];
}
