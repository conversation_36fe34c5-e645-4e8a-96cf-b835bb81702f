import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './billing_after_controller.dart';

class BillingAfterPage extends GetView<BillingAfterController> {
  const BillingAfterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marcadas para Depois',
            style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Obx(
        () => RefreshIndicator(
          onRefresh: () async {
            controller.refreshOrders();
          },
          child: ListView.builder(
            itemCount: controller.orders.length,
            itemBuilder: (context, index) {
              final order = controller.orders[index];
              return BillingOrderListTile(
                order: order,
                showDayMarking: true,
                onTap: (order) => controller.goToOrderDetails(order),
              );
            },
          ),
        ),
      ),
    );
  }
}
