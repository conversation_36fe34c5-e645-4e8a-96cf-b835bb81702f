import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';

class BillingAfterController extends GetxController {
  final OrderRepository _orderRepository;
  final UserService _userService;

  BillingAfterController({
    required OrderRepository orderRepository,
    required UserService userService,
  })  : _orderRepository = orderRepository,
        _userService = userService;

  UserModel? user;
  bool get isUserCobrador => user?.cobrador ?? false;
  List<String> cobradorRoutes = [];
  RxBool isLoading = false.obs;

  RxList<OrderModel> orders = <OrderModel>[].obs;

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    user = await _userService.getUserAuthenticated();
    if (isUserCobrador) {
      cobradorRoutes = user!.authorizedBillingRoutes;
    }
    refreshOrders();
  }

  void refreshOrders() async {
    isLoading(true);
    final markedOrders = await _orderRepository.getMarkedOrders();
    markedOrders.removeWhere((order) =>
        !cobradorRoutes.contains(order.routeId!) ||
        !order.dayMarkingItems.last.after);

    markedOrders.removeWhere((order) =>
        order.dayMarkingItems.last.after &&
        order.dayMarkingItems.last.createdAt
            .isBefore(DateTime.now().subtract(const Duration(days: 30))));

    orders.assignAll(markedOrders);

    isLoading(false);
  }

  goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    refreshOrders();
  }
}
