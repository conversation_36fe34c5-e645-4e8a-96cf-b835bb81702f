import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'billing_pix_controller.dart';

class BillingPixPage extends GetView<BillingPixController> {
  const BillingPixPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(FontAwesomeIcons.pix),
            SizedBox(width: 8),
            Text('Vai fazer PIX', style: TextStyle(color: Colors.white)),
          ],
        ),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Obx(
        () => RefreshIndicator(
          onRefresh: () async {
            controller.refreshOrders();
          },
          child: ListView.builder(
            itemCount: controller.orders.length,
            itemBuilder: (context, index) {
              final order = controller.orders[index];
              return BillingOrderListTile(
                order: order,
                showDayMarking: true,
                onTap: (order) => controller.goToOrderDetails(order),
              );
            },
          ),
        ),
      ),
    );
  }
}
