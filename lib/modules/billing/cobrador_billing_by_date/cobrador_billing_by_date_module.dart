import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/billing/cobrador_billing_by_date/cobrador_billing_by_date_bindings.dart';
import 'package:fl_app/modules/billing/cobrador_billing_by_date/cobrador_billing_by_date_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class CobradorBillingByDateModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/cobrador_billing_by_date',
      page: () => const CobradorBillingByDatePage(),
      binding: CobradorBillingByDateBindings(),
    ),
  ];
}
