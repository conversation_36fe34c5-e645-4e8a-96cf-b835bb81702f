import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';

class CobradorBillingByDateController extends GetxController {
  final OrderRepository _orderRepository;
  final UserService _userService;

  CobradorBillingByDateController({
    required OrderRepository orderRepository,
    required UserService userService,
  })  : _orderRepository = orderRepository,
        _userService = userService;

  UserModel? user;

  List<String> cobradorRoutes = [];

  RxBool isLoading = false.obs;

  Rx<DateTime> selectedDay = DateTime.now().obs;

  RxList<OrderModel> orders = <OrderModel>[].obs;

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    user = await _userService.getUserAuthenticated();
    cobradorRoutes = user!.authorizedBillingRoutes;
    refreshOrders();
  }

  void setSelectedDay(DateTime day) {
    selectedDay.value = day;
    refreshOrders();
  }

  void refreshOrders() async {
    isLoading(true);
    List<OrderModel> markedOrders = await _orderRepository.getMarkedOrders();

    markedOrders.removeWhere((order) =>
        !cobradorRoutes.contains(order.routeId!) ||
        order.dayMarkingItems.last.after ||
        order.dayMarkingItems.last.otherMonth ||
        order.dayMarkingItems.last.vaiFazerPix ||
        order.dayMarkingItems.last.vaiLigar ||
        (order.dayMarkingItems.last.dayToVisit != null &&
            !DateTimeHelper.isSameDay(
                order.dayMarkingItems.last.dayToVisit!, selectedDay.value)));

    markedOrders.sort((a, b) => a.date.compareTo(b.date) * -1);

    orders.assignAll(markedOrders);
    isLoading(false);
  }

  Future<void> goToClientDetails(ClientModel client) async {
    await Get.toNamed('/client_details', arguments: client);
    refreshOrders();
  }

  void goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    refreshOrders();
  }

  void goToToday() {
    selectedDay(DateTime.now());
    refreshOrders();
  }
}
