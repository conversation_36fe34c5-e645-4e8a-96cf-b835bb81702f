import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:weekly_date_picker/weekly_date_picker.dart';
import 'cobrador_billing_by_date_controller.dart';

class CobradorBillingByDatePage
    extends GetView<CobradorBillingByDateController> {
  const CobradorBillingByDatePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('Pedidos Marcados por Data'),
          actions: [
            IconButton(
              onPressed: () {
                controller.goToToday();
              },
              icon: const Icon(FontAwesomeIcons.calendarDay,
                  color: Colors.indigo),
            ),
          ],
        ),
        body: Column(
          children: <Widget>[
            Obx(
              () => WeeklyDatePicker(
                enableWeeknumberText: false,
                selectedDigitBackgroundColor: Colors.indigo,
                weekdays: const [
                  'Seg',
                  'Ter',
                  'Qua',
                  'Qui',
                  'Sex',
                  'Sab',
                  'Dom'
                ],
                selectedDay: controller.selectedDay.value,
                changeDay: (value) {
                  controller.setSelectedDay(value);
                },
              ),
            ),
            Obx(
              () => Padding(
                padding: const EdgeInsets.all(8),
                child: Text(
                  DateTimeHelper.isSameDay(
                          controller.selectedDay.value, DateTime.now())
                      ? 'Marcados para Hoje'
                      : 'Marcados para ${DateTimeHelper.getFormattedDate(controller.selectedDay.value)}',
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Obx(
                () => controller.isLoading.value
                    ? const Center(
                        child: CircularProgressIndicator(
                          strokeCap: StrokeCap.round,
                        ),
                      )
                    : controller.orders.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Text(
                                    'Nenhum pedido marcado para este dia.'),
                                const SizedBox(height: 8),
                                ElevatedButton(
                                  onPressed: () async {
                                    controller.isLoading(true);
                                    await Future.delayed(
                                        const Duration(milliseconds: 500));
                                    controller.refreshOrders();
                                  },
                                  child: const Text('Atualizar'),
                                ),
                              ],
                            ),
                          )
                        : RefreshIndicator(
                            onRefresh: () async {
                              controller.refreshOrders();
                            },
                            child: ListView.builder(
                              itemCount: controller.orders.length,
                              itemBuilder: (context, index) {
                                final order = controller.orders[index];
                                return BillingOrderListTile(
                                  order: order,
                                  onTap: (orderModel) {
                                    controller.goToOrderDetails(orderModel);
                                  },
                                );
                              },
                            ),
                          ),
              ),
            ),
          ],
        ));
  }
}
