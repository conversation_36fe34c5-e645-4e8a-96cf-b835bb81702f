import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/widgets/order_days_from_now.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/day_making_item.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';

// Exemplo de método utilitário para obter o status
String getOrderStatus(DayMarkingItem marking) {
  final agora = DateTime.now();
  String status = '';

  if (marking.dayToVisit != null) {
    final visit = marking.dayToVisit!;
    // Comparar apenas ano, mês e dia
    if (visit.year == agora.year &&
        visit.month == agora.month &&
        visit.day == agora.day) {
      status = "Hoje";
    } else {
      status = DateTimeHelper.getFormattedDate(visit);
    }
  } else if (marking.after) {
    status = "Depois";
  }
  if (marking.otherMonth) {
    status = "Outro Mês";
  }
  // Indicadores de ação
  if (marking.vaiLigar) {
    status = "Vai ligar";
  }
  if (marking.vaiFazerPix) {
    status = "Vai fazer Pix";
  }
  return status;
}

class BillingOrderListTile extends StatelessWidget {
  const BillingOrderListTile({
    super.key,
    required this.order,
    required this.onTap,
    this.location,
    this.showRoute = false,
    this.bottomWidgets,
    this.showDayMarking = false,
  });

  final OrderModel order;
  final Position? location;
  final Function(OrderModel) onTap;
  final bool showRoute;
  final bool showDayMarking;
  final List<Widget>? bottomWidgets;

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListTile(
        leading: _buildLeading(),
        title: _buildTitle(),
        subtitle: _buildSubtitle(),
        onTap: () => onTap(order),
        trailing: _buildTrailing(),
      ),
    );
  }

  Widget _buildLeading() {
    if (showDayMarking && order.activeDayMarkingItem != null) {
      DateTime now = DateTime.now();
      if (order.activeDayMarkingItem!.otherMonth &&
          now.difference(order.activeDayMarkingItem!.createdAt).inDays > 31) {
        return OrderDaysFromNow(order: order);
      }
      return _buildDayMarking();
    }

    return OrderDaysFromNow(order: order);
  }

  Widget _buildDayMarking() {
    final DayMarkingItem? marking = order.activeDayMarkingItem;
    if (marking == null) {
      return const SizedBox.shrink();
    }

    Color color = _getColor(marking);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: color, width: 1),
            borderRadius: BorderRadius.circular(5),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          child: Text(
            getOrderStatus(marking),
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Color _getColor(DayMarkingItem marking) {
    if (marking.dayToVisit != null) {
      //Hoje
      DateTime now = DateTime.now();
      if (marking.dayToVisit!.year == now.year &&
          marking.dayToVisit!.month == now.month &&
          marking.dayToVisit!.day == now.day) {
        return Colors.green;
      }

      // Futuro
      if (marking.dayToVisit!.isAfter(now)) {
        return Colors.indigo;
      }

      // Passado
      return Colors.deepOrange;
    } else if (marking.after) {
      return Colors.blue;
    } else if (marking.otherMonth) {
      return Colors.amber[700]!;
    } else if (marking.vaiLigar) {
      return Colors.red;
    } else if (marking.vaiFazerPix) {
      return Colors.deepPurple;
    } else {
      return Colors.grey;
    }
  }

  Widget _buildTitle() {
    return Text(
      order.clientName,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSubtitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildAddressText(),
        if (_shouldShowRoute) _buildRouteText(),
        if (_hasObservation) _buildObservationText(),
        if (bottomWidgets != null) ...bottomWidgets!,
      ],
    );
  }

  Widget _buildAddressText() {
    final hasNumber =
        order.clientNumber != null && order.clientNumber!.isNotEmpty;
    return Text(
      '${order.clientAddress},${hasNumber ? ' ${order.clientNumber},' : ''} ${order.clientLocalDescription}',
    );
  }

  Widget _buildRouteText() {
    return Text(
      order.routeName!,
    );
  }

  Widget _buildObservationText() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.indigo[900]!),
        borderRadius: BorderRadius.circular(5),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Text(
        'Obs: ${order.dayMarkingItems.last.observation}',
        style: TextStyle(
          color: Colors.indigo[900],
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTrailing() {
    if (order.clientLatitude == null || order.clientLongitude == null) {
      return Icon(Icons.location_off, color: Colors.red[300]!);
    }

    if (location != null) {
      return Text(
        '${order.distanceTo(location)!.toInt()}m',
        style: const TextStyle(
          fontSize: 12,
        ),
      );
    }

    return const SizedBox.shrink();
  }

  bool get _shouldShowRoute =>
      order.routeName != null && order.routeName!.isNotEmpty && showRoute;

  bool get _hasObservation =>
      order.dayMarkingItems.isNotEmpty &&
      order.dayMarkingItems.last.active &&
      order.dayMarkingItems.last.observation.isNotEmpty;
}
