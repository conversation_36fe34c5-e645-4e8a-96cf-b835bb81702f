import 'package:fl_app/application/ui/widgets/valor_recebido_tile.dart';
import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './billing_controller.dart';

class BillingPage extends GetView<BillingController> {
  const BillingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final showSearch = false.obs;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Cobranças'),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: ElevatedButton.icon(
              onPressed: () {
                controller.goToPendingOrdersMap();
              },
              label: const Text('Mapa'),
              icon: const Icon(Icons.map),
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: const BorderSide(color: Colors.indigo),
                ),
              ),
            ),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.goToBillingAfter,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[600],
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Depois'),
                  ),
                ),
                const SizedBox(width: 2),
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.goToBillingPix,
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: Colors.deepPurple,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(FontAwesomeIcons.pix),
                        SizedBox(width: 8),
                        Text('PIX'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 2),
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.goToBillingVaiLigar,
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: Colors.red,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.call),
                        SizedBox(width: 2),
                        Text('Vai ligar'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Obx(
                    () => SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          controller.goToBillingByDate();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                        ),
                        child: Text(
                          'Marcados para hoje: ${controller.markedToday.value}',
                          style: const TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: ElevatedButton(
                    onPressed: controller.goToBillingOtherMonth,
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.black,
                      backgroundColor: Colors.amber,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Outro Mês'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 1),
            Obx(
              () => ValorRecebidoTile(
                receivedValue: controller.receivedValue.value,
                receivedValueDinheiro: controller.receivedValueDinheiro.value,
                receivedValueCartao: controller.receivedValueCartao.value,
                receivedValuePix: controller.receivedValuePix.value,
                ordersDinheiro:
                    controller.ordersDinheiro.map((e) => e.value).toList(),
                ordersCartao:
                    controller.ordersCartao.map((e) => e.value).toList(),
                ordersPix: controller.ordersPix.map((e) => e.value).toList(),
              ),
            ),
            const SizedBox(height: 8),
            Obx(
              () => Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Pendentes (${controller.groupedOrders.entries.where((e) => !controller.clientsArchive.contains(e.key)).fold(0, (previousValue, element) => previousValue + element.value.length)})',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.start,
                  ),
                  //dropdown para filtrar por rotas vem selecionado com Todos e com o nome das rotas autorizadas
                  Obx(() {
                    List<DropdownMenuItem> drops = [];
                    //add Todos
                    drops.add(const DropdownMenuItem(
                      value: '',
                      child: Text('Todas as rotas'),
                    ));
                    drops.addAll(controller.authorizedBillingRoutes
                        .map((element) => DropdownMenuItem(
                              value: element.id,
                              child: Text(
                                element.name,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ))
                        .toList());
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      height: 45,
                      alignment: Alignment.center,
                      child: DropdownButton(
                        items: drops,
                        onChanged: (value) {
                          controller.filterByRoute(value);
                        },
                        value: controller.selectedRoute.value,
                        hint: const Text('Selecione uma rota'),
                        underline: Container(),
                      ),
                    );
                  }),
                ],
              ),
            ),
            Obx(
              () => Visibility(
                visible: !showSearch.value,
                child: Row(
                  children: [
                    if (controller.groupedOrders.isNotEmpty &&
                        controller.clientsArchive.isNotEmpty)
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            controller.openModalOrdersArchive();
                          },
                          label: Text(
                              'Clientes separados (${controller.clientsArchive.length})'),
                          icon: const Icon(Icons.visibility_off),
                          style: ElevatedButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                  color:
                                      Theme.of(context).colorScheme.secondary),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: Obx(
                () {
                  if (controller.isLoading.value) {
                    return const Center(
                      child: CircularProgressIndicator(
                        strokeCap: StrokeCap.round,
                      ),
                    );
                  }

                  if (controller.groupedOrders.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text('Nenhum pedido pendente.'),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: () async {
                              controller.isLoading(true);
                              await Future.delayed(
                                  const Duration(milliseconds: 500));
                              controller.refreshOrders();
                            },
                            child: const Text('Atualizar'),
                          ),
                        ],
                      ),
                    );
                  }

                  final groupedEntries = controller.groupedOrders.entries
                      .where((e) => e.value.any((order) =>
                          order.clientName.toLowerCase().contains(
                              controller.search.value.toLowerCase()) ||
                          order.clientAddress.toLowerCase().contains(
                              controller.search.value.toLowerCase()) ||
                          order.clientLocalDescription
                              .toLowerCase()
                              .contains(controller.search.value.toLowerCase())))
                      .toList();

                  return RefreshIndicator(
                    onRefresh: () async {
                      controller.refreshOrders();
                    },
                    child: ListView.builder(
                      itemCount: groupedEntries
                          .where(
                              (e) => !controller.clientsArchive.contains(e.key))
                          .length,
                      itemBuilder: (context, index) {
                        final orders = groupedEntries
                            .where((e) =>
                                !controller.clientsArchive.contains(e.key))
                            .toList()[index]
                            .value;
                        final order = orders.first;
                        return Slidable(
                          closeOnScroll: true,
                          startActionPane: ActionPane(
                            motion: const ScrollMotion(),
                            children: [
                              SlidableAction(
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(10),
                                  topLeft: Radius.circular(10),
                                ),
                                icon: Icons.visibility_off,
                                label: 'Separar',
                                onPressed: (context) {
                                  controller
                                      .markClientToNotShow(order.clientId);
                                },
                                backgroundColor:
                                    Theme.of(context).colorScheme.secondary,
                                foregroundColor: Colors.white,
                              ),
                            ],
                          ),
                          child: BillingOrderListTile(
                            order: order,
                            onTap: (orderModel) =>
                                controller.onClientTap(orderModel),
                            location: controller.location,
                            showRoute: controller.cobradorRoutes.isNotEmpty &&
                                controller.cobradorRoutes.length > 1,
                            showDayMarking: true,
                            bottomWidgets: orders.length > 1
                                ? [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary
                                            .withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      margin: const EdgeInsets.only(top: 4),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            '${orders.length} pedidos',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodySmall
                                                ?.copyWith(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .primary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                : null,
                          ),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
