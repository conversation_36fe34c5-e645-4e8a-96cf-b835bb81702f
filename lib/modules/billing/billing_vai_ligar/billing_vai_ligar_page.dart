import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'billing_vai_ligar_controller.dart';

class BillingVaiLigarPage extends GetView<BillingVaiLigarController> {
  const BillingVaiLigarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(FontAwesomeIcons.phone),
            SizedBox(width: 8),
            Text('Vai Ligar',
                style: TextStyle(fontSize: 20, color: Colors.white)),
          ],
        ),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Obx(
        () => RefreshIndicator(
          onRefresh: () async {
            controller.refreshOrders();
          },
          child: ListView.builder(
            itemCount: controller.orders.length,
            itemBuilder: (context, index) {
              final order = controller.orders[index];
              return BillingOrderListTile(
                order: order,
                showDayMarking: true,
                onTap: (order) => controller.goToOrderDetails(order),
              );
            },
          ),
        ),
      ),
    );
  }
}
