import 'dart:async';
import 'dart:developer';

import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/modules/billing/widgets/billing_order_list_tile.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';

class BillingController extends GetxController {
  final OrderRepository _orderRepository;
  final UserService _userService;
  final ClientRepository _clientRepository;

  BillingController({
    required OrderRepository orderRepository,
    required UserService userService,
    required ClientRepository clientRepository,
  })  : _orderRepository = orderRepository,
        _clientRepository = clientRepository,
        _userService = userService;

  UserModel? user;
  List<String> cobradorRoutes = [];

  RxList<ClientModel> allClientsInRoutes = <ClientModel>[].obs;

  RxInt markedToday = 0.obs;

  RxBool isLoading = false.obs;

  RxList<String> ordersArchive = <String>[].obs;

  Position? location;

  RxList<SaleRouteModel> authorizedBillingRoutes = <SaleRouteModel>[].obs;

  RxString selectedRoute = ''.obs;

  Box? boxArchive;

  RxDouble receivedValue = 0.0.obs;
  RxDouble receivedValueDinheiro = 0.0.obs;
  RxDouble receivedValueCartao = 0.0.obs;
  RxDouble receivedValuePix = 0.0.obs;

  RxList<MapEntry<DateTime, OrderModel>> ordersDinheiro =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersCartao =
      <MapEntry<DateTime, OrderModel>>[].obs;
  RxList<MapEntry<DateTime, OrderModel>> ordersPix =
      <MapEntry<DateTime, OrderModel>>[].obs;

  RxMap<String, List<OrderModel>> groupedOrders = RxMap({});

  RxList<String> clientsArchive = <String>[].obs;

  RxString search = ''.obs;

  @override
  void onInit() {
    init();
    super.onInit();
  }

  @override
  void onClose() {
    stopTimer();
    super.onClose();
  }

  Timer? _timer;

  void startTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer.periodic(const Duration(seconds: 10), (timer) {
      refreshOrders();
    });
  }

  void stopTimer() {
    _timer?.cancel();
  }

  void init() async {
    boxArchive = await Hive.openBox('billingOrdersArchive');
    var dateArchive = boxArchive!.get('dateArchive');
    if (dateArchive == null) {
      await boxArchive!.clear();
      await boxArchive!.put('dateArchive', DateTime.now());
    } else if (dateArchive != null &&
        !DateTimeHelper.isSameDay(dateArchive, DateTime.now())) {
      await boxArchive!.clear();
      await boxArchive!.put('dateArchive', DateTime.now());
    } else {
      clientsArchive.value =
          boxArchive!.get('clientsArchive', defaultValue: <String>[]);
    }

    log('ordersArchive: $ordersArchive');
    user = await _userService.getUserAuthenticated();
    cobradorRoutes = user!.authorizedBillingRoutes;
    for (var route in cobradorRoutes) {
      SaleRouteModel? rota = Get.find<SalesRoutesService>().getRouteById(route);
      if (rota != null) {
        authorizedBillingRoutes.add(rota);
      }
    }
    refreshClientsInRoutes();
    refreshOrders();
    startTimer();
  }

  void refreshClientsInRoutes() async {
    List<ClientModel> clients = [];
    for (var routeId in cobradorRoutes) {
      clients.addAll(await _clientRepository.getClientsFromCache(routeId));
    }
    allClientsInRoutes.assignAll(clients);
  }

  Future<void> refreshOrders() async {
    isLoading(true);
    await refreshLocation();
    List<OrderModel> markedOrders = await _orderRepository
        .getPendingOrdersToCobradorFixo(cobradorRoutes, selectedRoute.value);

    markedToday.value =
        (await _orderRepository.getMarkedToTodayOrders(cobradorRoutes)).length;
    final Map<String, List<OrderModel>> map = {};

    for (var order in markedOrders) {
      map.putIfAbsent(order.clientId, () => []).add(order);
    }
    groupedOrders.assignAll(map);
    refreshReceivedValue();
    isLoading(false);
  }

  Future<void> refreshReceivedValue() async {
    var ordersReceived = await _orderRepository.getOrdersReceived(
      routes: cobradorRoutes,
      date: DateTime.now(),
    );
    receivedValue.value = 0.0;
    receivedValueDinheiro.value = 0.0;
    receivedValueCartao.value = 0.0;
    receivedValuePix.value = 0.0;

    ordersDinheiro.clear();
    ordersCartao.clear();
    ordersPix.clear();

    for (var orderTemp in ordersReceived) {
      for (var payment in orderTemp.payments) {
        if (payment.userId != null &&
            payment.userId == user!.id &&
            payment.paymentMethod != null) {
          if (DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
            receivedValue.value += payment.value;
            if (payment.paymentMethod == PaymentMethod.dinheiro) {
              receivedValueDinheiro.value += payment.value;
              if (!ordersDinheiro.map((e) => e.value).contains(orderTemp)) {
                ordersDinheiro.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.cartao) {
              receivedValueCartao.value += payment.value;
              if (!ordersCartao.map((e) => e.value).contains(orderTemp)) {
                ordersCartao.add(MapEntry(payment.date, orderTemp));
              }
            } else if (payment.paymentMethod == PaymentMethod.pix) {
              receivedValuePix.value += payment.value;
              if (!ordersPix.map((e) => e.value).contains(orderTemp)) {
                ordersPix.add(MapEntry(payment.date, orderTemp));
              }
            }
          }
        }
      }
    }

    ordersDinheiro.sort((a, b) => a.key.compareTo(b.key));
    ordersCartao.sort((a, b) => a.key.compareTo(b.key));
    ordersPix.sort((a, b) => a.key.compareTo(b.key));
  }

  Future<void> goToClientDetails(ClientModel client) async {
    await Get.toNamed('/client_details', arguments: client);
    refreshOrders();
  }

  Future<void> goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    await refreshOrders();
  }

  void goToBillingAfter() async {
    await Get.toNamed('/billing_after');
    refreshOrders();
  }

  void goToBillingOtherMonth() async {
    await Get.toNamed('/billing_other_month');
    refreshOrders();
  }

  void goToBillingPix() async {
    await Get.toNamed('/billing_pix');
    refreshOrders();
  }

  void goToBillingVaiLigar() async {
    await Get.toNamed('/billing_vai_ligar');
    refreshOrders();
  }

  void goToBillingByDate() async {
    Get.toNamed('/cobrador_billing_by_date');
    refreshOrders();
  }

  refreshLocation() async {
    location = await Get.find<GeolocationService>().getCurrentLocation();
  }

  void filterByRoute(value) {
    selectedRoute.value = value;
    refreshOrders();
  }

  void markOrderToNotShow(String? id) {
    if (id != null) {
      ordersArchive.add(id);
      boxArchive!.put('ordersArchive', ordersArchive.toList());
      log('ordersArchive: $ordersArchive');
    }
  }

  void markOrderToShow(String? id) {
    if (id != null) {
      ordersArchive.remove(id);
      boxArchive!.put('ordersArchive', ordersArchive.toList());
      log('ordersArchive: $ordersArchive');
    }
  }

  void openModalOrdersArchive() async {
    // open bottom sheet with ordersArchive
    await Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          border: Border(
            top: BorderSide(
              color: Get.theme.colorScheme.primary.withOpacity(0.8),
              width: 3,
            ),
          ),
        ),
        child: Column(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Text(
                'Clientes separados:',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: Obx(
                () => ListView.builder(
                  itemCount: groupedOrders.entries
                      .where((e) => clientsArchive.contains(e.key))
                      .length,
                  itemBuilder: (context, index) {
                    var clientGroup = groupedOrders.entries
                        .where((e) => clientsArchive.contains(e.key))
                        .toList()[index];
                    var order = clientGroup.value.first;
                    return Column(
                      children: [
                        Slidable(
                          closeOnScroll: true,
                          startActionPane: ActionPane(
                            motion: const ScrollMotion(),
                            children: [
                              SlidableAction(
                                borderRadius: const BorderRadius.only(
                                  bottomLeft: Radius.circular(10),
                                  topLeft: Radius.circular(10),
                                ),
                                icon: Icons.visibility,
                                label: 'Mostrar',
                                onPressed: (context) {
                                  markClientToShow(order.clientId);
                                  if (groupedOrders.entries
                                      .where(
                                          (e) => clientsArchive.contains(e.key))
                                      .isEmpty) {
                                    Get.back();
                                  }
                                },
                                backgroundColor:
                                    Theme.of(context).colorScheme.secondary,
                                foregroundColor: Colors.white,
                              ),
                            ],
                          ),
                          child: BillingOrderListTile(
                            order: order,
                            onTap: (orderModel) => onClientTap(orderModel),
                            location: location,
                            showRoute: cobradorRoutes.isNotEmpty &&
                                cobradorRoutes.length > 1,
                            bottomWidgets: clientGroup.value.length > 1
                                ? [
                                    Container(
                                      decoration: BoxDecoration(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary
                                            .withOpacity(0.2),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      margin: const EdgeInsets.only(top: 4),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            '${clientGroup.value.length} pedidos',
                                            style: Theme.of(context)
                                                .textTheme
                                                .bodySmall
                                                ?.copyWith(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .primary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ]
                                : null,
                          ),
                        )
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void markClientToNotShow(String? clientId) {
    if (clientId != null) {
      clientsArchive.add(clientId);
      boxArchive!.put('clientsArchive', clientsArchive.toList());
      log('clientsArchive: $clientsArchive');
    }
  }

  void markClientToShow(String? clientId) {
    if (clientId != null) {
      clientsArchive.remove(clientId);
      boxArchive!.put('clientsArchive', clientsArchive.toList());
      log('clientsArchive: $clientsArchive');
    }
  }

  void onClientTap(OrderModel order) async {
    var orders = groupedOrders[order.clientId]!;
    if (orders.length > 1) {
      var client = await _clientRepository.getById(order.clientId);
      if (client != null) {
        goToClientDetails(client);
        return;
      }
    }
    goToOrderDetails(order);
  }

  Future<void> goToPendingOrdersMap() async {
    await Get.toNamed('/billing-pendings-map');
  }
}
