import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/billing/billing_after/billing_after_module.dart';
import 'package:fl_app/modules/billing/billing_bindings.dart';
import 'package:fl_app/modules/billing/billing_other_month/billing_other_month_module.dart';
import 'package:fl_app/modules/billing/billing_pendings_map/billing_pendings_map_module.dart';
import 'package:fl_app/modules/billing/billing_pix/billing_pix_module.dart';
import 'package:fl_app/modules/billing/billing_vai_ligar/billing_vai_ligar_module.dart';
import 'package:fl_app/modules/billing/cobrador_billing_by_date/cobrador_billing_by_date_module.dart';
import 'package:fl_app/modules/billing/billing_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class BillingModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/billing',
      page: () => const BillingPage(),
      binding: BillingBindings(),
    ),
    ...BillingAfterModule().routers,
    ...BillingOtherMonthModule().routers,
    ...CobradorBillingByDateModule().routers,
    ...BillingPixModule().routers,
    ...BillingVaiLigarModule().routers,
    ...BillingPendingsMapModule().routers,
  ];
}
