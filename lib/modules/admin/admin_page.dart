import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'admin_controller.dart';

class AdminPage extends GetView<AdminController> {
  const AdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin'),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          Card(
            child: ListTile(
              title: const Text('Pedidos com erro de comissão'),
              leading: const Icon(Icons.error),
              onTap: () {
                Get.toNamed('/admin/orders-product-comission-error');
              },
            ),
          ),
        ],
      ),
    );
  }
}
