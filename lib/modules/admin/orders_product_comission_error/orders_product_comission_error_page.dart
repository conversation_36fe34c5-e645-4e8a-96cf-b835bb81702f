import 'package:fl_app/application/ui/widgets/order_list_card.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'orders_product_comission_error_controller.dart';

class OrdersProductComissionErrorPage
    extends GetView<OrdersProductComissionErrorController> {
  const OrdersProductComissionErrorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Remover pedidos com comissão'),
      ),
      body: Obx(() {
        final orders = controller.orders;
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              child: SearchBar(
                controller: controller.searchController,
                hintText: 'Pesquisar produto com comissão',
                onChanged: (value) => controller.search(value),
                elevation: WidgetStateProperty.all(2),
                trailing: [
                  if (controller.search.isNotEmpty)
                    IconButton(
                      onPressed: () => controller.clearSearch(),
                      icon: const Icon(Icons.clear),
                    ),
                  IconButton(
                    onPressed: () => controller.refreshOrders(),
                    icon: const Icon(Icons.search_rounded),
                  ),
                ],
                shape: WidgetStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                focusNode: controller.searchFocus,
              ),
            ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.max,
                children: [
                  if (orders.isEmpty && controller.isLoading.value)
                    const Center(
                      child: CircularProgressIndicator(
                        strokeCap: StrokeCap.round,
                      ),
                    ),
                  if (orders.isEmpty && !controller.isLoading.value)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Text('Nenhum pedido encontrado'),
                            ElevatedButton(
                              onPressed: () {
                                controller.refreshOrders();
                              },
                              child: const Text('Tentar novamente'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  if (orders.isNotEmpty && !controller.isLoading.value)
                    Expanded(
                      child: RefreshIndicator(
                        onRefresh: () async {
                          await controller.refreshOrders();
                        },
                        child: Scrollbar(
                          child: ListView.builder(
                            itemCount: orders.length,
                            itemBuilder: (context, index) {
                              final order = orders[index];
                              return OrderListCard(
                                  order: order,
                                  onTap: controller.goToOrderDetails);
                            },
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            if (controller.orders.isNotEmpty)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    controller.removeCommission();
                  },
                  child: Text(
                      'Remover comissão do "${controller.search.value}" (${controller.orders.length})'),
                ),
              ),
          ],
        );
      }),
    );
  }
}
