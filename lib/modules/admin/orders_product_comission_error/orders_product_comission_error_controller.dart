import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrdersProductComissionErrorController extends GetxController {
  final RxList<OrderModel> orders = <OrderModel>[].obs;

  RxBool isLoading = false.obs;

  RxString search = ''.obs;
  TextEditingController searchController = TextEditingController();
  FocusNode searchFocus = FocusNode();

  final OrderRepository _orderRepository = Get.find<OrderRepository>();

  @override
  void onInit() {
    init();
    super.onInit();
  }

  void init() async {
    await refreshOrders();
  }

  Future<void> refreshOrders() async {
    isLoading(true);
    orders.assignAll(await _orderRepository.getIncorrectOrders(search.value));
    isLoading(false);
  }

  void goToOrderDetails(OrderModel order) async {
    await Get.toNamed('/view-order', arguments: order);
    await refreshOrders();
  }

  Future<void> removeCommission() async {
    isLoading(true);
    await _orderRepository.removeComissionFromOrders(
        orders.toList(), search.value);
    await refreshOrders();
    isLoading(false);
  }

  clearSearch() {
    search('');
    searchController.clear();
    searchFocus.unfocus();
    orders.clear();
  }
}
