import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/admin/orders_product_comission_error/orders_product_comission_error_bindings.dart';
import 'package:fl_app/modules/admin/orders_product_comission_error/orders_product_comission_error_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class OrdersProductComissionErrorModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/admin/orders-product-comission-error',
      page: () => const OrdersProductComissionErrorPage(),
      binding: OrdersProductComissionErrorBindings(),
    ),
  ];
}
