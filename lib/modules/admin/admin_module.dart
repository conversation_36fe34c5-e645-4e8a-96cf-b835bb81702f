import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/admin/admin_bindings.dart';
import 'package:fl_app/modules/admin/admin_page.dart';
import 'package:fl_app/modules/admin/orders_product_comission_error/orders_product_comission_error_module.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class AdminModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/admin',
      page: () => const AdminPage(),
      binding: AdminBindings(),
    ),
    ...OrdersProductComissionErrorModule().routers,
  ];
}
