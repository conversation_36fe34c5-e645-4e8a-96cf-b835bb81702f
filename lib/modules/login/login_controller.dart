import 'dart:developer';

import 'package:fl_app/application/ui/loader/loader_mixin.dart';
import 'package:fl_app/application/ui/messages/messages_mixin.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';

class LoginController extends GetxController with LoaderMixin, MessagesMixin {
  final UserService _userService;

  LoginController({required UserService userService})
      : _userService = userService;

  final loading = false.obs;

  final message = Rxn<MessageModel>();

  @override
  void onInit() {
    super.onInit();
    loaderListener(loading);
    messageListener(message);
  }

  Future<void> login() async {
    try {
      loading(true);
      await _userService.login();
      loading(false);
      //message(MessageModel.success(title: 'Sucesso', message: 'Login realizado com sucesso'));
      Get.offAllNamed('/splash');
    } on Exception catch (exception, stacktrace) {
      log(exception.toString());
      log(stacktrace.toString());
      loading(false);
      message(MessageModel.error(
          title: 'Erro', message: 'Erro ao realizar login com o Google'));
    }
  }
}
