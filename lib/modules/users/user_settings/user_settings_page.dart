import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './user_settings_controller.dart';

class UserSettingsPage extends GetView<UserSettingsController> {
  const UserSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Obx(
          () => GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  SizedBox(
                    height: 150,
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundImage: controller.user.image != null
                              ? NetworkImage(controller.user.image!)
                              : null,
                          radius: 50,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Column(
                            children: [
                              TextFormField(
                                controller: controller.nameController,
                                onChanged: (value) {
                                  controller.user.name = value;
                                },
                                decoration: const InputDecoration(
                                  labelText: 'Nome',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 20),
                                width: double.infinity,
                                child: Text(
                                  controller.user.email,
                                  style: const TextStyle(
                                      fontSize: 16, color: Colors.grey),
                                ),
                              ),
                              Text('Dispositivo: ${controller.user.phoneModel}')
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  CheckboxListTile(
                    visualDensity:
                        const VisualDensity(horizontal: -4, vertical: -4),
                    value: controller.authorized.value,
                    onChanged: (value) {
                      controller.setAuthorized(value!);
                    },
                    title: const Text('Autorizado'),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  if (controller.authorized.value)
                    CheckboxListTile(
                      visualDensity:
                          const VisualDensity(horizontal: -4, vertical: -4),
                      value: controller.admin.value,
                      onChanged: (value) {
                        controller.setAdmin(value!);
                      },
                      title: const Text('Administrador'),
                    ),
                  if (controller.authorized.value && !controller.admin.value)
                    CheckboxListTile(
                      visualDensity:
                          const VisualDensity(horizontal: -4, vertical: -4),
                      value: controller.cobrador.value,
                      onChanged: (value) {
                        controller.setCobrador(value!);
                      },
                      title: const Text('Cobrador'),
                    ),
                  CheckboxListTile(
                    visualDensity:
                        const VisualDensity(horizontal: -4, vertical: -4),
                    value: controller.userTest.value,
                    onChanged: (value) {
                      controller.setUserTest(value!);
                    },
                    title: const Text('Usuário de Teste'),
                  ),
                  if (controller.cobrador.value && !controller.admin.value)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 8),
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Tipo de Cobrador:',
                            style: TextStyle(fontSize: 16),
                          ),
                          const SizedBox(height: 8),
                          ToggleButtons(
                            selectedColor: Get.isDarkMode
                                ? Colors.white
                                : Theme.of(context).primaryColor,
                            selectedBorderColor: Get.isDarkMode
                                ? Colors.white
                                : Theme.of(context).primaryColor,
                            borderColor: Get.isDarkMode
                                ? Colors.white
                                : Theme.of(context).primaryColor,
                            fillColor: Colors.indigo,
                            constraints: BoxConstraints.expand(
                              width: (Get.width - 64) / 2,
                              height: 50,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            isSelected: [
                              controller.cobradorFixo.value,
                              controller.cobradorTemporario.value,
                            ],
                            onPressed: (index) {
                              if (index == 0) {
                                controller.cobradorFixo.value = true;
                                controller.cobradorTemporario.value = false;
                              } else {
                                controller.cobradorFixo.value = false;
                                controller.cobradorTemporario.value = true;
                              }
                            },
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                child: const Text('Fixo'),
                              ),
                              Container(
                                padding: const EdgeInsets.all(8),
                                child: const Text('Cobrador Temporário'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Rotas Liberadas para Cobrança:',
                            style: TextStyle(fontSize: 16),
                          ),
                          const Divider(),
                          Obx(
                            () => Column(
                              children: [
                                for (var route in controller.routes)
                                  CheckboxListTile(
                                    visualDensity: const VisualDensity(
                                        horizontal: -4, vertical: -4),
                                    contentPadding: EdgeInsets.zero,
                                    value: controller.selectedRoutes
                                        .contains(route.id),
                                    onChanged: (value) {
                                      if (value! &&
                                          !controller.selectedRoutes
                                              .contains(route.id)) {
                                        controller.selectedRoutes
                                            .add(route.id!);
                                      } else {
                                        controller.selectedRoutes
                                            .remove(route.id);
                                      }
                                    },
                                    title: Text(route.name),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        controller.save();
                      },
                      child: const Text('Salvar'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
