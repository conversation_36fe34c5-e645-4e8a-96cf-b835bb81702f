import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserSettingsController extends GetxController {
  final UserModel user = Get.arguments as UserModel;
  final UserService _userService;
  final SalesRoutesService _salesRoutesService;

  UserSettingsController({
    required UserService userService,
    required SalesRoutesService salesRoutesService,
  })  : _userService = userService,
        _salesRoutesService = salesRoutesService;

  RxList<SaleRouteModel> routes = <SaleRouteModel>[].obs;
  //selectedRoutes
  RxList<String> selectedRoutes = <String>[].obs;

  RxBool admin = false.obs;
  RxBool cobrador = false.obs;
  RxBool cobradorTemporario = true.obs;
  RxBool cobradorFixo = false.obs;
  RxBool authorized = false.obs;

  RxBool userTest = false.obs;

  TextEditingController nameController = TextEditingController();

  @override
  void onInit() {
    admin.value = user.admin;
    cobrador.value = user.cobrador;
    if (user.cobrador) {
      cobradorTemporario.value = user.cobradorTemporario;
      cobradorFixo.value = user.cobradorFixo;
    }
    authorized.value = user.authorized;
    userTest.value = user.userTest;
    nameController.text = user.name;

    refreshRoutes();
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    selectedRoutes.addAll(user.authorizedBillingRoutes);
  }

  void refreshRoutes() {
    _salesRoutesService.getSalesRoutesFromCache().then((value) {
      routes(value);
    });
  }

  void setAdmin(bool isAdmin) {
    admin.value = isAdmin;
    admin.value ? cobrador.value = false : null;
  }

  void setCobrador(bool isCobrador) {
    cobrador.value = isCobrador;
    if (!isCobrador) {
      cobradorTemporario.value = false;
      cobradorFixo.value = false;
    } else {
      cobradorTemporario.value = true;
      cobradorFixo.value = false;
    }
  }

  void setUserTest(bool isUserTest) {
    userTest.value = isUserTest;
  }

  void save() {
    if (nameController.text.isEmpty) {
      Get.snackbar('Erro', 'Nome não pode ser vazio',
          backgroundColor: Colors.red, colorText: Colors.white);
      return;
    }
    if (cobrador.value && selectedRoutes.isEmpty) {
      Get.showSnackbar(GetSnackBar(
        title: 'Erro',
        message: 'Selecione ao menos uma rota de cobrança',
        backgroundColor: Colors.red[800]!,
        duration: const Duration(seconds: 2),
      ));
      return;
    }
    user.name = nameController.text;
    user.admin = admin.value;
    user.cobrador = cobrador.value;
    user.authorized = authorized.value;
    user.cobradorFixo = cobradorFixo.value;
    user.cobradorTemporario = cobradorTemporario.value;
    user.userTest = userTest.value;
    if (cobrador.value) {
      user.authorizedBillingRoutes = selectedRoutes.toList();
    } else {
      user.authorizedBillingRoutes = [];
    }
    _userService.updateUser(user);
    Get.back(result: user);
  }

  void setAuthorized(bool bool) {
    authorized.value = bool;
    if (!bool) {
      admin.value = false;
      cobrador.value = false;
    }
  }
}
