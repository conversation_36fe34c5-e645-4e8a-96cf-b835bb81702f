import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/users/user_settings/user_settings_bindings.dart';
import 'package:fl_app/modules/users/user_settings/user_settings_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class UserSettingsModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/user_settings',
      page: () => const UserSettingsPage(),
      binding: UserSettingsBindings(),
    ),
  ];
}
