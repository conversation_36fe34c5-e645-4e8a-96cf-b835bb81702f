import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CobradorFixoChip extends StatelessWidget {
  const CobradorFixoChip({super.key});

  @override
  Widget build(BuildContext context) {
    final Color backgroundColor = Get.isDarkMode ? Colors.black : Colors.white;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(
          'Cobrador Fixo',
          style: TextStyle(
            color: Colors.orange[700]!,
            fontSize: 14,
          ),
        ),
        labelPadding: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        backgroundColor: backgroundColor,
        side: BorderSide(
          color: Colors.orange[700]!,
          width: 1,
        ),
      ),
    );
  }
}
