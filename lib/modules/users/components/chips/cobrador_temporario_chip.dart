import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CobradorTemporarioChip extends StatelessWidget {
  const CobradorTemporarioChip({super.key});

  @override
  Widget build(BuildContext context) {
    final Color backgroundColor = Get.isDarkMode ? Colors.black : Colors.white;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(
          'Cobrador Temporário',
          style: TextStyle(
            color: Colors.green[700]!,
          ),
        ),
        labelPadding: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        backgroundColor: backgroundColor,
        side: BorderSide(
          color: Colors.green[700]!,
          width: 1,
        ),
      ),
    );
  }
}
