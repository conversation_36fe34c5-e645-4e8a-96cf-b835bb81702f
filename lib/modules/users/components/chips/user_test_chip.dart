import 'package:flutter/material.dart';

class UserTestChip extends StatelessWidget {
  const UserTestChip({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(
          'TESTE',
          style: TextStyle(
            color: Colors.grey[700]!,
          ),
        ),
        labelPadding: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        backgroundColor: Colors.white,
        side: BorderSide(
          color: Colors.grey[700]!,
          width: 1,
        ),
      ),
    );
  }
}
