import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AdminChip extends StatelessWidget {
  const AdminChip({super.key});

  @override
  Widget build(BuildContext context) {
    final Color backgroundColor = Get.isDarkMode ? Colors.black : Colors.white;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(
          'ADMINISTRADOR',
          style: TextStyle(
            color: Colors.blue[700]!,
            fontSize: 14,
          ),
        ),
        labelPadding: EdgeInsets.zero,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        backgroundColor: backgroundColor,
        side: BorderSide(
          color: Colors.blue[700]!,
          width: 1,
        ),
      ),
    );
  }
}
