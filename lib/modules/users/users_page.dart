import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_app/modules/users/components/chips/admin_chip.dart';
import 'package:fl_app/modules/users/components/chips/cobrador_fixo_chip.dart';
import 'package:fl_app/modules/users/components/chips/cobrador_temporario_chip.dart';
import 'package:fl_app/modules/users/components/chips/user_test_chip.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './users_controller.dart';

class UsersPage extends GetView<UsersController> {
  const UsersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Usuários'),
        actions: [
          IconButton(
            onPressed: () {
              controller.showUsersTest();
            },
            icon: Obx(() {
              return controller.showTestUsers.value
                  ? Icon(FontAwesomeIcons.eye, color: Colors.grey[700])
                  : const Icon(FontAwesomeIcons.eyeSlash, color: Colors.grey);
            }),
            tooltip: 'Mostrar usuários de teste',
          ),
        ],
      ),
      body: Obx(() {
        return RefreshIndicator(
          onRefresh: () async {
            await controller.refreshUsers();
          },
          child: ListView.builder(
            itemCount: controller.users.length,
            itemBuilder: (context, index) {
              final user = controller.users[index];
              return ListTile(
                title: Text(user.name),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(user.email),
                    Row(
                      children: [
                        if (user.admin) const AdminChip(),
                        if (user.cobrador && user.cobradorFixo)
                          const CobradorFixoChip(),
                        if (user.cobrador && user.cobradorTemporario)
                          const CobradorTemporarioChip(),
                        if (user.userTest) const UserTestChip(),
                      ],
                    ),
                  ],
                ),
                leading: user.image != null
                    ? CircleAvatar(
                        backgroundImage:
                            CachedNetworkImageProvider(user.image!),
                      )
                    : null,
                onTap: () {
                  controller.goToUserView(user);
                },
              );
            },
          ),
        );
      }),
    );
  }
}
