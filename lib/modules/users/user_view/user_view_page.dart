import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_app/modules/users/components/chips/admin_chip.dart';
import 'package:fl_app/modules/users/components/chips/cobrador_fixo_chip.dart';
import 'package:fl_app/modules/users/components/chips/cobrador_temporario_chip.dart';
import 'package:fl_app/modules/users/components/chips/user_test_chip.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import './user_view_controller.dart';
import 'components/rotas_permitidas_widget.dart';

class UserViewPage extends GetView<UserViewController> {
  const UserViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    var user = controller.user;
    return Scaffold(
      appBar: AppBar(
        actions: [
          IconButton(
            onPressed: () {
              controller.goToUserSettings();
            },
            icon: const Icon(FontAwesomeIcons.penToSquare),
          )
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          controller.showMoreOptions();
        },
        child: const Icon(Icons.more_horiz),
      ),
      body: SizedBox(
        width: double.infinity,
        child: Obx(
          () => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CircleAvatar(
                  radius: 50,
                  child: CachedNetworkImage(
                    imageUrl: user.value.image ?? '',
                    imageBuilder: (context, imageProvider) => CircleAvatar(
                      backgroundImage: imageProvider,
                      radius: 50,
                    ),
                    placeholder: (context, url) => const CircleAvatar(
                      radius: 50,
                      child: CircularProgressIndicator(
                        strokeCap: StrokeCap.round,
                      ),
                    ),
                    errorWidget: (context, url, error) => const CircleAvatar(
                      radius: 50,
                    ),
                  ),
                ),
                Text(
                  user.value.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  user.value.email,
                  style: const TextStyle(fontSize: 16),
                ),
                Text('Dispositivo: ${user.value.phoneModel}'),
                const SizedBox(height: 8),
                Wrap(
                  children: [
                    if (user.value.admin) const AdminChip(),
                    if (user.value.cobrador && user.value.cobradorFixo)
                      const CobradorFixoChip(),
                    if (user.value.cobrador && user.value.cobradorTemporario)
                      const CobradorTemporarioChip(),
                    if (user.value.userTest) const UserTestChip(),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Vendido:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Obx(
                              () => Text(
                                'R\$ ${controller.salesTotal.value.toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          controller.showVales();
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          padding: const EdgeInsets.all(8),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'VALE:',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Obx(
                                () => Text(
                                  'R\$ ${controller.valesTotal.value.toStringAsFixed(2)}',
                                  style: const TextStyle(
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        padding: const EdgeInsets.all(8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'A Receber:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Obx(
                              () => Text(
                                'R\$ ${(controller.salesTotal.value - controller.valesTotal.value).toStringAsFixed(2)}',
                                style: const TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                if (user.value.cobrador)
                  RotasPermitidasWidget(controller: controller)
              ],
            ),
          ),
        ),
      ),
    );
  }
}
