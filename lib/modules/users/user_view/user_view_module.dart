import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/users/user_view/user_view_bindings.dart';
import 'package:fl_app/modules/users/user_view/user_view_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class UserViewModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/user-view',
      page: () => const UserViewPage(),
      binding: UserViewBindings(),
    ),
  ];
}
