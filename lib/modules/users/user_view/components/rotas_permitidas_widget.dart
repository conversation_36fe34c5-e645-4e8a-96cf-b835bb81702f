import 'package:fl_app/modules/users/user_view/user_view_controller.dart';
import 'package:flutter/material.dart';

class RotasPermitidasWidget extends StatelessWidget {
  const RotasPermitidasWidget({
    super.key,
    required this.controller,
  });

  final UserViewController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Rotas permitidas:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            controller.getAuthorizedRoutes(),
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }
}
