import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/ui/loader/loader_mixin.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/salary_advance.dart';
import 'package:fl_app/models/user_model.dart';

import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:fl_app/services/user/salary_advance/salary_advance_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get/get.dart';

class UserViewController extends GetxController with LoaderMixin {
  Rx<UserModel> user = Rx<UserModel>(Get.arguments as UserModel);

  RxDouble valesTotal = 0.0.obs;
  RxDouble salesTotal = 0.0.obs;

  final loading = false.obs;

  @override
  void onInit() {
    super.onInit();
    refreshSales();
    refreshVales();
    loaderListener(loading);
  }

  void goToUserSettings() async {
    var result = await Get.toNamed('/user_settings', arguments: user.value);

    if (result != null) {
      user.value = result as UserModel;
      user.refresh();
    }
  }

  String getAuthorizedRoutes() {
    String routes = '';
    for (var id in user.value.authorizedBillingRoutes) {
      var rota = Get.find<SalesRoutesService>().getRouteById(id);
      if (rota != null) {
        routes += '- ${rota.name}';
      }
      if (id != user.value.authorizedBillingRoutes.last) {
        routes += '\n';
      }
    }
    return routes;
  }

  void showMoreOptions() async {
    Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          border: Border(
            top: BorderSide(
              color: Get.theme.colorScheme.primary,
              width: 2,
            ),
          ),
        ),
        child: Wrap(
          children: [
            ListTile(
              leading: const Icon(
                Icons.add,
                color: Colors.blue,
              ),
              title: const Text(
                'Adicionar Vale',
              ),
              onTap: () async {
                Get.back();
                addSalaryAdvance();
              },
            ),
          ],
        ),
      ),
    );
  }

  void addSalaryAdvance() {
    //show dialog to add vale
    //requires value and description
    // class SalaryAdvance {
    //     String? id;
    //     int userId;
    //     double amount;
    //     String date;
    //     String description;

    //     SalaryAdvance({
    //       this.id,
    //       required this.userId,
    //       required this.amount,
    //       required this.date,
    //       required this.description,
    //     });
    //     }
    Rx<DateTime> valeDate = DateTime.now().obs;
    TextEditingController valueController = TextEditingController();
    TextEditingController descriptionController = TextEditingController();

    double valor = 0;
    String descricao = '';

    //form key
    GlobalKey<FormState> formKey = GlobalKey<FormState>();

    FocusNode descriptionFocus = FocusNode();

    RxBool loading = false.obs;
    Get.dialog(
      AlertDialog(
        title: const Text('Adicionar Vale'),
        content: Obx(
          () => Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: valueController,
                  decoration: const InputDecoration(
                    labelText: 'Valor',
                    prefixText: 'R\$ ',
                  ),
                  keyboardType: TextInputType.number,
                  autofocus: true,
                  onFieldSubmitted: (value) {
                    descriptionFocus.requestFocus();
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Campo obrigatório';
                    }
                    if (double.tryParse(value) == null) {
                      return 'Valor inválido';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    valor = double.parse(value!);
                  },
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Descrição',
                  ),
                  focusNode: descriptionFocus,
                  onFieldSubmitted: (value) {
                    descriptionFocus.unfocus();
                  },
                  onSaved: (value) {
                    descricao = value!;
                  },
                ),
                const SizedBox(height: 8),
                //data
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      side: const BorderSide(color: Colors.indigo, width: 1),
                    ),
                    onPressed: () async {
                      DateTime? date = await showDatePicker(
                        context: Get.overlayContext!,
                        initialDate: valeDate.value,
                        firstDate:
                            valeDate.value.subtract(const Duration(days: 30)),
                        lastDate: valeDate.value.add(const Duration(days: 30)),
                      );
                      if (date != null) {
                        valeDate.value = date;
                      }
                    },
                    child: Text(
                      'Data: ${DateTimeHelper.getFormattedDate(valeDate.value)}',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: const Text('Cancelar'),
          ),
          Obx(
            () => ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo,
                foregroundColor: Colors.white,
              ),
              onPressed: loading.value
                  ? null
                  : () async {
                      if (formKey.currentState!.validate()) {
                        formKey.currentState!.save();
                        var vale = SalaryAdvance(
                          userId: user.value.id,
                          amount: valor,
                          date: valeDate.value,
                          description: descricao,
                        );
                        try {
                          await Get.find<SalaryAdvanceService>()
                              .addSalaryAdvance(vale);
                          refreshVales();
                        } catch (e) {
                          Get.showSnackbar(
                            GetSnackBar(
                              title: 'Erro ao adicionar vale',
                              message: e.toString(),
                              duration: const Duration(seconds: 5),
                            ),
                          );
                          return;
                        }
                        Get.back();
                      }
                    },
              child: const Text('Adicionar'),
            ),
          ),
        ],
      ),
    );
    refreshVales();
  }

  void refreshSales() async {
    var ordersTemp = await Get.find<OrderRepository>().getOrdersFromCache(
      sellerId: user.value.id,
    );
    DateTime initialDate = DateTime.now();

    if (user.value.cobrador && user.value.cobradorFixo) {
      initialDate = DateTime(DateTime.now().year, DateTime.now().month, 1);
      ordersTemp.removeWhere((element) => element.date.isBefore(initialDate));
    } else {
      var initialFinalDay = Get.find<SettingsService>().getInitialFinalDay();
      var now = DateTime.now();
      if (now.day > initialFinalDay) {
        initialDate = DateTime(now.year, now.month, initialFinalDay);
      } else {
        initialDate = DateTime(now.year, now.month - 1, initialFinalDay);
      }

      ordersTemp.removeWhere((element) => element.date.isBefore(initialDate));
    }

    //calcular produtos vendidos
    salesTotal.value = ordersTemp.fold<double>(
        0,
        (previousValue, element) =>
            previousValue +
            element.products.fold<int>(
                0,
                (previousValue, element) =>
                    element.temComissao && (element.customValue == 0)
                        ? previousValue + (element.quantity)
                        : previousValue));
  }

  void refreshVales() async {
    if (user.value.cobrador && user.value.cobradorFixo) {
      var vales = await Get.find<SalaryAdvanceService>()
          .getSalaryAdvancesCobradorFixo(user.value.id);

      valesTotal.value = vales.fold(0, (total, vale) => total + vale.amount);
      return;
    } else {
      var vales = await Get.find<SalaryAdvanceService>()
          .getSalaryAdvancesInMonth(user.value.id);

      valesTotal.value = vales.fold(0, (total, vale) => total + vale.amount);
    }
  }

  void showVales() async {
    loading(true);
    RxList<SalaryAdvance> vales = <SalaryAdvance>[].obs;
    if (user.value.cobrador && user.value.cobradorFixo) {
      vales = (await Get.find<SalaryAdvanceService>()
              .getSalaryAdvancesCobradorFixo(user.value.id))
          .obs;
    } else {
      vales = (await Get.find<SalaryAdvanceService>()
              .getSalaryAdvancesInMonth(user.value.id))
          .obs;
    }
    loading(false);
    Get.defaultDialog(
      title: 'Vales',
      content: SizedBox(
        height: Get.height * 0.5,
        width: Get.width * 0.8,
        child: Obx(
          () => ListView(
            children: [
              for (var vale in vales)
                Slidable(
                  startActionPane: ActionPane(
                    motion: const ScrollMotion(),
                    extentRatio: 0.25,
                    openThreshold: 0.2,
                    closeThreshold: 0.8,
                    children: [
                      SlidableAction(
                        autoClose: true,
                        onPressed: (ctx) async {
                          await Get.find<SalaryAdvanceService>()
                              .deleteSalaryAdvance(vale);
                          vales.value = vales
                              .where((element) => element.id != vale.id)
                              .toList();
                          refreshVales();
                        },
                        icon: Icons.delete,
                        label: 'Deletar',
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(10),
                          topLeft: Radius.circular(10),
                        ),
                      ),
                    ],
                  ),
                  child: Card(
                    child: ListTile(
                      title: Text('R\$ ${vale.amount.toStringAsFixed(2)}'),
                      subtitle:
                          Text(DateTimeHelper.getFormattedDate(vale.date)),
                      trailing: Text(vale.description != ''
                          ? vale.description
                          : 'Sem descrição'),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
