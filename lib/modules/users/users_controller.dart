import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';

class UsersController extends GetxController {
  final UserService _userService;

  UsersController({required UserService userService})
      : _userService = userService;

  RxList<UserModel> users = <UserModel>[].obs;

  UserModel get userAuthenticated => Get.find<UserModel>();

  RxBool showTestUsers = false.obs;

  @override
  void onInit() {
    super.onInit();
    refreshUsers();
  }

  Future<void> refreshUsers() async {
    users.value = await _userService.getUsers();
    if (!showTestUsers.value) {
      users.value = users.where((user) => !user.userTest).toList();
    }
    users.refresh();
  }

  void goToUserSettings(UserModel user) async {
    await Get.toNamed('/user_settings', arguments: user);
    refreshUsers();
  }

  void goToUserView(UserModel user) async {
    await Get.toNamed('/user-view', arguments: user);
    refreshUsers();
  }

  void showUsersTest() {
    showTestUsers.value = !showTestUsers.value;
    refreshUsers();
  }
}
