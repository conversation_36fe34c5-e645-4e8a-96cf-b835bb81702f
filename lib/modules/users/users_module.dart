import 'package:fl_app/application/modules/module.dart';
import 'package:fl_app/modules/users/user_settings/user_settings_module.dart';
import 'package:fl_app/modules/users/user_view/user_view_module.dart';
import 'package:fl_app/modules/users/users_bindings.dart';
import 'package:fl_app/modules/users/users_page.dart';
import 'package:get/get_navigation/src/routes/get_route.dart';

class UsersModule implements Module {
  @override
  List<GetPage> routers = [
    GetPage(
      name: '/users',
      page: () => const UsersPage(),
      binding: UsersBindings(),
    ),
    ...UserSettingsModule().routers,
    ...UserViewModule().routers,
  ];
}
