// import 'dart:async';
// import 'dart:developer';

// import 'package:fl_app/models/client_model.dart';
// import 'package:fl_app/modules/cart/widgets/client_selection_bottom_sheet.dart';
// import 'package:fl_app/modules/home/<USER>';
// import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
// import 'package:fl_app/services/client/client_service.dart';
// import 'package:fl_app/services/geolocation/geolocation_service.dart';
// import 'package:flutter/material.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:get/get.dart';

// class ClientsPerto extends GetView<HomeController> {
//   const ClientsPerto({super.key});

//   init() async {}

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         ListTile(
//           title: Text(
//             'Clientes próximos',
//             style: Theme.of(context).textTheme.titleLarge,
//           ),
//           trailing: Row(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               IconButton(
//                 icon: const Icon(Icons.search),
//                 onPressed: () {
//                   //controller.goToSearchClientsToCart([]);
//                 },
//               ),
//             ],
//           ),
//         ),
//         Expanded(
//           child: RefreshIndicator(
//             onRefresh: controller.refreshClientsNearby,
//             child: Obx(() {
//               return ListView.builder(
//                 itemCount: controller.clientsNearby.length,
//                 itemBuilder: (context, index) {
//                   final client = controller.clientsNearby.value[index];
//                   return ClientListTile(
//                     client: client,
//                     onTap: () {
//                       Get.toNamed('/client_details', arguments: client);
//                     },
//                     distance: client.latitude != null &&
//                             client.longitude != null
//                         ? controller.currentPosition.value != null
//                             ? client
//                                 .distanceTo(controller.currentPosition.value)!
//                                 .toInt()
//                             : null
//                         : null,
//                   );
//                 },
//               );
//             }),
//           ),
//         ),
//       ],
//     );
//   }
// }
