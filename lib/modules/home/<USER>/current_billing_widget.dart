// lib/modules/home/<USER>/current_billing_widget.dart

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fl_app/modules/home/<USER>';

class CurrentBillingWidget extends StatelessWidget {
  const CurrentBillingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();
    final theme = Theme.of(context);

    return Obx(
      () => controller.appState.value.isCobrando
          ? Card(
              elevation: 8,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surface,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                  border: Border(
                    top: BorderSide(
                      color: Colors.orange.withOpacity(0.4),
                      width: 3,
                    ),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Indicador de Cobrança Atual
                    Row(
                      children: [
                        const Icon(
                          Icons.money,
                          color: Colors.orange,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Cobrando em:',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            fontWeight: FontWeight.w300,
                            fontSize: 16,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (controller.cobrancaRouteIds.length == 1)
                          Expanded(
                            child: Text(
                              controller.getRotaCobrancaAtualName(),
                              style: theme.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: theme.colorScheme.onSurface,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                      ],
                    ),
                    if (controller.cobrancaRouteIds.length > 1)
                      const SizedBox(height: 4),
                    if (controller.cobrancaRouteIds.length > 1)
                      Text(
                        controller.getRotaCobrancaAtualName(),
                        textAlign: TextAlign.start,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: theme.colorScheme.onSurface,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    const SizedBox(height: 16),

                    // Botões de Ação
                    Row(
                      children: [
                        // Botão "Ver Cobrança"
                        Expanded(
                          flex: 3,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              controller.goToNonCobradorPage();
                            },
                            icon: const Icon(Icons.visibility, size: 20),
                            label: const Text(
                              'Ver Cobrança',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              elevation: 5,
                              backgroundColor: Colors.orange[800],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),

                        // Botão "Finalizar Cobrança"
                        Expanded(
                          flex: 2,
                          child: OutlinedButton.icon(
                            onPressed: () {
                              controller.finishCobrancaInRoute();
                            },
                            icon: const Icon(Icons.check,
                                size: 20, color: Colors.orange),
                            label: const Text(
                              'Finalizar',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 14),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              side: const BorderSide(
                                  color: Colors.orange, width: 2),
                              foregroundColor: Colors.orange,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // Botão "Nova Venda"
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          controller.goToNewCart();
                        },
                        icon: Icon(Icons.add_shopping_cart,
                            size: 24, color: theme.colorScheme.onSurface),
                        label: const Text(
                          'Nova Venda',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                            side: const BorderSide(
                              color: Colors.orange,
                              width: 2,
                            ),
                          ),
                          elevation: 5,
                          backgroundColor: theme.colorScheme.surface,
                          foregroundColor: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            )
          : const SizedBox.shrink(),
    );
  }
}
