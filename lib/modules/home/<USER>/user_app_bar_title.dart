import 'package:fl_app/modules/home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserAppBarTitle extends GetView<HomeController> {
  const UserAppBarTitle({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final user = controller.user.value;
      return Text(
        'Olá, ${user?.name}',
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      );
    });
  }
}
