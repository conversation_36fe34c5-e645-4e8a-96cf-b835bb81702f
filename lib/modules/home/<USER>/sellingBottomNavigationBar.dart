import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:fl_app/modules/home/<USER>';

class SellingBottomNavigationBar extends StatelessWidget {
  const SellingBottomNavigationBar({super.key});

  @override
  Widget build(BuildContext context) {
    final HomeController controller = Get.find<HomeController>();
    final theme = Theme.of(context);

    return Card(
      elevation: 8,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      margin: EdgeInsets.zero,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          border: Border(
            top: BorderSide(
              color: theme.colorScheme.primary.withOpacity(0.4),
              width: 3,
            ),
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Primary Route Section
              _buildRouteSection(
                context,
                routeLabel: 'Vendendo em',
                routeName: controller.getRotaAtualName(),
                routeColor: theme.colorScheme.primary,
                controller: controller,
                actions: [
                  // "Ver Venda" Button
                  Expanded(
                    flex: 3,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        controller.goToSalesOrders();
                      },
                      icon: const Icon(Icons.visibility, size: 20),
                      label: const Text(
                        'Ver Venda',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        elevation: 5,
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        iconColor: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),
                  // "Cobranças" Button
                  Expanded(
                    flex: 2,
                    child: FilledButton.icon(
                      onPressed: () {
                        controller.goToCobrancasVendas();
                      },
                      icon: const Icon(Icons.receipt_long, size: 20),
                      label: const Text(
                        'Cobranças',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        elevation: 5,
                        backgroundColor: theme.colorScheme.secondaryContainer,
                      ),
                    ),
                  ),
                ],
              ),
              Obx(() {
                if (controller.appState.value.secondRouteId == null) {
                  return const SizedBox.shrink();
                }
                return _buildRouteSection(
                  context,
                  routeLabel: 'Repasse em',
                  routeName: controller.getRotaSecundariaName(),
                  routeColor: theme.colorScheme.tertiary,
                  controller: controller,
                  showMenu: false,
                  actions: [
                    Expanded(
                      flex: 3,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          controller.goToRepasses();
                        },
                        icon: const Icon(Icons.sync_alt,
                            size: 20, color: Colors.black),
                        label: const Text(
                          'Repasses',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          elevation: 5,
                          backgroundColor: theme.colorScheme.tertiary,
                          foregroundColor: theme.colorScheme.onTertiary,
                        ),
                      ),
                    ),
                    // "Remover" Button
                    Expanded(
                      flex: 2,
                      child: OutlinedButton.icon(
                        onPressed: () {
                          controller.removeSecondRoute();
                        },
                        icon: Icon(
                          Icons.close,
                          size: 20,
                          color: Theme.of(context).colorScheme.tertiary,
                        ),
                        label: Text(
                          'Remover',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Theme.of(context).colorScheme.tertiary,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          side: BorderSide(
                            color: theme.colorScheme.tertiary,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }),

              // "Nova Venda" Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    controller.goToNewCart();
                  },
                  icon: Icon(Icons.add_shopping_cart,
                      size: 24, color: theme.colorScheme.onSurface),
                  label: const Text(
                    'Nova Venda',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                      side: BorderSide(
                        color: theme.colorScheme.primary.withValues(
                          alpha: 0.4,
                        ),
                        width: 2,
                      ),
                    ),
                    elevation: 5,
                    backgroundColor: theme.colorScheme.surface,
                    foregroundColor: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRouteSection(
    BuildContext context, {
    required String routeLabel,
    required String routeName,
    required Color routeColor,
    required List<Widget> actions,
    required HomeController controller,
    bool showMenu = true,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 16,
      children: [
        // Route Information
        Row(
          children: [
            Icon(
              Icons.store,
              color: routeColor,
            ),
            const SizedBox(width: 8),
            Text(
              '$routeLabel:',
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w300,
                fontSize: 16,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                routeName,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: theme.colorScheme.onSurface,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (showMenu)
              SizedBox(
                width: 50,
                child: PopupMenuButton(
                  surfaceTintColor: theme.colorScheme.primary,
                  itemBuilder: (context) {
                    return [
                      PopupMenuItem(
                        child: ListTile(
                          title: const Text('Carregar\nCarro',
                              textAlign: TextAlign.center),
                          onTap: () async {
                            Get.back();
                            controller.goToStock();
                          },
                        ),
                      ),
                      if (controller.appState.value.secondRouteId == null)
                        PopupMenuItem(
                          child: ListTile(
                            title: const Text('Adicionar\nRota Secundaria',
                                textAlign: TextAlign.center),
                            onTap: () async {
                              await controller.addSecondRoute();
                              Get.back();
                            },
                          ),
                        ),
                      PopupMenuItem(
                        child: ListTile(
                          title: const Text('Finalizar\nVenda',
                              textAlign: TextAlign.center),
                          onTap: () async {
                            await controller.finalizarVenda();
                            Get.back();
                          },
                        ),
                      ),
                    ];
                  },
                ),
              ),
          ],
        ),
        // Action Buttons
        Row(
          spacing: 8,
          children: actions,
        ),
        const SizedBox(height: 4),
      ],
    );
  }
}
