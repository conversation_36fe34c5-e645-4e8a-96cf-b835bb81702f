import 'package:cached_network_image/cached_network_image.dart';
import 'package:fl_app/modules/home/<USER>';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class HomeDrawer extends GetView<HomeController> {
  const HomeDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = controller.isDarkMode.value;

    return Drawer(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: isDarkMode ? Colors.black : theme.scaffoldBackgroundColor,
          border: Border(
            right: BorderSide(
              color: theme.colorScheme.primary.withOpacity(0.2),
              width: 3,
            ),
          ),
        ),
        child: Column(
          children: [
            // Header do Usu<PERSON>rio
            Obx(() {
              final user = controller.user.value;
              return UserAccountsDrawerHeader(
                accountName: Text(
                  user?.name ?? 'Usuário',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                accountEmail: Text(
                  user?.email ?? '',
                  style: TextStyle(
                    color: Colors.grey[200],
                  ),
                ),
                currentAccountPicture: CircleAvatar(
                  backgroundColor: Colors.grey[300],
                  backgroundImage:
                      user?.image != null && user!.image!.isNotEmpty
                          ? CachedNetworkImageProvider(user.image!)
                          : null,
                  child: user?.image == null || user!.image!.isEmpty
                      ? Icon(
                          Icons.person,
                          size: 40,
                          color: theme.iconTheme.color,
                        )
                      : null,
                ),
                decoration: BoxDecoration(
                  color: isDarkMode
                      ? Colors.grey[900]!.withOpacity(0.5)
                      : theme.primaryColor,
                ),
                otherAccountsPictures: [
                  IconButton(
                    icon: const Icon(
                      Icons.refresh_rounded,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      controller.reloadUser();
                    },
                    tooltip: 'Atualizar',
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.exit_to_app_rounded,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      controller.logout();
                    },
                    tooltip: 'Sair',
                  ),
                ],
              );
            }),
            // Lista de Opções
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  if (controller.user.value?.admin ?? false)
                    _buildDrawerItem(
                      context,
                      icon: Icons.map,
                      text: 'Rotas de Venda',
                      onTap: () async {
                        await Get.toNamed('/sales_routes');
                        await controller.getActualSale();
                      },
                    ),
                  _buildDrawerItem(
                    context,
                    icon: FontAwesomeIcons.box,
                    text: 'Produtos',
                    onTap: () {
                      Get.toNamed('/products');
                    },
                  ),
                  if (controller.appState.value.isSelling ||
                      (controller.user.value?.admin ?? false))
                    _buildDrawerItem(
                      context,
                      icon: Icons.person,
                      text: 'Clientes',
                      onTap: controller.goToClients,
                    ),
                  if (controller.appState.value.isSelling ||
                      (controller.user.value?.admin ?? false))
                    _buildDrawerItem(
                      context,
                      icon: Icons.list_alt_rounded,
                      text: 'Pedidos',
                      onTap: controller.goToOrders,
                    ),
                  if (controller.user.value?.admin ?? false)
                    _buildDrawerItem(
                      context,
                      icon: FontAwesomeIcons.idCard,
                      text: 'Usuários',
                      onTap: () {
                        Get.toNamed('/users');
                      },
                    ),
                  if (controller.user.value?.admin ?? false)
                    _buildDrawerItem(
                      context,
                      icon: FontAwesomeIcons.chartLine,
                      text: 'Relatórios',
                      onTap: () {
                        Get.toNamed('/reports');
                      },
                    ),
                  if ((controller.user.value?.admin ?? false) &&
                      controller.user.value?.id ==
                          "P8X05JagU8RBMZNsDZwtERzVRX43")
                    _buildDrawerItem(
                      context,
                      icon: FontAwesomeIcons.userShield,
                      text: 'Admin',
                      onTap: () {
                        Get.toNamed('/admin');
                      },
                    ),
                ],
              ),
            ),
            Divider(
              color: theme.dividerColor,
              height: 0,
            ),
            // Seção de Configurações e Alternância de Tema
            Column(
              children: [
                _buildDrawerItem(
                  context,
                  icon: isDarkMode
                      ? Icons.light_mode_outlined
                      : Icons.dark_mode_outlined,
                  text: isDarkMode ? 'Mudar para Claro' : 'Mudar para Escuro',
                  onTap: () {
                    controller.isDarkMode.toggle();
                    Get.changeThemeMode(
                      Get.isDarkMode ? ThemeMode.light : ThemeMode.dark,
                    );
                    Get.find<SettingsService>().togleDarkMode();
                    Get.reload();
                  },
                ),
                _buildDrawerItem(
                  context,
                  icon: Icons.settings,
                  text: 'Configurações',
                  onTap: controller.goToSettings,
                ),
                // Botões Impressora e GPS
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Get.toNamed('/bluetooth');
                          },
                          icon: const Icon(
                            Icons.print,
                          ),
                          label: const Text('Impressora'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: theme.colorScheme.primary,
                            side: BorderSide(
                              color: theme.colorScheme.primary,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Get.toNamed('/location');
                          },
                          icon: const Icon(Icons.location_on_rounded),
                          label: const Text('GPS'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: theme.colorScheme.primary,
                            side: BorderSide(
                              color: theme.colorScheme.primary,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // Indicadores de Posição GPS
                Obx(
                  () => Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        _buildPositionIndicator(
                          icon: FontAwesomeIcons.upDown,
                          value: controller.currentPosition.value?.latitude
                                  .toString() ??
                              '',
                        ),
                        _buildPositionIndicator(
                          icon: FontAwesomeIcons.leftRight,
                          value: controller.currentPosition.value?.longitude
                                  .toString() ??
                              '',
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Função auxiliar para construir itens do Drawer
  Widget _buildDrawerItem(BuildContext context,
      {required IconData icon,
      required String text,
      required VoidCallback onTap}) {
    final theme = Theme.of(context);
    return ListTile(
      leading: Icon(
        icon,
        color: theme.iconTheme.color,
      ),
      title: Text(
        text,
        style: TextStyle(
          color: theme.textTheme.bodyLarge?.color,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      hoverColor: theme.primaryColor.withOpacity(0.1),
      contentPadding:
          const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
    );
  }

  // Função auxiliar para indicadores de posição GPS
  Widget _buildPositionIndicator(
      {required IconData icon, required String value}) {
    final theme = Theme.of(Get.context!);
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.grey,
        ),
        const SizedBox(width: 4),
        Text(
          value,
          style: TextStyle(
            color: theme.textTheme.bodyMedium?.color,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
