// ignore_for_file: constant_identifier_names
import 'dart:async';
import 'package:fl_app/application/ui/loader/loader_mixin.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';
import 'package:fl_app/services/cart/cart_service.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:restart_app/restart_app.dart';

class HomeController extends GetxController with LoaderMixin {
  final UserService _userService;
  final CartService _cartService;
  final AppStateService _appStateService;
  final OrderRepository _ordersRepository;
  final GeolocationService _geolocationService;
  final SalesRoutesRepository _salesRoutesRepository;
  final OfflineSyncRepositoryImpl _offlineSyncRepositoryImpl;

  HomeController({
    required UserService userService,
    required CartService cartService,
    required AppStateService appStateService,
    required OrderRepository ordersRepository,
    required GeolocationService geolocationService,
    required SalesRoutesRepository salesRoutesRepository,
    required OfflineSyncRepositoryImpl offlineSyncRepositoryImpl,
  })  : _userService = userService,
        _cartService = cartService,
        _appStateService = appStateService,
        _ordersRepository = ordersRepository,
        _geolocationService = geolocationService,
        _salesRoutesRepository = salesRoutesRepository,
        _offlineSyncRepositoryImpl = offlineSyncRepositoryImpl;

  OfflineSyncRepositoryImpl get offlineSyncRepositoryImpl =>
      _offlineSyncRepositoryImpl;

  Rxn<UserModel> user = Rxn<UserModel>();
  RxList<ShoppingCart> get shoppingCarts => _cartService.getShoppingCarts();

  RxBool connection = false.obs;

  RxInt markedToday = 0.obs;
  RxInt pendingCount = 0.obs;
  RxInt myDeliveryCount = 0.obs;

  final Rx<AppState> appState = Get.find<AppStateService>().getAppState().obs;

  late Stream<InternetStatus> _connectionStream;

  RxBool isCobrando = false.obs;
  RxList<String> cobrancaRouteIds = <String>[].obs;

  // ignore: unused_field
  late StreamSubscription<Position?> _stream;
  Rxn<Position?> currentPosition = Rxn<Position?>();

  RxBool isLoading = false.obs;

  RxBool isDarkMode = false.obs;

  @override
  void onInit() {
    loaderListener(isLoading);
    super.onInit();
  }

  Future<void> reloadUser() async {
    if (connection.value == false) {
      Get.showSnackbar(
        const GetSnackBar(
          title: 'Sem conexão',
          message: 'Você precisa de uma conexão com a internet para atualizar',
          duration: Duration(seconds: 1),
        ),
      );

      return;
    }
    user.value = await _userService.reloadUserAuthenticated();
    user.refresh();
  }

  init() async {
    _connectionStream = await Get.find<ConnectionService>().getListenable();
    _connectionStream.listen((status) {
      if (status == InternetStatus.connected) {
        connection.value = true;
      } else {
        connection.value = false;
      }
    });
    user.value = await _userService.getUserAuthenticated();
    if (user.value == null) {
      Get.offAllNamed('/splash');
    }
    user.refresh();
    loadSettings();
    getActualSale();
    getActualCobranca();

    final stream = await _geolocationService.getCurrentLocationStream();
    currentPosition.value = await _geolocationService.getCurrentLocation();
    _geolocationService.updateUserLocation(currentPosition.value!);
    _stream = stream.listen((position) {
      //log('Localização atualizada HOME: $position');
      currentPosition.value = position;
      _geolocationService.updateUserLocation(position);
    });

    if (user.value != null &&
        user.value!.cobrador &&
        user.value!.cobradorFixo) {
      refreshCobradorFixoCounts();
    }
  }

  @override
  void onReady() {
    init();
    super.onReady();
  }

  Future<void> logout() async {
    await Get.dialog(
      AlertDialog(
        title: const Text('Sair'),
        content: const Text(
            'Deseja realmente sair? Necessário conexão com a internet para sair'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () async {
              if ((await Get.find<ConnectionService>().checkConnection())) {
                isLoading(true);
                try {
                  Get.delete<UserModel>();
                  await _userService.logout();
                  isLoading(false);
                  Get.back();
                  Restart.restartApp();
                  return;
                } catch (e) {
                  isLoading(false);
                  Get.showSnackbar(
                    GetSnackBar(
                      title: 'Erro',
                      message: 'Erro ao sair ${e.toString()}',
                      duration: const Duration(seconds: 1),
                    ),
                  );
                  return;
                }
              }
              Get.showSnackbar(
                const GetSnackBar(
                  title: 'Sem conexão',
                  message:
                      'Você precisa de uma conexão com a internet para sair',
                  duration: Duration(seconds: 1),
                ),
              );
            },
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );
  }

  Future<void> goToNewCart({ClientModel? client}) async {
    final newShoppingCart = await _cartService.createShoppingCart();
    await Get.toNamed('/cart', arguments: {
      'order': null,
      'cart': newShoppingCart,
      'client': client,
    });
  }

  Future<void> addClientAndOpenNewOrder() async {
    final client = await Get.toNamed('/form_client');
    if (client != null) {
      await goToNewCart(client: client as ClientModel);
    }
  }

  Future<void> goToCart(ShoppingCart e) async {
    await Get.toNamed('/cart', arguments: {
      'order': null,
      'cart': e,
    });
    shoppingCarts.refresh();
  }

  Future<void> removeCart(ShoppingCart e) async {
    await _cartService.removeShoppingCart(e);
  }

  Future<void> getActualSale() async {
    appState(_appStateService.getAppState());
    appState.refresh();
  }

  String getRotaAtualName() {
    if (appState.value.isSellingRouteId == null) {
      return 'Rota não informada';
    }
    final rotaName = _salesRoutesRepository
        .getSaleRouteName(appState.value.isSellingRouteId!);
    return rotaName ?? 'Rota não informada';
  }

  String getRotaSecundariaName() {
    if (appState.value.secondRouteId == null) {
      return 'Rota não informada';
    }
    final rotaName =
        _salesRoutesRepository.getSaleRouteName(appState.value.secondRouteId!);
    return rotaName ?? 'Rota não informada';
  }

  String getRotaCobrancaAtualName() {
    if (cobrancaRouteIds.isEmpty) {
      return 'Rota não informada';
    }
    String rotaNames = '';
    for (var id in cobrancaRouteIds) {
      var rotaName = _salesRoutesRepository.getSaleRouteName(id);
      if (cobrancaRouteIds.last == id) {
        rotaNames += rotaName!;
      } else if (cobrancaRouteIds.length == 1) {
        rotaNames += rotaName!;
      } else {
        rotaNames += '$rotaName, ';
      }
    }
    return rotaNames;
  }

  String? getRotaName(String id) {
    final rotaname = _salesRoutesRepository.getSaleRouteName(id);
    return rotaname;
  }

  void goToSalesOrders() async {
    await Get.toNamed(
      '/sale_details',
      arguments: appState.value.isSellingRouteId,
    );
    await getActualSale();
  }

  void goToCobrancasVendas() {
    Get.toNamed('/sale_billing', arguments: appState.value);
  }

  void goToCobrancas() async {
    if (user.value!.admin || user.value!.cobrador) {
      await Get.toNamed('/billing');
      refreshMarkedTodayOrdersCount();
      refreshPendingOrdersCount();
    }
  }

  Widget adminPasswordAlert(String password, String adminPassword) =>
      AlertDialog(
        title: const Text('Senha de administrador'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                onChanged: (value) {
                  password = value;
                },
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'Senha',
                ),
                autofocus: true,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(result: false);
            },
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () {
              if (password == adminPassword) {
                FocusScope.of(Get.overlayContext!).unfocus();
                Get.back(result: true);
              } else {
                Get.showSnackbar(
                  const GetSnackBar(
                    title: 'Senha incorreta',
                    message: 'A senha informada não é válida',
                    duration: Duration(seconds: 1),
                  ),
                );
              }
            },
            child: const Text('Confirmar'),
          ),
        ],
      );

  Future<bool> checkAdminPassword() async {
    bool passed = false;
    bool result = false;
    if (user.value!.admin) {
      passed = true;
    } else {
      final adminPassword =
          await Get.find<SettingsService>().getAdminPassword();

      //request password
      var password = '';
      var resultAlert =
          await Get.dialog(adminPasswordAlert(password, adminPassword));
      if (resultAlert != null) {
        result = resultAlert;
      }
      await Future.delayed(const Duration(milliseconds: 500));
    }
    return passed || result;
  }

  Future<void> startSale() async {
    if (await checkAdminPassword()) {
      final routes = _salesRoutesRepository.getSalesRoutesFromCache();
      Get.dialog(
        AlertDialog(
          title: const Text('Iniciar venda'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: (Get.height * 0.5) - Get.bottomBarHeight,
                width: 300,
                child: Scrollbar(
                  child: ListView.builder(
                    itemCount: routes.length,
                    itemBuilder: (context, index) {
                      final route = routes[index];
                      return ListTile(
                        title: Text(route.name),
                        onTap: () async {
                          await _appStateService.startSaleInRoute(route.id!);
                          await getActualSale();
                          notifyChildrens();
                          Get.back();
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
              },
              child: const Text('Cancelar'),
            ),
          ],
        ),
      );
    }
  }

  Future<void> goToClients() async {
    final routes = _salesRoutesRepository.getSalesRoutesFromCache();

    if (user.value != null && (user.value!.admin || user.value!.cobrador)) {
      Get.dialog(
        AlertDialog(
          title: const Text('Selecionar rota', textAlign: TextAlign.center),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: Get.height * 0.5,
                width: 300,
                child: Scrollbar(
                  child: ListView.builder(
                    itemCount: routes.length + 1,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return user.value!.admin
                            ? ListTile(
                                title: const Text('Todas as rotas'),
                                onTap: () async {
                                  Get.back();
                                  await Get.toNamed('/clients',
                                      arguments: null);
                                },
                              )
                            : const SizedBox.shrink();
                      }
                      final route = routes[index - 1];
                      if (route.id == appState.value.isSellingRouteId) {
                        return ListTile(
                          title: Text(route.name),
                          onTap: () async {
                            Get.back();
                            await Get.toNamed('/clients', arguments: route.id);
                          },
                        );
                      } else if (user.value!.cobrador &&
                          user.value!.authorizedBillingRoutes
                              .contains(route.id)) {
                        return ListTile(
                          title: Text(route.name),
                          onTap: () async {
                            Get.back();
                            await Get.toNamed('/clients', arguments: route.id);
                          },
                        );
                      } else if (user.value!.admin) {
                        return ListTile(
                          title: Text(route.name),
                          onTap: () async {
                            Get.back();
                            await Get.toNamed('/clients', arguments: route.id);
                          },
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
              },
              child: const Text('Cancelar'),
            ),
          ],
        ),
      );
    } else if (user.value != null &&
        appState.value.isSellingRouteId != null &&
        appState.value.isSelling) {
      Get.toNamed('/clients', arguments: appState.value.isSellingRouteId);
    }
  }

  Future<void> goToOrders() async {
    final routes = _salesRoutesRepository.getSalesRoutesFromCache();

    if (user.value != null && (user.value!.admin || user.value!.cobrador)) {
      await Get.dialog(
        AlertDialog(
          title: const Text('Selecionar rota', textAlign: TextAlign.center),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: Get.height * 0.5,
                width: 300,
                child: Scrollbar(
                  child: ListView.builder(
                    itemCount: routes.length + 1,
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return user.value!.admin
                            ? ListTile(
                                title: const Text('Todas as rotas'),
                                onTap: () async {
                                  Get.back();
                                  await Get.toNamed('/orders', arguments: null);
                                },
                              )
                            : const SizedBox.shrink();
                      }
                      final route = routes[index - 1];
                      if (route.id == appState.value.isSellingRouteId) {
                        return ListTile(
                          title: Text(route.name),
                          onTap: () async {
                            Get.back();
                            await Get.toNamed('/orders', arguments: route.id);
                          },
                        );
                      } else if (user.value!.cobrador &&
                          user.value!.authorizedBillingRoutes
                              .contains(route.id)) {
                        return ListTile(
                          title: Text(route.name),
                          onTap: () async {
                            Get.back();
                            await Get.toNamed('/orders', arguments: route.id);
                          },
                        );
                      } else if (user.value!.admin) {
                        return ListTile(
                          title: Text(route.name),
                          onTap: () async {
                            Get.back();
                            await Get.toNamed('/orders', arguments: route.id);
                          },
                        );
                      } else {
                        return const SizedBox.shrink();
                      }
                    },
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Get.back();
              },
              child: const Text('Cancelar'),
            ),
          ],
        ),
      );
      if (user.value != null &&
          user.value!.cobrador &&
          user.value!.cobradorFixo) {
        refreshCobradorFixoCounts();
      }
    } else if (user.value != null &&
        appState.value.isSellingRouteId != null &&
        appState.value.isSelling) {
      await Get.toNamed('/orders', arguments: appState.value.isSellingRouteId);
    }
  }

  refreshCobradorFixoCounts() {
    refreshMarkedTodayOrdersCount();
    refreshPendingOrdersCount();
    refreshMyDeliveriesCount();
  }

  goToRoutes() {
    Get.toNamed('/sales_routes');
  }

  void goToDeliveries() async {
    await Get.toNamed('/deliveries',
        arguments: user.value!.authorizedBillingRoutes);
    refreshMyDeliveriesCount();
  }

  void refreshMarkedTodayOrdersCount() async {
    if (user.value != null && user.value!.cobrador) {
      markedToday.value = (await _ordersRepository
              .getMarkedToTodayOrders(user.value!.authorizedBillingRoutes))
          .length;
    }
  }

  void refreshPendingOrdersCount() async {
    if (user.value != null && user.value!.cobrador) {
      pendingCount.value =
          (await _ordersRepository.getPendingOrdersToCobradorFixo(
                  user.value!.authorizedBillingRoutes, ''))
              .length;
    }
  }

  void refreshMyDeliveriesCount() async {
    if (user.value != null && user.value!.cobrador) {
      var ordersDelivery =
          await _ordersRepository.getOrdersDeliveryGroupedByRoute();
      int count = 0;
      for (var key in ordersDelivery.keys) {
        if (user.value!.authorizedBillingRoutes.contains(key.id!)) {
          count += ordersDelivery[key]!.length;
        }
      }
      myDeliveryCount.value = count;
    }
  }

  void startCobrancaInRoutes(List<SaleRouteModel> routes) async {
    await _appStateService
        .startCobrancaInRoutes(routes.map((e) => e.id!).toList());
    await getActualCobranca();
    notifyChildrens();
  }

  getActualCobranca() {
    isCobrando.value = _appStateService.isCobrando();
    cobrancaRouteIds.value = _appStateService.getCobrancaRouteIds() ?? [];
    appState.value = _appStateService.getAppState();
    appState.refresh();
  }

  Future<void> finishCobrancaInRoute() async {
    await Get.dialog(
      AlertDialog(
        title: const Text('Finalizar cobrança'),
        content: const Text('Deseja finalizar a cobrança?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () async {
              await _appStateService.finishCobrancaInRoute();
              await getActualCobranca();
              var boxArchive = await Hive.openBox('nonCobradorOrdersArchive');
              boxArchive.put('clientsArchive', <String>[]);
              Get.back();
            },
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );
  }

  Future<void> finalizarVenda() async {
    await Get.dialog(
      AlertDialog(
        title: const Text('Finalizar venda'),
        content: const Text('Deseja finalizar a venda?'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: const Text('Cancelar'),
          ),
          TextButton(
            onPressed: () async {
              await _appStateService.finishSaleInRoute();
              await getActualSale();
              notifyChildrens();
              Get.back();
            },
            child: const Text('Confirmar'),
          ),
        ],
      ),
    );
  }

  void goToNonCobradorPage() async {
    await Get.toNamed('/non_cobrador_billing',
        arguments: [cobrancaRouteIds, getRotaCobrancaAtualName()]);
  }

  Future<void> addSecondRoute() async {
    final routes = _salesRoutesRepository.getSalesRoutesFromCache();

    await Get.dialog(
      AlertDialog(
        title: const Text('Adicionar rota secundaria'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: (Get.height * 0.5) - Get.bottomBarHeight,
              width: 300,
              child: Scrollbar(
                child: ListView.builder(
                  itemCount: routes.length,
                  itemBuilder: (context, index) {
                    final route = routes[index];
                    return ListTile(
                      title: Text(route.name),
                      onTap: () async {
                        DateTime? date;
                        //select date
                        await showDatePicker(
                          context: Get.overlayContext!,
                          helpText: 'Selecione a data da ultima venda',
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now()
                              .subtract(const Duration(days: 365)),
                          lastDate:
                              DateTime.now().add(const Duration(days: 365)),
                        ).then((value) {
                          date = value;
                        });
                        if (date != null) {
                          // select
                          await _appStateService.enableSecondRoute(
                              route.id!, date!);
                          await getActualSale();
                          Get.back();
                        } else {
                          Get.back();
                        }
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: const Text('Cancelar'),
          ),
        ],
      ),
    );
  }

  Future<void> removeSecondRoute() async {
    await _appStateService.disableSecondRoute();
    await getActualSale();
  }

  Future<void> goToRepasses() async {
    await Get.toNamed('/resell_billing', arguments: appState.value);
  }

  void goToSettings() async {
    await Get.toNamed('/settings');
    loadSettings();
  }

  void loadSettings() {
    Get.find<SettingsService>().getDarkMode().then((value) {
      isDarkMode.value = value;
    });
  }

  void goToStock() {
    Get.toNamed('/sale-stock', arguments: appState.value.isSellingRouteId!);
  }

  void setKeepAlive(bool value) {
    Get.find<AppStateService>().setKeepAlive(value);
    appState.value.isKeepAlive = value;
    appState.refresh();
  }
}
