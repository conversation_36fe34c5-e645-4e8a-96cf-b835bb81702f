import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/modules/home/<USER>';
import 'package:fl_app/modules/home/<USER>/current_billing_widget.dart';
import 'package:fl_app/modules/home/<USER>/home_drawer.dart';
import 'package:fl_app/modules/home/<USER>/sellingBottomNavigationBar.dart';
import 'package:fl_app/modules/home/<USER>/user_app_bar_title.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:move_to_background/move_to_background.dart';
import 'package:multi_select_flutter/multi_select_flutter.dart';

class HomePage extends GetView<HomeController> {
  const HomePage({super.key});

  // Adjusted method to create home items
  Widget _buildHomeItem({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    Color? backgroundColor,
    Color? textColor,
  }) {
    final isDarkMode = controller.isDarkMode.value;
    return Container(
      margin: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: backgroundColor ??
            (isDarkMode ? Colors.grey[800] : Colors.indigo[50]),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: backgroundColor ?? Colors.transparent),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(10),
        splashColor: Colors.indigo.withOpacity(0.2),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 2.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon,
                  size: 24,
                  color:
                      textColor ?? (isDarkMode ? Colors.white : Colors.black)),
              const SizedBox(height: 2),
              Text(
                title,
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: textColor),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Adjusted method to build the grid of home items
  Widget _buildHomeGrid(BuildContext context) {
    final user = controller.user.value;
    final isSelling = controller.appState.value.isSelling;

    int crossAxisCount = _calculateCrossAxisCount(context);

    return GridView(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: 0.66,
        crossAxisSpacing: 0,
        mainAxisSpacing: 0,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        if (user != null && user.cobrador && !isSelling)
          _buildHomeItem(
            title: 'Iniciar\nVendas',
            icon: Icons.shopping_cart,
            onTap: controller.startSale,
            backgroundColor: Colors.indigoAccent,
            textColor: Colors.white,
          ),
        _buildHomeItem(
          title: 'Produtos',
          icon: FontAwesomeIcons.box,
          onTap: () => Get.toNamed('/products'),
        ),
        if (isSelling || user?.admin == true || user?.cobrador == true)
          _buildHomeItem(
            title: 'Pedidos',
            icon: Icons.list_alt_rounded,
            onTap: controller.goToOrders,
          ),
        if (isSelling || user?.admin == true || user?.cobrador == true)
          _buildHomeItem(
            title: 'Clientes',
            icon: Icons.person,
            onTap: controller.goToClients,
          ),
        if (user?.admin == true)
          _buildHomeItem(
            title: 'Rotas',
            icon: Icons.map,
            onTap: controller.goToRoutes,
          ),
        _buildHomeItem(
          title: 'Entregas',
          icon: Icons.delivery_dining,
          onTap: controller.goToDeliveries,
        ),
      ],
    );
  }

  // Method to calculate crossAxisCount based on screen width
  int _calculateCrossAxisCount(BuildContext context) {
    // double screenWidth = MediaQuery.of(context).size.width;
    // int crossAxisCount = (screenWidth / 120).floor();
    //return crossAxisCount.clamp(2, 5);
    return 5;
  }

  // Adjusted method to build the action buttons row
  Widget _buildActionButtons(BuildContext context) {
    final user = controller.user.value;
    final isSelling = controller.appState.value.isSelling;
    final isCobrando = controller.isCobrando.value;

    if (isSelling || isCobrando) return const SizedBox.shrink();

    List<Widget> buttons = [];

    if (user != null && !user.cobrador) {
      buttons.add(
        Expanded(
          child: ElevatedButton(
            onPressed: controller.startSale,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              foregroundColor: Colors.black,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Iniciar Vendas'),
          ),
        ),
      );
    }

    if (user != null &&
        (user.cobrador && user.cobradorTemporario || user.admin == true)) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 8));
      buttons.add(
        Expanded(
          child: ElevatedButton(
            onPressed: () => _startTemporaryBilling(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.tertiary,
              foregroundColor: Colors.black,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10)),
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text('Iniciar Cobrança'),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: buttons,
      ),
    );
  }

  // Method to handle starting temporary billing
  Future<void> _startTemporaryBilling(BuildContext context) async {
    final salesRoutesService = Get.find<SalesRoutesService>();
    final routes = await salesRoutesService.getSalesRoutesFromCache();
    final user = controller.user.value!;

    if (user.cobrador && !user.admin) {
      final cobradorRoutes = user.authorizedBillingRoutes;
      routes.removeWhere((route) => !cobradorRoutes.contains(route.id));
    }

    List<SaleRouteModel> selectedRoutes = [];

    await showDialog(
      context: context,
      builder: (ctx) {
        return MultiSelectDialog<SaleRouteModel>(
          title: const Text('Selecione as rotas'),
          items: routes
              .map((route) => MultiSelectItem(route, route.name))
              .toList(),
          initialValue: selectedRoutes,
          onConfirm: (values) {
            selectedRoutes = values;
            Get.back();
          },
          itemsTextStyle: TextStyle(
            fontSize: 16,
            color: controller.isDarkMode.value ? Colors.white : Colors.black,
          ),
          selectedItemsTextStyle: TextStyle(
            fontSize: 16,
            color: controller.isDarkMode.value ? Colors.white : Colors.black,
          ),
          checkColor: Colors.amber[400],
          selectedColor: Colors.indigo,
        );
      },
    );

    if (selectedRoutes.isNotEmpty) {
      controller.startCobrancaInRoutes(selectedRoutes);
      await controller.getActualCobranca();
    }
  }

  // Extracted method to build the ongoing orders section
  Widget _buildOngoingOrders() {
    if (controller.shoppingCarts.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(
            'Pedidos em andamento (${controller.shoppingCarts.length})',
            style: Get.textTheme.titleLarge,
          ),
        ),
        Obx(() {
          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.shoppingCarts.length,
            itemBuilder: (context, index) {
              final cart = controller.shoppingCarts[index];
              return Slidable(
                key: ValueKey(cart.id),
                endActionPane: ActionPane(
                  motion: const ScrollMotion(),
                  children: [
                    SlidableAction(
                      onPressed: (context) => controller.removeCart(cart),
                      label: 'Cancelar',
                      backgroundColor: Colors.red,
                      icon: Icons.delete,
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ],
                ),
                child: _buildOrderCard(cart),
              );
            },
          );
        }),
      ],
    );
  }

  // Adjusted method to build order card
  Widget _buildOrderCard(ShoppingCart cart) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: ListTile(
        leading: const Icon(Icons.shopping_bag, color: Colors.indigo),
        title: Text(
          cart.client.value?.name ?? 'Cliente não informado',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (cart.client.value?.address != null)
              Text(cart.client.value!.address),
            const SizedBox(height: 4),
            Text(
              'Total: R\$ ${(cart.getTotalCart() + cart.restante.value).toStringAsFixed(2)}',
              style: TextStyle(
                  color: Colors.green[700], fontWeight: FontWeight.bold),
            ),
          ],
        ),
        trailing: IconButton(
          icon: const Icon(Icons.arrow_forward),
          onPressed: () => controller.goToCart(cart),
        ),
        onTap: () => controller.goToCart(cart),
      ),
    );
  }

  // Extracted method to build the floating action button
  Widget _buildFloatingActionButton() {
    final user = controller.user.value;
    final isSelling = controller.appState.value.isSelling;
    final isCobrando = controller.isCobrando.value;

    if (user == null ||
        (!user.admin && !user.cobrador) ||
        isSelling ||
        isCobrando) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        Positioned(
          right: 0,
          bottom: 0,
          child: SizedBox(
            width: 100,
            child: ElevatedButton(
              onPressed: () {
                controller.addClientAndOpenNewOrder();
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.only(
                    left: 60, right: 8, top: 18, bottom: 20),
                backgroundColor: Colors.indigo[50],
                foregroundColor: Colors.indigo,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Icon(Icons.person_add, color: Colors.indigo),
            ),
          ),
        ),
        Positioned(
          right: 40,
          bottom: 0,
          child: FloatingActionButton.extended(
            onPressed: controller.goToNewCart,
            label: const Text('Nova Venda'),
            icon: const Icon(Icons.add_shopping_cart),
          ),
        ),
      ],
    );
  }

  // Extracted method to build the app bar actions
  List<Widget> _buildAppBarActions() {
    return [
      Obx(() {
        final isEnable = controller.offlineSyncRepositoryImpl.isEnable.value;
        final connectedDevices =
            controller.offlineSyncRepositoryImpl.connectedDevices.value;

        if (!isEnable) return const SizedBox.shrink();

        return Stack(
          alignment: Alignment.center,
          children: [
            Tooltip(
              message: '${connectedDevices.length} dispositivos conectados',
              child: IconButton(
                onPressed: () => Get.toNamed('/offline_sync'),
                icon: Icon(
                  connectedDevices.isNotEmpty
                      ? Icons.sync
                      : Icons.sync_disabled,
                  color: connectedDevices.isNotEmpty
                      ? Colors.green[300]
                      : Colors.grey,
                ),
              ),
            ),
            //badge
            if (connectedDevices.isNotEmpty)
              Positioned(
                right: 8,
                top: 8,
                child: CircleAvatar(
                  radius: 10,
                  backgroundColor: Colors.redAccent,
                  child: Text(
                    connectedDevices.length.toString(),
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
          ],
        );
      }),
      Padding(
        padding: const EdgeInsets.all(8.0),
        child: Obx(() {
          final isConnected = controller.connection.value;
          return Icon(
            isConnected ? Icons.wifi_rounded : Icons.wifi_off_rounded,
            color: isConnected ? Colors.green[300] : Colors.red[300],
          );
        }),
      ),
      Obx(() {
        return Tooltip(
          message: 'Manter aplicativo aberto',
          child: Switch(
            value: controller.appState.value.isKeepAlive,
            onChanged: (value) {
              controller.setKeepAlive(value);
            },
            activeColor: Colors.green,
          ),
        );
      }),
    ];
  }

  // Helper method to build cobranca info boxes
  Widget _buildCobrancaInfo(String title, String value) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white70,
        borderRadius: BorderRadius.circular(10),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(
                fontSize: 14, color: Colors.black, fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: const TextStyle(
                fontSize: 14, fontWeight: FontWeight.w500, color: Colors.black),
          ),
        ],
      ),
    );
  }

  Widget _buildCobradorSections(BuildContext context) {
    final user = controller.user.value;
    final isSelling = controller.appState.value.isSelling;
    final isCobrando = controller.isCobrando.value;

    if (isSelling || isCobrando) return const SizedBox.shrink();

    List<Widget> sections = [];

    // Handling for cobrador fixo
    if (user != null && user.cobrador && user.cobradorFixo) {
      sections.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Row(
            children: [
              // Cobranças Fixo
              Expanded(
                child: InkWell(
                  onTap: controller.goToCobrancas,
                  borderRadius: BorderRadius.circular(10),
                  child: Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: Get.isDarkMode
                          ? Colors.green[900]
                          : Colors.green[100],
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color:
                            Get.isDarkMode ? Colors.black : Colors.green[200]!,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'COBRANÇAS',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Get.isDarkMode
                                ? Colors.white
                                : Colors.indigo[900],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _buildCobrancaInfo(
                                'Hoje', '${controller.markedToday.value}'),
                            const SizedBox(width: 16),
                            _buildCobrancaInfo('Pendentes',
                                '${controller.pendingCount.value}'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: InkWell(
                  onTap: controller.goToDeliveries,
                  borderRadius: BorderRadius.circular(10),
                  child: Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: Get.isDarkMode
                          ? Colors.green[900]
                          : Colors.green[100],
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color:
                            Get.isDarkMode ? Colors.black : Colors.green[200]!,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'MINHAS\nENTREGAS',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 17,
                            fontWeight: FontWeight.bold,
                            color: Get.isDarkMode
                                ? Colors.white
                                : Colors.indigo[900],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${controller.myDeliveryCount.value}',
                          style: TextStyle(
                            fontSize: 34,
                            fontWeight: FontWeight.w500,
                            color: Get.isDarkMode
                                ? Colors.white
                                : Colors.indigo[900],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Action buttons for non-cobrador or cobrador temporário
    sections.add(_buildActionButtons(context));

    return Column(children: sections);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => PopScope(
        canPop: !controller.appState.value.isKeepAlive,
        onPopInvokedWithResult: (didPop, result) {
          if (controller.appState.value.isKeepAlive) {
            MoveToBackground.moveTaskToBack();
          }
        },
        child: Scaffold(
          drawer: const HomeDrawer(),
          bottomNavigationBar: Obx(() {
            final appState = controller.appState.value;
            if (appState.isSelling) {
              return const SellingBottomNavigationBar();
            } else if (appState.isCobrando && !appState.isSelling) {
              return const CurrentBillingWidget();
            } else {
              return const SizedBox.shrink();
            }
          }),
          appBar: AppBar(
            title: const UserAppBarTitle(),
            actions: _buildAppBarActions(),
          ),
          body: SingleChildScrollView(
            child: Obx(() {
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    _buildHomeGrid(context),
                    _buildCobradorSections(context),
                    _buildOngoingOrders(),
                  ],
                ),
              );
            }),
          ),
          floatingActionButton: Obx(() => _buildFloatingActionButton()),
        ),
      ),
    );
  }
}
