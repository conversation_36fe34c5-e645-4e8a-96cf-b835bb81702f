// import 'package:fl_app/models/product_model.dart';
// import 'package:fl_app/repositories/image/image_repository.dart';
// import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
// import 'package:fl_app/data/repositories/product_repository.dart';
// import 'package:fl_app/services/connection/connection_service.dart';
// import 'package:get/get.dart';
// import 'package:uuid/uuid.dart';

// import './product_service.dart';

// class ProductServiceImpl implements ProductService {
//   late ProductRepository _repository;
//   late ImageRepository _imageRepository;
//   late OfflineSyncRepositoryImpl _offlineSyncRepository;

//   ProductServiceImpl({
//     required ProductRepository repository,
//     required ImageRepository imageRepository,
//     required OfflineSyncRepositoryImpl offlineSyncRepository,
//   }) {
//     _repository = repository;
//     _imageRepository = imageRepository;
//     _offlineSyncRepository = offlineSyncRepository;
//   }

//   @override
//   Future<RxList<ProductModel>> getProducts() async {
//     final result = await _repository.getProducts();

//     return result.where((p0) => !p0.isDeleted).toList().obs;
//   }

//   @override
//   Future<ProductModel?> getProduct(String id) async {
//     return _repository.getProduct(id);
//   }

//   @override
//   Future<ProductModel> addProduct(
//       ProductModel product, String? imagePath) async {
//     if (imagePath != null && imagePath.isNotEmpty) {
//       if (await Get.find<ConnectionService>().checkConnection()) {
//         final url = await _imageRepository.uploadImage(imagePath,
//             '/products/${product.nome}-${const Uuid().v4().substring(0, 10)}.jpg');
//         product.imagem = url;
//       }
//     }
//     final result = await _repository.addProduct(product);
//     _offlineSyncRepository.addProductToSync(product);
//     return result;
//   }

//   @override
//   Future<ProductModel> updateProduct(
//       ProductModel product, String? imagePath) async {
//     if (imagePath != null && imagePath.isNotEmpty) {
//       if (await Get.find<ConnectionService>().checkConnection()) {
//         if (product.imagem != null && product.imagem != '') {
//           await _imageRepository.deleteImage(product.imagem!);
//         }
//         final newImageUrl = await _imageRepository.uploadImage(imagePath,
//             '/products/${product.nome}-${const Uuid().v4().substring(0, 10)}.jpg');
//         product.imagem = newImageUrl;
//       }
//     }
//     final result = await _repository.updateProduct(product);
//     _offlineSyncRepository.addProductToSync(product);
//     return result;
//   }

//   @override
//   Future<ProductModel> deleteProduct(ProductModel product) async {
//     final result = await _repository.deleteProduct(product);
//     _offlineSyncRepository
//         .addProductToSync((await _repository.getProduct(product.id!))!);
//     return result;
//   }

//   @override
//   Future<RxList<ProductModel>> getProductsFromCache() async {
//     final result = await _repository.getProductsFromCache();
//     return result.obs;
//   }

//   @override
//   Future<RxList<ProductModel>> getDeletedProductsFromCache() async {
//     final result = await _repository.getDeletedProductsFromCache();
//     return result.obs;
//   }

//   @override
//   Future<void> syncProduct(ProductModel productModel) async {
//     await _repository.syncProduct(productModel);
//   }

//   @override
//   Future<ProductModel> restoreProduct(ProductModel product) async {
//     final result = await _repository.restoreProduct(product);
//     _offlineSyncRepository.addProductToSync(product);
//     return result;
//   }
// }
