// import 'package:fl_app/models/product_model.dart';
// import 'package:get/get.dart';

// abstract class ProductService {
//   Future<RxList<ProductModel>> getProducts();
//   Future<RxList<ProductModel>> getProductsFromCache();
//   Future<RxList<ProductModel>> getDeletedProductsFromCache();
//   Future<ProductModel?> getProduct(String id);
//   Future<ProductModel> addProduct(ProductModel product, String? imagePath);
//   Future<ProductModel> updateProduct(ProductModel product, String? imagePath);
//   Future<ProductModel> deleteProduct(ProductModel product);

//   Future<void> syncProduct(ProductModel productModel);

//   Future<ProductModel> restoreProduct(ProductModel product);
// }
