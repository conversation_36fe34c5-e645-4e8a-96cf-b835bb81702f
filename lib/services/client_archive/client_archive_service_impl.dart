import 'dart:developer';
import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import './client_archive_service.dart';

class ClientArchiveServiceImpl implements ClientArchiveService {
  final OrderRepository _orderRepository;
  Box? box;
  List<String> _archivedClients = [];
  final RxMap<String, List<OrderModel>> _groupedOrders = RxMap({});
  final RxList<MapEntry<String, List<OrderModel>>> _activeOrders =
      RxList<MapEntry<String, List<OrderModel>>>();
  final RxList<MapEntry<String, List<OrderModel>>> _archivedOrders =
      RxList<MapEntry<String, List<OrderModel>>>();

  ClientArchiveServiceImpl({
    required OrderRepository orderRepository,
  }) : _orderRepository = orderRepository;

  @override
  Future<void> initialize(String boxName) async {
    log('Inicializando ClientArchiveService com box: $boxName');
    box = await Hive.openBox(boxName);
    _archivedClients = box!.get('clientsArchive', defaultValue: <String>[]);
    _updateOrderLists();
  }

  @override
  Future<void> archiveClient(String clientId) async {
    if (!_archivedClients.contains(clientId)) {
      _archivedClients.add(clientId);
      await box?.put('clientsArchive', _archivedClients);
      log('Cliente arquivado: $clientId');
      _updateOrderLists();
    }
  }

  @override
  Future<void> unarchiveClient(String clientId) async {
    if (_archivedClients.contains(clientId)) {
      _archivedClients.remove(clientId);
      await box?.put('clientsArchive', _archivedClients);
      log('Cliente desarquivado: $clientId');
      _updateOrderLists();
    }
  }

  @override
  bool isClientArchived(String clientId) {
    return _archivedClients.contains(clientId);
  }

  @override
  List<String> getArchivedClientIds() {
    return List<String>.from(_archivedClients);
  }

  @override
  RxMap<String, List<OrderModel>> getClientsWithOrders() {
    return _groupedOrders;
  }

  void _updateOrderLists() {
    _activeOrders.assignAll(_groupedOrders.entries
        .where((e) => !_archivedClients.contains(e.key))
        .toList());
    _archivedOrders.assignAll(_groupedOrders.entries
        .where((e) => _archivedClients.contains(e.key))
        .toList());
  }

  @override
  RxList<MapEntry<String, List<OrderModel>>> getActiveClientsWithOrders() {
    return _activeOrders;
  }

  @override
  RxList<MapEntry<String, List<OrderModel>>> getArchivedClientsWithOrders() {
    return _archivedOrders;
  }

  @override
  Future<void> refreshOrders(List<String> routesId) async {
    DateTime now = DateTime.now();
    now = DateTime(now.year, now.month, now.day);

    List<OrderModel> orders = await _orderRepository.getOrdersFromCache();
    orders = orders
        .where((order) =>
            routesId.contains(order.routeId) &&
            !order.isPaid &&
            !order.isJoined &&
            !order.isDeleted &&
            (
                // pedidos marcados para depois e não foi hoje, foi antes de hoje
                (order.isMarked &&
                        order.dayMarkingItems.last.after &&
                        order.dayMarkingItems.last.createdAt.isBefore(now)) ||
                    // pedidos marcados para hoje
                    (order.isMarked &&
                        !order.dayMarkingItems.last.after &&
                        !order.dayMarkingItems.last.otherMonth &&
                        !order.dayMarkingItems.last.vaiFazerPix &&
                        !order.dayMarkingItems.last.vaiLigar &&
                        DateTimeHelper.isSameDay(
                            order.dayMarkingItems.last.dayToVisit!, now)) ||
                    // pedidos marcados para outro mês e criados a mais de 30 dias
                    (order.isMarked &&
                        !order.dayMarkingItems.last.after &&
                        !order.dayMarkingItems.last.vaiFazerPix &&
                        !order.dayMarkingItems.last.vaiLigar &&
                        order.dayMarkingItems.last.otherMonth &&
                        order.dayMarkingItems.last.createdAt.isBefore(
                            now.subtract(const Duration(days: 30)))) ||
                    // pedidos marcados para fazer pix e não foi hoje, foi antes de hoje
                    (order.isMarked &&
                        order.dayMarkingItems.last.vaiFazerPix &&
                        order.dayMarkingItems.last.createdAt.isBefore(now)) ||
                    // pedidos marcados para ligar e não foi hoje, foi antes de hoje
                    (order.isMarked &&
                        order.dayMarkingItems.last.vaiLigar &&
                        order.dayMarkingItems.last.createdAt.isBefore(now)) ||
                    // pedidos não marcados com mais de 30 dias
                    (!order.isMarked &&
                        order.date
                            .isBefore(now.subtract(const Duration(days: 30))))))
        .toList();

    final geolocationService = Get.find<GeolocationService>();
    final location = await geolocationService.getCurrentLocation();
    orders.sort((a, b) {
      if ((a.clientLongitude == null || a.clientLatitude == null) &&
          (b.clientLongitude == null || b.clientLatitude == null)) {
        return 0;
      } else if ((a.clientLongitude == null || a.clientLatitude == null) &&
          (b.clientLongitude != null && b.clientLatitude != null)) {
        return -1;
      } else if ((a.clientLongitude != null && a.clientLatitude != null) &&
          (b.clientLongitude == null || b.clientLatitude == null)) {
        return 1;
      }

      final distanceA = a.distanceTo(location);
      final distanceB = b.distanceTo(location);
      if (distanceA == null || distanceB == null) {
        return -1;
      }
      return distanceA.compareTo(distanceB);
    });

    final Map<String, List<OrderModel>> map = {};
    for (var order in orders) {
      map.putIfAbsent(order.clientId, () => []).add(order);
    }
    _groupedOrders.assignAll(map);
    _updateOrderLists();
  }
}
