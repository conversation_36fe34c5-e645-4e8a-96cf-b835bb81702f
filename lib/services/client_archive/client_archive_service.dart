import 'package:fl_app/models/order_model.dart';
import 'package:get/get.dart';

abstract class ClientArchiveService {
  Future<void> initialize(String boxName);
  Future<void> archiveClient(String clientId);
  Future<void> unarchiveClient(String clientId);
  bool isClientArchived(String clientId);
  List<String> getArchivedClientIds();
  RxMap<String, List<OrderModel>> getClientsWithOrders();
  RxList<MapEntry<String, List<OrderModel>>> getActiveClientsWithOrders();
  RxList<MapEntry<String, List<OrderModel>>> getArchivedClientsWithOrders();
  Future<void> refreshOrders(List<String> routesId);
}
