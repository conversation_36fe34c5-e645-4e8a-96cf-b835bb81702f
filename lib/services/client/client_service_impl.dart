import 'dart:developer';

import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';

import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import './client_service.dart';

class ClientServiceImpl implements ClientService {
  late ClientRepository _repository;
  late GeolocationService _geolocationService;
  late OrderRepository _orderRepository;
  late OfflineSyncRepositoryImpl _offlineSyncRepository;

  ClientServiceImpl({
    required ClientRepository clientRepository,
    required GeolocationService geolocationService,
    required OrderRepository orderRepository,
    required OfflineSyncRepositoryImpl offlineSyncRepository,
  }) {
    _repository = clientRepository;
    _geolocationService = geolocationService;
    _orderRepository = orderRepository;
    _offlineSyncRepository = offlineSyncRepository;
  }

  @override
  Future<List<ClientModel>> getClients(String? rotaId) async {
    final result = _repository.getClients(rotaId);
    return result;
  }

  @override
  Future<List<ClientModel>> getClientsFromCache(String? rotaId) async {
    return await _repository.getClientsFromCache(rotaId);
  }

  @override
  Future<ClientModel?> getClient(String id) async {
    return _repository.getById(id);
  }

  @override
  Future<ClientModel> addClient(ClientModel client) async {
    final result = await _repository.create(client);
    _offlineSyncRepository.addClientToSync(result);
    return result;
  }

  @override
  Future<ClientModel> updateClient(ClientModel client) async {
    final result = await _repository.update(client);
    await _orderRepository.updateInfoFromClientOrders(result);
    _offlineSyncRepository.addClientToSync(result);
    return result;
  }

  @override
  Future<void> deleteClient(ClientModel client) async {
    await _repository.delete(client);
    var findClient = await getClient(client.id!);
    if (findClient != null) {
      _offlineSyncRepository.addClientToSync(findClient);
    }
  }

  @override
  Future<List<ClientModel>> getClientsNearby(
      Position? currentPosition, List<String> rotasId,
      {int limit = 15}) async {
    List<ClientModel> clients = [];
    if (rotasId.isEmpty) {
      clients = await getClientsFromCache(null);
    } else {
      for (var rotaId in rotasId) {
        final clientsTemp = await getClientsFromCache(rotaId);
        clients.addAll(clientsTemp);
      }
    }
    if (currentPosition != null) {
      sortByDistance(
          clients, LatLng(currentPosition.latitude, currentPosition.longitude));
      return clients
          .sublist(0, clients.length > limit ? limit : clients.length)
          .obs;
    } else {
      try {
        final currentLocation = await _geolocationService.getCurrentLocation();
        sortByDistance(clients,
            LatLng(currentLocation!.latitude, currentLocation.longitude));
        return clients
            .sublist(0, clients.length > limit ? limit : clients.length)
            .obs;
      } catch (e) {
        log('Erro ao obter localização atual: $e');
        return clients;
      }
    }
  }

  void sortByDistance(List<ClientModel> clients, LatLng currentLocation) {
    clients.sort((a, b) {
      if (a.latitude == null || a.longitude == null) {
        return 1;
      }
      if (b.latitude == null || b.longitude == null) {
        return -1;
      }
      final distanceA = _geolocationService.calculateDistance(
          LatLng(currentLocation.latitude, currentLocation.longitude),
          LatLng(a.latitude!, a.longitude!));
      final distanceB = _geolocationService.calculateDistance(
          LatLng(currentLocation.latitude, currentLocation.longitude),
          LatLng(b.latitude!, b.longitude!));
      return distanceA.compareTo(distanceB);
    });
  }

  @override
  ClientModel? getClientFromCache(String id) {
    return _repository.getClientFromCache(id);
  }

  @override
  Future<List<OrderModel>> getOrdersFromClient(String clientId) async {
    var orders = await _orderRepository.getOrdersFromCache();
    orders = orders.where((order) => order.clientId == clientId).toList().obs;
    orders.sort((a, b) => a.date.compareTo(b.date));
    return orders;
  }

  @override
  Future<Map<ClientModel, List<OrderModel>>> getClientsOrders(
      List<String> routesIds,
      {bool removeTodayOrders = false}) async {
    String? routeId = routesIds.isEmpty
        ? null
        : routesIds.length == 1
            ? routesIds.first
            : null;
    final clients = await getClientsFromCache(routeId);
    final orders = await _orderRepository.getOrdersFromCache(routeId: routeId);
    if (routesIds.isNotEmpty && routesIds.length > 1) {
      orders.removeWhere((order) => !routesIds.contains(order.routeId));
    }
    Map<ClientModel, List<OrderModel>> clientsOrders = {};

    for (var client in clients) {
      clientsOrders.addAll({
        client: orders.where((order) => order.clientId == client.id).toList()
      });
    }
    return clientsOrders;
  }

  @override
  Future<void> syncClient(ClientModel clientModel) async {
    await _repository.syncClient(clientModel);
  }

  @override
  Future<void> changeRoute(List<ClientModel> clients, String routeId) async {
    for (var client in clients) {
      client.routeId = routeId;
      await updateClient(client);
    }
  }
}
