import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:geolocator/geolocator.dart';

abstract class ClientService {
  Future<List<ClientModel>> getClients(String? rotaId);
  Future<ClientModel?> getClient(String id);
  ClientModel? getClientFromCache(String id);
  Future<List<ClientModel>> getClientsNearby(
      Position? currentPosition, List<String> rotasId,
      {int limit = 15});
  Future<ClientModel> addClient(ClientModel client);
  Future<ClientModel> updateClient(ClientModel client);
  Future<void> deleteClient(ClientModel client);
  Future<List<ClientModel>> getClientsFromCache(String? rotaId);
  Future<List<OrderModel>> getOrdersFromClient(String clientId);
  Future<Map<ClientModel, List<OrderModel>>> getClientsOrders(
      List<String> routesIds);

  Future<void> syncClient(ClientModel clientModel);
  Future<void> changeRoute(List<ClientModel> clients, String routeId);
}
