import 'dart:developer';

import 'package:fl_app/repositories/app_state/app_state_repository.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';

class AppStateServiceImpl implements AppStateService {
  late AppStateRepository _repository;

  AppStateServiceImpl({required AppStateRepository appStateRepository}) {
    _repository = appStateRepository;
  }

  @override
  Future<void> startSaleInRoute(String routeId) async {
    log('iniciando venda na rota $routeId');
    return await _repository.startSaleInRoute(routeId);
  }

  @override
  Future<void> finishSaleInRoute() async {
    log('finalizando venda na rota');
    return await _repository.finishSaleInRoute();
  }

  @override
  Future<void> enableSecondRoute(String routeId, DateTime date) async {
    log('habilitando segunda rota $routeId');
    return await _repository.enableSecondRoute(routeId, date);
  }

  @override
  Future<void> disableSecondRoute() async {
    log('desabilitando segunda rota');
    return await _repository.disableSecondRoute();
  }

  @override
  AppState getAppState() {
    return _repository.getAppState();
  }

  @override
  bool isSelling() {
    final result = _repository.getAppState();
    return result.isSelling;
  }

  @override
  String? getIsSellingRouteId() {
    final result = _repository.getAppState();
    return result.isSellingRouteId;
  }

  @override
  Future<void> startCobrancaInRoutes(List<String> routes) async {
    log('iniciando cobrança nas rotas $routes');
    return await _repository.startCobrancaInRoutes(routes);
  }

  @override
  Future<void> finishCobrancaInRoute() async {
    log('finalizando cobrança na rota');
    return await _repository.finishCobrancaInRoute();
  }

  @override
  bool isCobrando() {
    final result = getCobrancaRouteIds();
    return result != null;
  }

  @override
  List<String>? getCobrancaRouteIds() {
    return _repository.getCobrancaRouteIds();
  }

  @override
  bool isKeepAlive() {
    return _repository.isKeepAlive();
  }

  @override
  void setKeepAlive(bool value) {
    _repository.setKeepAlive(value);
  }
}
