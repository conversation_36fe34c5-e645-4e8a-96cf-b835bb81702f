import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';

abstract interface class AppStateService {
  Future<void> startSaleInRoute(String routeId);

  Future<void> enableSecondRoute(String routeId, DateTime date);
  Future<void> disableSecondRoute();

  AppState getAppState();
  Future<void> finishSaleInRoute();
  bool isSelling();
  String? getIsSellingRouteId();

  Future<void> startCobrancaInRoutes(List<String> routes);
  Future<void> finishCobrancaInRoute();
  bool isCobrando();

  List<String>? getCobrancaRouteIds();

  bool isKeepAlive();

  void setKeepAlive(bool value);
}
