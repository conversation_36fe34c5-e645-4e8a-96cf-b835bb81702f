import 'package:fl_app/models/sale_route_model.dart';
import 'package:get/get.dart';

abstract class SalesRoutesService {
  Future<void> addSaleRoute(SaleRouteModel saleRouteModel);
  Future<void> deleteSaleRoute(SaleRouteModel saleRouteModel);
  Future<RxList<SaleRouteModel>> getSalesRoutes();
  Future<void> updateSaleRoute(SaleRouteModel saleRouteModel);

  Future<RxList<SaleRouteModel>> getSalesRoutesFromCache();

  SaleRouteModel? getRouteById(String id);
}
