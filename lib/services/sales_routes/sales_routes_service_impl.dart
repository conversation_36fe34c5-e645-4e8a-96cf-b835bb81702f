import 'package:fl_app/models/sale_route_model.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';
import 'package:get/get.dart';

import './sales_routes_service.dart';

class SalesRoutesServiceImpl implements SalesRoutesService {
  late SalesRoutesRepository _repository;

  SalesRoutesServiceImpl(
      {required SalesRoutesRepository salesRoutesRepository}) {
    _repository = salesRoutesRepository;
  }

  @override
  Future<void> addSaleRoute(SaleRouteModel saleRouteModel) async {
    final result = await _repository.addSaleRoute(saleRouteModel);
    return result;
  }

  @override
  Future<void> deleteSaleRoute(SaleRouteModel saleRouteModel) async {
    final result = await _repository.deleteSaleRoute(saleRouteModel);

    return result;
  }

  @override
  Future<RxList<SaleRouteModel>> getSalesRoutes() async {
    final result = await _repository.getSalesRoutes();
    return result.where((p0) => !p0.isDeleted).toList().obs;
  }

  @override
  Future<RxList<SaleRouteModel>> getSalesRoutesFromCache() async {
    return _repository
        .getSalesRoutesFromCache()
        .where((p0) => !p0.isDeleted)
        .toList()
        .obs;
  }

  @override
  Future<void> updateSaleRoute(SaleRouteModel saleRouteModel) async {
    final result = await _repository.updateSaleRoute(saleRouteModel);
    return result;
  }

  @override
  SaleRouteModel? getRouteById(String id) {
    final salesRoutesCache = _repository.getSalesRoutesFromCache();
    return salesRoutesCache.firstWhereOrNull((element) => element.id == id);
  }
}
