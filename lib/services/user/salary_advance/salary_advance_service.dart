import 'package:fl_app/models/salary_advance.dart';

abstract class SalaryAdvanceService {
  Future<List<SalaryAdvance>> getSalaryAdvances(String userId);
  Future<List<SalaryAdvance>> getSalaryAdvancesInMonth(String userId);
  Future<List<SalaryAdvance>> getSalaryAdvancesCobradorFixo(String userId);

  Future<SalaryAdvance> addSalaryAdvance(SalaryAdvance salaryAdvance);

  Future<void> deleteSalaryAdvance(SalaryAdvance salaryAdvance);
}
