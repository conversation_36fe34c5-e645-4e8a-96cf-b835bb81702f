import 'package:fl_app/models/salary_advance.dart';
import 'package:fl_app/repositories/user/salary_advance/salary_advance_repository.dart';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:fl_app/services/user/salary_advance/salary_advance_service.dart';
import 'package:get/get.dart';
import 'package:once/once.dart';

class SalaryAdvanceServiceImpl implements SalaryAdvanceService {
  late SalaryAdvanceRepository _repository;

  SalaryAdvanceServiceImpl({
    required SalaryAdvanceRepository salaryAdvanceRepository,
  }) {
    _repository = salaryAdvanceRepository;
    init();
  }

  void init() async {
    Once.runCustom(
      'verifyOldSalaryAdvances90days',
      callback: () async {
        await verifyOldSalaryAdvances();
      },
      duration: const Duration(days: 90),
    );
  }

  @override
  Future<List<SalaryAdvance>> getSalaryAdvances(String userId) async {
    final result = await _repository.getSalaryAdvancesForUser(userId);
    return result;
  }

  @override
  Future<List<SalaryAdvance>> getSalaryAdvancesInMonth(String userId) async {
    final result = await _repository.getSalaryAdvancesForUser(userId);
    DateTime initialDate = DateTime.now();

    var initialFinalDay = Get.find<SettingsService>().getInitialFinalDay();
    var now = DateTime.now();
    if (now.day > initialFinalDay) {
      initialDate = DateTime(now.year, now.month, initialFinalDay);
    } else {
      initialDate = DateTime(now.year, now.month - 1, initialFinalDay);
    }
    return result.where((x) => x.date.isAfter(initialDate)).toList();
  }

  @override
  Future<List<SalaryAdvance>> getSalaryAdvancesCobradorFixo(
      String userId) async {
    final result = await _repository.getSalaryAdvancesForUser(userId);
    DateTime initialDate = DateTime.now();

    var now = DateTime.now();
    initialDate = DateTime(now.year, now.month, 1);
    return result.where((x) => x.date.isAfter(initialDate)).toList();
  }

  @override
  Future<SalaryAdvance> addSalaryAdvance(SalaryAdvance salaryAdvance) async {
    final result = await _repository.create(salaryAdvance);
    return result;
  }

  @override
  Future<void> deleteSalaryAdvance(SalaryAdvance salaryAdvance) async {
    await _repository.delete(salaryAdvance);
  }

  Future verifyOldSalaryAdvances() async {
    var salaryAdvances = _repository.cachedSalaryAdvances;
    var pastDate = DateTime.now().subtract(const Duration(days: 90));
    var oldSalaryAdvances =
        salaryAdvances.where((x) => x.date.isBefore(pastDate)).toList();
    for (var salaryAdvance in oldSalaryAdvances) {
      await _repository.delete(salaryAdvance);
    }
  }
}
