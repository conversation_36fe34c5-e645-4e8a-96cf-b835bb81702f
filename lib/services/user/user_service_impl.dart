import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_information/device_information.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:get/get.dart';

class UserServiceImpl implements UserService {
  final UserRepository _userRepository;
  late UserModel _user;

  RxBool isInitialized = false.obs;

  UserServiceImpl({
    required UserRepository userRepository,
  }) : _userRepository = userRepository;

  @override
  bool isAppInitialized() {
    return isInitialized.value;
  }

  @override
  void setAppInitialized(bool value) {
    isInitialized.value = value;
  }

  @override
  Future<void> login() async {
    try {
      final credentials = await _userRepository.login();
      _user = UserModel(
        id: credentials.user!.uid,
        name: credentials.user!.displayName!,
        email: credentials.user!.email!,
        image: credentials.user!.photoURL!,
        lastModified: Timestamp.now(),
        phoneModel: await DeviceInformation.deviceModel,
      );

      final users = await _userRepository.getUsers();
      var user = users.firstWhereOrNull((u) => u.id == _user.id);
      if (user == null) {
        await _userRepository.addUser(_user);
      } else {
        user.phoneModel = await DeviceInformation.deviceModel;
        await _userRepository.updateUser(user);
      }

      //Restart.restartApp();
    } catch (e) {
      log('Erro ao realizar login: $e');
      throw Exception('Erro ao realizar login: $e');
    }
  }

  @override
  Future<void> logout() async {
    try {
      await refreshAllData();
      await _userRepository.logout();
      //await resetCache();
    } catch (e) {
      log('Erro ao realizar logout: $e');
      throw Exception('Erro ao realizar logout: $e');
    }
  }

  @override
  Future<List<UserModel>> getUsers() async {
    try {
      return await _userRepository.getUsersFromCache();
    } catch (e) {
      log('Erro ao obter lista de usuários: $e');
      throw Exception('Erro ao obter lista de usuários: $e');
    }
  }

  @override
  Future<UserModel?> getUser(String id) async {
    try {
      return await _userRepository.getUser(id);
    } catch (e) {
      log('Erro ao obter usuário por ID: $e');
      throw Exception('Erro ao obter usuário por ID: $e');
    }
  }

  @override
  Future<UserModel> addUser(UserModel user) async {
    try {
      return await _userRepository.addUser(user);
    } catch (e) {
      log('Erro ao adicionar usuário: $e');
      throw Exception('Erro ao adicionar usuário: $e');
    }
  }

  @override
  Future<UserModel> updateUser(UserModel user) async {
    try {
      await _userRepository.updateUser(user);
      return user;
    } catch (e) {
      log('Erro ao atualizar usuário: $e');
      throw Exception('Erro ao atualizar usuário: $e');
    }
  }

  @override
  Future<void> deleteUser(UserModel user) async {
    try {
      await _userRepository.deleteUser(user);
    } catch (e) {
      log('Erro ao excluir usuário: $e');
      throw Exception('Erro ao excluir usuário: $e');
    }
  }

  @override
  Future<bool> isLogged() async {
    try {
      return await _userRepository.isLogged();
    } catch (e) {
      log('Erro ao verificar se usuário está logado: $e');
      throw Exception('Erro ao verificar se usuário está logado: $e');
    }
  }

  @override
  Future<List<UserModel>> getUsersFromCache() async {
    return _userRepository.getUsersFromCache();
  }

  @override
  Future<UserModel?> getUserAuthenticated() async {
    return await _userRepository.getUserAuthenticated();
  }

  @override
  Future<UserModel?> reloadUserAuthenticated() async {
    return await _userRepository.reloadUserAuthenticated();
  }

  Future<void> refreshAllData() async {
    OrderRepository orderRepository = Get.find();
    ClientRepository clientRepository = Get.find();
    ProductRepository productRepository = Get.find();
    SalesRoutesRepository salesRoutesRepository = Get.find();

    await orderRepository.getOrders();
    await clientRepository.getClients(null);
    await productRepository.getProducts();
    await salesRoutesRepository.getSalesRoutes();
  }
}
