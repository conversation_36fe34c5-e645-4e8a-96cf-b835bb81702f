import 'package:fl_app/models/user_model.dart';

abstract class UserService {
  Future<void> login();
  Future<void> logout();
  Future<List<UserModel>> getUsers();
  Future<UserModel?> getUser(String id);
  Future<UserModel> addUser(UserModel user);
  Future<UserModel> updateUser(UserModel user);
  Future<void> deleteUser(UserModel user);
  Future<bool> isLogged();

  Future<List<UserModel>> getUsersFromCache();

  Future<UserModel?> getUserAuthenticated();

  Future<UserModel?> reloadUserAuthenticated();

  bool isAppInitialized();

  void setAppInitialized(bool value);
}
