import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/repositories/cart/cart_repository.dart';
import 'package:get/get.dart';

import './cart_service.dart';

class CartServiceImpl implements CartService {
  final CartRepository _repository;

  CartServiceImpl({required CartRepository cartRepository})
      : _repository = cartRepository {
    init();
  }

  final _shoppingCarts = <ShoppingCart>[].obs;

  init() async {
    final shoppingCarts = await _repository.getShoppingCarts();
    _shoppingCarts.addAll(shoppingCarts);
    _shoppingCarts.refresh();
  }

  @override
  Future<ShoppingCart> createShoppingCart() async {
    final newShoppingCart = ShoppingCart();
    _shoppingCarts.add(newShoppingCart);
    _repository.saveShoppingCart(newShoppingCart);
    return newShoppingCart;
  }

  @override
  Future<ShoppingCart?> getShoppingCart(String id) async {
    final shoppingCart =
        _shoppingCarts.firstWhereOrNull((element) => element.id == id);
    return shoppingCart;
  }

  @override
  RxList<ShoppingCart> getShoppingCarts() {
    return _shoppingCarts;
  }

  @override
  Future<void> removeShoppingCart(ShoppingCart e) async {
    _shoppingCarts.remove(e);
    _repository.removeShoppingCart(e);
  }

  @override
  Future<void> saveShoppingCart(ShoppingCart shoppingCart) async {
    _repository.saveShoppingCart(shoppingCart);
  }
}
