import 'dart:developer';

import 'package:fl_app/main.dart';
import 'package:fl_app/repositories/settings/settings_repository.dart';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:restart_app/restart_app.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';

class SettingsServiceImpl implements SettingsService {
  final SettingsRepository _repository;

  SettingsServiceImpl(this._repository) {
    init();
  }

  void init() async {
    await Future.delayed(const Duration(seconds: 30));
    try {
      if (isShorebirdAvailable) {
        var status = await checkForShorebirdUpdate();
        if (status == UpdateStatus.outdated) {
          await applyShorebirdUpdate();
        }
        status = await checkForShorebirdUpdate();
        if (status == UpdateStatus.restartRequired) {
          log('Reinicie o aplicativo para aplicar a atualização.');
          Get.showSnackbar(GetSnackBar(
            message: 'Reinicie o aplicativo para aplicar a atualização.',
            duration: const Duration(seconds: 25),
            backgroundColor: Colors.deepOrange,
            icon: const Icon(Icons.info),
            mainButton: TextButton(
              onPressed: () {
                Restart.restartApp();
              },
              child: const Text('Reiniciar'),
            ),
            isDismissible: true,
          ));
        }
      }
    } catch (e) {
      log('Erro ao verificar atualização: $e');
    }
  }

  @override
  Future<String> getAdminPassword() async {
    return await _repository.getAdminPassword();
  }

  @override
  Future<void> setAdminPassword(String password) async {
    await _repository.setAdminPassword(password);
  }

  @override
  void setInitialFinalDay(int day) {
    _repository.setInitialFinalDay(day);
  }

  @override
  int getInitialFinalDay() {
    return _repository.getInitialFinalDay();
  }

  @override
  Future<void> togleDarkMode() async {
    await _repository.togleDarkMode();
  }

  @override
  Future<bool> getDarkMode() async {
    bool result = await _repository.getDarkMode();
    if (result) {
      Get.changeThemeMode(ThemeMode.dark);
    } else {
      Get.changeThemeMode(ThemeMode.light);
    }
    return result;
  }

  @override
  Future<void> applyShorebirdUpdate() async {
    shorebirdUpdater.update();
  }

  @override
  Future<UpdateStatus> checkForShorebirdUpdate() async {
    return shorebirdUpdater.checkForUpdate();
  }

  @override
  Future<String> getPubspecVersion() async {
    final contents = await rootBundle.loadString('pubspec.yaml');
    final lines = contents.split('\n');

    for (var line in lines) {
      if (line.startsWith('version:')) {
        final version = line.split(':')[1].trim();
        return version;
      }
    }

    return '-';
  }

  @override
  bool get isShorebirdAvailable => shorebirdUpdater.isAvailable;

  @override
  Future<Patch?> readCurrentPatch() async {
    return shorebirdUpdater.readCurrentPatch();
  }
}
