import 'package:shorebird_code_push/shorebird_code_push.dart';

abstract class SettingsService {
  Future<String> getAdminPassword();
  Future<void> setAdminPassword(String password);
  void setInitialFinalDay(int day);
  int getInitialFinalDay();
  Future<void> togleDarkMode();
  Future<bool> getDarkMode();

  bool get isShorebirdAvailable;
  //readCurrentPatch()
  Future<Patch?> readCurrentPatch();
  Future<String> getPubspecVersion();
  //checkForShorebirdUpdate()
  Future<UpdateStatus> checkForShorebirdUpdate();
  //applyShorebirdUpdate
  Future<void> applyShorebirdUpdate();
}
