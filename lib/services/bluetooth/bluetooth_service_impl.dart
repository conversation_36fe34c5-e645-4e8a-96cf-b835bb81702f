import 'package:fl_app/services/bluetooth/bluetooth_service.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';

class BluetoothServiceImpl implements BluetoothService {
  List<BluetoothInfo> availableBluetoothDevices = [];
  String mainPrinterMac = "";
  String mainPrinterName = "";

  Box? box;

  @override
  Future<void> disconnect() async {
    PrintBluetoothThermal.disconnect;
  }

  @override
  Future<String> getMainMac() async {
    if (mainPrinterMac == "") {
      box = await Hive.openBox('settings');
      mainPrinterMac = box!.get('mainPrinterMac') ?? "";
    }
    return mainPrinterMac;
  }

  @override
  Future<bool> isBluetoothEnabled() async {
    if (await PrintBluetoothThermal.bluetoothEnabled) {
      return true;
    } else {
      return false;
    }
  }

  @override
  Future<bool> isConnected() async {
    if (mainPrinterMac != "") {
      try {
        final result = await PrintBluetoothThermal.connect(
            macPrinterAddress: mainPrinterMac);
        if (result) {
          await PrintBluetoothThermal.disconnect;
          return true;
        }
        return false;
      } catch (e) {
        return false;
      }
    }

    return PrintBluetoothThermal.connectionStatus;
  }

  @override
  Future<bool> setConnect(String mac, String name) async {
    try {
      final result =
          await PrintBluetoothThermal.connect(macPrinterAddress: mac);
      if (result) {
        await PrintBluetoothThermal.disconnect;
        setMainMac(mac);
        setMainName(name);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> setMainMac(String mac) async {
    box = await Hive.openBox('settings');
    box!.put('mainPrinterMac', mac);
    mainPrinterMac = mac;
  }

  @override
  Future<void> setMainName(String name) async {
    box = await Hive.openBox('settings');
    box!.put('mainPrinterName', name);
    mainPrinterName = name;
  }

  @override
  Future<List<BluetoothInfo>> getAvailableBluetoothDevices() async {
    availableBluetoothDevices = await PrintBluetoothThermal.pairedBluetooths;
    return availableBluetoothDevices;
  }

  @override
  Future<String> getMainName() async {
    if (mainPrinterName == "") {
      box = await Hive.openBox('settings');
      mainPrinterName = box!.get('mainPrinterName') ?? "";
    }
    return mainPrinterName;
  }

  @override
  Future<int> getBaterryLevel() async {
    return await PrintBluetoothThermal.batteryLevel;
  }

  @override
  Future<bool> print(List<int> bytes) async {
    await PrintBluetoothThermal.connect(macPrinterAddress: mainPrinterMac);
    final result = await PrintBluetoothThermal.writeBytes(bytes);
    if (!result) {
      return false;
    }
    await Future.delayed(const Duration(seconds: 6)).then((value) async {
      await PrintBluetoothThermal.disconnect;
    });
    return result;
  }
}
