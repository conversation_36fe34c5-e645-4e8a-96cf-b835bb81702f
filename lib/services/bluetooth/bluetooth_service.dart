import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';

abstract class BluetoothService {
  Future<bool> isBluetoothEnabled();
  Future<bool> isConnected();
  Future<String> getMainMac();
  Future<String> getMainName();
  Future<bool> setConnect(String mac, String name);
  Future<void> disconnect();
  Future<void> setMainMac(String mac);
  Future<void> setMainName(String name);
  Future<List<BluetoothInfo>> getAvailableBluetoothDevices();
  Future<int> getBaterryLevel();
  Future<bool> print(List<int> bytes);
}
