import 'package:fl_app/services/connection/connection_service.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

class ConnectionServiceImpl implements ConnectionService {
  late InternetConnection internetConnection;

  ConnectionServiceImpl() {
    internetConnection = InternetConnection.createInstance(
      checkInterval: const Duration(seconds: 10),
    );
  }

  @override
  Future<bool> checkConnection() async {
    bool result = await internetConnection.hasInternetAccess;
    return result;
  }

  @override
  Future<Stream<InternetStatus>> getListenable() async {
    return internetConnection.onStatusChange;
  }
}
