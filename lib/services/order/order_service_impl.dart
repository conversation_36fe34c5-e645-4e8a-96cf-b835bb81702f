// import 'dart:developer';

// import 'package:fl_app/application/helpers/date_time_helper.dart';
// import 'package:fl_app/models/client_model.dart';
// import 'package:fl_app/models/order_model.dart';
// import 'package:fl_app/models/sale_route_model.dart';
// import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
// import 'package:fl_app/repositories/order/order_repository.dart';
// import 'package:fl_app/services/client/client_service.dart';
// import 'package:fl_app/services/geolocation/geolocation_service.dart';
// import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
// import 'package:get/get.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';

// import './order_service.dart';

// class OrderRepositoryImpl implements OrderRepository {
//   late OrderRepository _repository;
//   late OfflineSyncRepositoryImpl _offlineSyncRepository;

//   OrderRepositoryImpl({
//     required OrderRepository orderRepository,
//     required OfflineSyncRepositoryImpl offlineSyncRepository,
//   }) {
//     _repository = orderRepository;
//     _offlineSyncRepository = offlineSyncRepository;
//   }

//   @override
//   Future<List<OrderModel>> getOrders({
//     String? routeId,
//     DateTime? initialDate,
//     DateTime? finalDate,
//     bool? showToDelivery,
//     bool? showPaidOrders,
//     bool ascending = true,
//     String? sellerId,
//   }) async {
//     final result = await _repository.getOrders();
//     result.removeWhere((order) =>
//         (routeId != null && routeId != "" && order.routeId != routeId) ||
//         (initialDate != null && order.date.isBefore(initialDate)) ||
//         (finalDate != null &&
//             order.date.isAfter(finalDate.add(const Duration(days: 1)))) ||
//         (showToDelivery != null && order.toDelivery != showToDelivery) ||
//         (showPaidOrders != null && order.isPaid != showPaidOrders) ||
//         (sellerId != null && sellerId != "" && order.userId != sellerId) ||
//         order.isDeleted);

//     result.sort((a, b) =>
//         ascending ? a.date.compareTo(b.date) : b.date.compareTo(a.date));

//     return result;
//   }

//   @override
//   Future<OrderModel?> getOrder(String id) async {
//     return _repository.getOrder(id);
//   }

//   @override
//   Future<OrderModel> addOrder(OrderModel order) async {
//     final result = await _repository.addOrder(order);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> updateOrder(OrderModel order) async {
//     final result = await _repository.updateOrder(order);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<void> deleteOrder(OrderModel order) async {
//     await _repository.deleteOrder(order);
//     _offlineSyncRepository
//         .addOrderToSync(await _repository.getOrder(order.id!));
//   }

//   @override
//   Future<OrderModel> restoreOrder(OrderModel order) async {
//     final result = await _repository.restoreOrder(order);
//     _offlineSyncRepository
//         .addOrderToSync(await _repository.getOrder(order.id!));
//     return result;
//   }

//   @override
//   Future<List<OrderModel>> getTodaySaleOrders(String rotaId) async {
//     final cachedOrders = await getOrdersFromCache();
//     if (cachedOrders.isEmpty) {
//       await getOrders();
//     }

//     return cachedOrders
//         .where((order) =>
//             isSameDate(order.date, DateTime.now()) &&
//             (order.routeId == rotaId || order.routeSaleId == rotaId))
//         .toList()
//         .obs;
//   }

//   @override
//   Future<Map<DateTime, List<OrderModel>>> getOrdersFromRouteByDate(
//       String routeId) async {
//     List<OrderModel> orders = await getOrdersFromCache(routeId: routeId);
//     Map<DateTime, List<OrderModel>> ordersByDate = {};
//     for (var order in orders) {
//       DateTime date = DateTime(
//         order.date.year,
//         order.date.month,
//         order.date.day,
//       );
//       if (ordersByDate.containsKey(date)) {
//         ordersByDate[date]!.add(order);
//       } else {
//         ordersByDate[date] = [order];
//       }
//     }

//     ordersByDate.forEach((key, value) {
//       value.sort((a, b) => a.date.compareTo(b.date));
//     });

//     return ordersByDate;
//   }

//   bool isSameDate(DateTime date1, DateTime date2) {
//     return date1.year == date2.year &&
//         date1.month == date2.month &&
//         date1.day == date2.day;
//   }

//   @override
//   Future<List<OrderModel>> getOrdersFromCache(
//       {String? routeId,
//       DateTime? initialDate,
//       DateTime? finalDate,
//       bool? showToDelivery,
//       bool? showPaidOrders,
//       bool ascending = true,
//       String? sellerId}) async {
//     final orders = await _repository.getOrdersFromCache();

//     if (finalDate != null) {
//       finalDate = DateTime(
//         finalDate.year,
//         finalDate.month,
//         finalDate.day,
//         23,
//         59,
//         59,
//       );
//     }

//     if (initialDate != null) {
//       initialDate = DateTime(
//         initialDate.year,
//         initialDate.month,
//         initialDate.day,
//         0,
//         0,
//         0,
//       );
//     }
//     orders.removeWhere(
//       (order) =>
//           (initialDate != null && order.date.isBefore(initialDate)) ||
//           (finalDate != null && order.date.isAfter(finalDate)) ||
//           (showToDelivery != null && order.toDelivery != showToDelivery) ||
//           (showPaidOrders != null && order.isPaid != showPaidOrders) ||
//           (sellerId != null && sellerId != "" && order.userId != sellerId) ||
//           (order.isDeleted),
//     );
//     if (routeId != null && routeId.isNotEmpty) {
//       return orders
//           .where((e) => e.routeId == routeId || e.routeSaleId == routeId)
//           .toList();
//     }
//     return orders;
//   }

//   @override
//   Future<List<OrderModel>> getDeletedOrdersFromCache({String? routeId}) async {
//     return await _repository.getDeletedOrdersFromCache(routeId: routeId);
//   }

//   @override
//   Future<List<Map<String, Map<ClientModel, List<OrderModel>>>>>
//       getOrdersByStreet(String routeId) async {
//     //[
//     //  {'rua', {'cliente': [pedidos]}},
//     //]

//     List<String> streets = <String>[].obs;

//     Map<ClientModel, List<OrderModel>> clientsOrders =
//         await Get.find<ClientService>().getClientsOrders([routeId]);

//     // remove pedidos de hoje
//     for (var client in clientsOrders.keys) {
//       clientsOrders[client]!.removeWhere((order) =>
//           isSameDate(order.date, DateTime.now()) ||
//           order.isPaid ||
//           order.dayMarkingItems.any(
//             (element) =>
//                 element.active &&
//                 (isSameDate(element.createdAt, DateTime.now()) ||
//                     (element.dayToVisit?.isAfter(DateTime.now()) != null &&
//                         element.dayToVisit!.isAfter(DateTime.now()))),
//           ));
//     }

//     for (var client in clientsOrders.keys) {
//       if (!streets.contains(client.address.trim())) {
//         streets.add(client.address.trim());
//       }
//     }

//     List<Map<String, Map<ClientModel, List<OrderModel>>>> ordersSortedByStreet =
//         <Map<String, Map<ClientModel, List<OrderModel>>>>[];

//     for (var street in streets) {
//       Map<String, Map<ClientModel, List<OrderModel>>> streetOrders = {};
//       Map<ClientModel, List<OrderModel>> clientsOrdersByStreet = {};
//       for (var client in clientsOrders.keys) {
//         if (client.address.trim() == street) {
//           clientsOrdersByStreet[client] = clientsOrders[client]!;
//         }
//       }
//       streetOrders[street] = clientsOrdersByStreet;
//       ordersSortedByStreet.add(streetOrders);
//     }

//     // remove clientes sem pedidos
//     for (var street in ordersSortedByStreet) {
//       street.forEach((key, value) {
//         value.removeWhere((key, value) => value.isEmpty);
//       });
//     }

//     // remove ruas sem pedidos
//     ordersSortedByStreet.removeWhere((element) => element.values.first.isEmpty);
//     final geoService = Get.find<GeolocationService>();
//     final location = await geoService.getCurrentLocation();

//     if (location != null) {
//       ordersSortedByStreet.sort((a, b) {
//         //pegar um cliente de cada rua que tenha localização
//         ClientModel clientA = a.values.first.keys.firstWhere(
//             (element) => element.latitude != null && element.longitude != null,
//             orElse: () => a.values.first.keys.first);
//         ClientModel clientB = b.values.first.keys.firstWhere(
//             (element) => element.latitude != null && element.longitude != null,
//             orElse: () => b.values.first.keys.first);

//         if (clientA.latitude == null || clientA.longitude == null) {
//           return 1;
//         }
//         if (clientB.latitude == null || clientB.longitude == null) {
//           return -1;
//         }
//         final distanceA = geoService.calculateDistance(
//             LatLng(clientA.latitude!, clientA.longitude!),
//             LatLng(location.latitude,
//                 location.longitude)); // Use a sua localização aqui
//         final distanceB = geoService.calculateDistance(
//             LatLng(clientB.latitude!, clientB.longitude!),
//             LatLng(location.latitude,
//                 location.longitude)); // Use a sua localização aqui
//         return distanceA.compareTo(distanceB);
//       });
//     }

//     return ordersSortedByStreet;
//   }

//   @override
//   Future<Map<SaleRouteModel, List<OrderModel>>> getOrdersDelivery() async {
//     final ordersDelivery = await _repository.getOrdersDelivery();
//     final SalesRoutesService salesRoutesService = Get.find();
//     final routes = await salesRoutesService.getSalesRoutesFromCache();
//     Map<SaleRouteModel, List<OrderModel>> ordersByRoute = {};

//     for (var route in routes) {
//       final ordersRoute = ordersDelivery
//           .where((order) => order.routeId == route.id)
//           .toList()
//           .obs;

//       if (ordersRoute.isNotEmpty) {
//         ordersByRoute[route] = ordersRoute;
//       }
//     }

//     return ordersByRoute;
//   }

//   @override
//   Future<List<OrderModel>> getMarkedOrders() async {
//     final orders = await _repository.getOrdersFromCache();
//     return orders
//         .where((order) =>
//             order.dayMarkingItems.any((element) => element.active) &&
//             !order.isPaid)
//         .toList();
//   }

//   @override
//   Future<List<OrderModel>> getPendingOrders() async {
//     final orders = await _repository.getOrdersFromCache();
//     return orders
//         .where((order) =>
//             (order.dayMarkingItems.any((element) => element.active) ||
//                 order.date.isBefore(
//                     DateTime.now().subtract(const Duration(days: 30)))) &&
//             !order.isPaid &&
//             !order.isJoined)
//         .toList();
//   }

//   @override
//   Future<List<OrderModel>> getMarkedToTodayOrders(
//       List<String> cobradorRoutes) async {
//     final orders = await _repository.getOrdersFromCache();
//     return orders
//         .where((order) =>
//             cobradorRoutes.contains(order.routeId!) &&
//             order.dayMarkingItems.any((element) => element.active) &&
//             order.dayMarkingItems.last.dayToVisit != null &&
//             isSameDate(order.dayMarkingItems.last.dayToVisit!, DateTime.now()))
//         .toList();
//   }

//   @override
//   Future<List<OrderModel>> getOrdersReveived({
//     required List<String> routes,
//     required DateTime date,
//   }) async {
//     bool allRoutes = routes.isEmpty;
//     var now = DateTime.now();
//     final orders = await _repository.getOrdersFromCache();
//     return orders
//         .where((order) =>
//             //pedidos nas rotas ou todas as rotas
//             (routes.contains(order.routeId!) || allRoutes) &&
//             //pedidos com pagamento hoje
//             (order.payments.any((p) => isSameDate(p.date, now))))
//         .toList();
//   }

//   @override
//   Future<List<OrderModel>> getPendingOrdersToCobradorFixo(
//       List<String> cobradorRoutes, String routeId) async {
//     final markedOrders = await getPendingOrders();

//     DateTime now = DateTime.now();
//     now = DateTime(now.year, now.month, now.day);

//     var markedTodayTemp = markedOrders
//         .where((order) =>
//             (!order.dayMarkingItems.any((element) => element.active) &&
//                 order.date.isBefore(now) &&
//                 cobradorRoutes.contains(order.routeId!)) ||
//             (cobradorRoutes.contains(order.routeId!) &&
//                 (((!order.dayMarkingItems.last.otherMonth &&
//                         order.dayMarkingItems.last.dayToVisit != null &&
//                         order.dayMarkingItems.last.createdAt.isBefore(
//                             now.subtract(const Duration(days: 30))))) ||
//                     (order.dayMarkingItems.last.otherMonth &&
//                         order.dayMarkingItems.last.createdAt.isBefore(
//                             now.subtract(const Duration(days: 30)))) ||
//                     (order.dayMarkingItems.last.dayToVisit != null &&
//                         (DateTimeHelper.isSameDay(
//                                 order.dayMarkingItems.last.dayToVisit!, now) ||
//                             order.dayMarkingItems.last.dayToVisit!
//                                 .isBefore(now))) ||
//                     order.dayMarkingItems.last.after)))
//         .toList();

//     if (routeId.isNotEmpty) {
//       markedTodayTemp = markedTodayTemp
//           .where((element) => element.routeId == routeId)
//           .toList();
//     }

//     final geolocationService = Get.find<GeolocationService>();
//     final location = await geolocationService.getCurrentLocation();
//     markedTodayTemp.sort((a, b) {
//       if ((a.clientLongitude == null || a.clientLatitude == null) &&
//           (b.clientLongitude == null || b.clientLatitude == null)) {
//         return 0;
//       } else if ((a.clientLongitude == null || a.clientLatitude == null) &&
//           (b.clientLongitude != null && b.clientLatitude != null)) {
//         return -1;
//       } else if ((a.clientLongitude != null && a.clientLatitude != null) &&
//           (b.clientLongitude == null || b.clientLatitude == null)) {
//         return 1;
//       }

//       final distanceA = a.distanceTo(location);
//       final distanceB = b.distanceTo(location);
//       if (distanceA == null || distanceB == null) {
//         return -1;
//       }
//       return distanceA.compareTo(distanceB);
//     });

//     return markedTodayTemp;
//   }

//   @override
//   Future<void> updateInfoFromClientOrders(ClientModel client) async {
//     var ordersClient =
//         await Get.find<ClientService>().getOrdersFromClient(client.id!);
//     for (var order in ordersClient) {
//       if (!order.isPaid &&
//           !order.isJoined &&
//           (order.clientName != client.name ||
//               order.clientPhone != client.phoneNumber ||
//               order.clientAddress != client.address ||
//               order.clientNumber != client.number ||
//               order.clientLocalDescription != client.localDescription ||
//               order.clientLatitude != client.latitude ||
//               order.clientLongitude != client.longitude ||
//               order.routeId != client.routeId)) {
//         order.clientName = client.name;
//         order.clientPhone = client.phoneNumber;
//         order.clientAddress = client.address;
//         order.clientLocalDescription = client.localDescription;
//         order.clientNumber = client.number;
//         order.clientLatitude = client.latitude;
//         order.clientLongitude = client.longitude;
//         order.routeId = client.routeId;
//         order.routeName =
//             Get.find<SalesRoutesService>().getSaleRouteName(client.routeId);
//         await updateClientInfo(order);
//         log('Atualizado informações do cliente do pedido ${order.id}');
//       }
//     }
//   }

//   @override
//   Future<OrderModel> updateClientInfo(OrderModel order) async {
//     final result = await _repository.updateClientInfo(order);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> updatePayments(OrderModel order) async {
//     final result = await _repository.updatePayments(order);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> markAsDelivered(OrderModel order) async {
//     final result = await _repository.markAsDelivered(order);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> markAsPaid(
//       OrderModel order, PaymentMethod paymentMethod, DateTime date) async {
//     final result = await _repository.markAsPaid(order, paymentMethod, date);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> markAsUnpaid(OrderModel order) async {
//     final result = await _repository.markAsUnpaid(order);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> markAsJoined(OrderModel order,
//       {OrderModel? orderJoined}) async {
//     //adiciona o valor restante ao pedido juntado
//     if (orderJoined != null) {
//       var valorRestante = order.getTotalPending();
//       orderJoined.remaining += valorRestante;
//       await updateOrder(orderJoined);
//     }
//     final result =
//         await _repository.markAsJoined(order, orderJoinedId: orderJoined?.id);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> markAsUnjoined(OrderModel order) async {
//     //remove o valor juntado
//     if (order.joinedInOrderId != null) {
//       var orderJoined = await getOrder(order.joinedInOrderId!);
//       if (orderJoined != null &&
//           order.getTotalPending() <= orderJoined.remaining) {
//         var valorRestante = order.getTotalPending();
//         orderJoined.remaining -= valorRestante;
//         await updateOrder(orderJoined);
//       }
//     }
//     final result = await _repository.markAsUnjoined(order);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> updateDayMarking(OrderModel value) async {
//     final result = await _repository.updateDayMarking(value);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<OrderModel> markProductsAsDelivered(OrderModel value) async {
//     final result = await _repository.markProductsAsDelivered(value);
//     _offlineSyncRepository.addOrderToSync(result);
//     return result;
//   }

//   @override
//   Future<void> syncOrder(OrderModel orderModel) async {
//     await _repository.syncOrder(orderModel);
//   }
// }
