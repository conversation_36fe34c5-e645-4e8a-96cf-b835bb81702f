// import 'package:fl_app/models/client_model.dart';
// import 'package:fl_app/models/order_model.dart';
// import 'package:fl_app/models/sale_route_model.dart';

// abstract class OrderRepository {
//   Future<List<OrderModel>> getOrders(
//       {String? routeId,
//       DateTime? initialDate,
//       DateTime? finalDate,
//       bool? showToDelivery,
//       bool? showPaidOrders,
//       bool ascending = true,
//       String? sellerId});
//   Future<List<OrderModel>> getOrdersFromCache(
//       {String? routeId,
//       DateTime? initialDate,
//       DateTime? finalDate,
//       bool? showToDelivery,
//       bool? showPaidOrders,
//       bool ascending = true,
//       String? sellerId});

//   Future<List<OrderModel>> getDeletedOrdersFromCache({
//     String? routeId,
//   });

//   Future<OrderModel?> getOrder(String id);
//   Future<OrderModel> addOrder(OrderModel order);
//   Future<OrderModel> updateOrder(OrderModel order);
//   Future<OrderModel> updatePayments(OrderModel order);
//   Future<OrderModel> markAsDelivered(OrderModel order);
//   Future<OrderModel> markAsPaid(
//       OrderModel order, PaymentMethod paymentMethod, DateTime date);
//   Future<OrderModel> markAsUnpaid(OrderModel order);

//   Future<OrderModel> markAsJoined(OrderModel order, {OrderModel? orderJoined});
//   Future<OrderModel> markAsUnjoined(OrderModel order);

//   Future<void> deleteOrder(OrderModel order);
//   Future<OrderModel> restoreOrder(OrderModel order);

//   Future<List<OrderModel>> getTodaySaleOrders(String rotaId);

//   Future<Map<DateTime, List<OrderModel>>> getOrdersFromRouteByDate(
//       String routeId);

//   Future<List<Map<String, Map<ClientModel, List<OrderModel>>>>>
//       getOrdersByStreet(String routeId);

//   Future<Map<SaleRouteModel, List<OrderModel>>> getOrdersDelivery();

//   Future<List<OrderModel>> getMarkedOrders();

//   Future<List<OrderModel>> getPendingOrders();

//   Future<List<OrderModel>> getOrdersReveived({
//     required List<String> routes,
//     required DateTime date,
//   });

//   Future<List<OrderModel>> getMarkedToTodayOrders(List<String> cobradorRoutes);

//   Future<List<OrderModel>> getPendingOrdersToCobradorFixo(
//       List<String> cobradorRoutes, String routeId);

//   Future<void> updateInfoFromClientOrders(ClientModel client);
//   Future<OrderModel> updateClientInfo(OrderModel order);
//   Future<OrderModel> updateDayMarking(OrderModel value);

//   Future<OrderModel> markProductsAsDelivered(OrderModel value);

//   Future<void> syncOrder(OrderModel orderModel);
// }
