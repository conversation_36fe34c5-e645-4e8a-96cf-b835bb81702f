import 'dart:async';

import 'package:fl_app/repositories/geolocation/geolocation_repository.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import './geolocation_service.dart';

class GeolocationServiceImpl implements GeolocationService {
  final GeolocationRepository _geolocationRepository;

  GeolocationServiceImpl({required GeolocationRepository geolocationRepository})
      : _geolocationRepository = geolocationRepository;

  @override
  Future<Position?> getCurrentLocation(
      {LocationAccuracy accuracy = LocationAccuracy.bestForNavigation}) async {
    return await _geolocationRepository.getCurrentLocation();
  }

  @override
  Future<Stream<Position>> getCurrentLocationStream() async {
    return await _geolocationRepository.getCurrentLocationStream();
  }

  @override
  double calculateDistance(LatLng position1, LatLng position2) {
    return Geolocator.distanceBetween(position1.latitude, position1.longitude,
        position2.latitude, position2.longitude);
  }

  @override
  void updateUserLocation(Position position) {
    _geolocationRepository.updateUserLocation(position);
  }
}
