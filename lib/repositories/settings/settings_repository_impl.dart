import 'dart:developer';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/setting_model.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/services/connection/connection_service.dart';

import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'settings_repository.dart';

class SettingsRepositoryImpl implements SettingsRepository {
  final firestoreInstance = FirebaseFirestore.instance;
  final CollectionReference collection =
      FirebaseFirestore.instance.collection('global_settings');

  List<SettingModel> _cachedSettings = <SettingModel>[];
  List<SettingModel> get cachedSettings => List.from(_cachedSettings);
  set cachedSettings(List<SettingModel> value) => _cachedSettings = value;

  Box? box;
  Box? boxLocal;
  Box? boxLastUpdated;

  SettingsRepositoryImpl() {
    init();
  }

  Future<void> init() async {
    if (box == null) {
      box = await Hive.openBox('global_settings');
      await loadCache();
      sortCache();
    }
    boxLocal ??= await Hive.openBox('local_settings');
    await _getSettings();
  }

  Future<void> _getSettings() async {
    try {
      final lastUpdated = await getLastUpdated();
      Timestamp newLastUpdated = Timestamp.now();
      if (box == null) {
        box = await Hive.openBox('global_settings');
        await loadCache();
        sortCache();
      }
      if (cachedSettings.isNotEmpty) {
        log('Buscando configurações em cache e verificando se estão atualizadas');
        // Se houver dados em cache, verifique se estão atualizados
        final querySnapshot = await collection
            .where('lastModified', isGreaterThan: lastUpdated)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          // Se houver documentos mais recentes no Firestore, atualize apenas os documentos alterados
          final updatedSettings = querySnapshot.docs.map((doc) {
            var setting =
                SettingModel.fromMap(doc.data() as Map<String, dynamic>);
            setting.id = doc.id;
            return setting;
          }).toList();
          log('quantidade de configurações em cache: ${cachedSettings.length}');
          log('quantidade de configurações atualizadas: ${updatedSettings.length}');
          for (final updatedSetting in updatedSettings) {
            final index = _cachedSettings.indexWhere(
                (cachedSetting) => cachedSetting.id == updatedSetting.id);
            if (index == -1) {
              log('Configuração não encontrada em cache, adicionando');
              _cachedSettings.add(updatedSetting);
            } else {
              log('Configuração encontrada em cache, atualizando');
              _cachedSettings[index] = updatedSetting;
            }
            await saveThisToCache(updatedSetting);
          }
          sortCache();
          //await saveCache();
        }
      } else {
        log('Buscando configurações no Firestore');
        // Se não houver dados em cache, obtenha todos os documentos do Firestore
        await updateCache();
      }
      sortCache();
      if (await Get.find<ConnectionService>().checkConnection()) {
        _setLastUpdated(newLastUpdated);
      }
    } catch (e) {
      if (await Get.find<UserRepository>().isLogged()) {
        log('Erro ao obter configurações: $e');
        throw Exception('Erro ao obter configurações $e');
      }
      log('Erro ao obter configurações: $e');
    }
  }

  @override
  Future<void> updateCache() async {
    log('Atualizando cache de configurações (Firestore)');
    final querySnapshot = await collection.get();
    cachedSettings = querySnapshot.docs.map((doc) {
      var setting = SettingModel.fromMap(doc.data() as Map<String, dynamic>);
      setting.id = doc.id;
      return setting;
    }).toList();
    await saveCache();
  }

  void sortCache() {
    _cachedSettings.sort((a, b) {
      return a.name.toLowerCase().compareTo(b.name.toLowerCase());
    });
  }

  Future<void> saveCache() async {
    box ??= await Hive.openBox('global_settings');
    log('Salvando configurações em cache (Hive)');
    List<SettingModel> updatedSettings = [];
    for (var setting in cachedSettings) {
      await box!.put(setting.id, setting.toMap());
      updatedSettings.add(setting);
    }
    log('Configurações salvas em cache (Hive): ${updatedSettings.length}');
    //verificar configurações excluídas
    final deletedSettings = box!.values
        .where((element) =>
            updatedSettings.indexWhere((s) => s.id == element['id']) == -1)
        .toList();
    for (final deletedSetting in deletedSettings) {
      try {
        DocumentSnapshot doc = await collection.doc(deletedSetting['id']).get();
        if (!doc.exists) {
          log('Excluindo configuração do cache (Hive): ${deletedSetting['id']} - ${deletedSetting['name']}');
          await box!.delete(deletedSetting['id']);
        }
      } catch (e) {
        log('Erro ao verificar se a configuração foi excluída: $e');
      }
    }
  }

  Future<void> loadCache() async {
    log('Carregando configurações em cache (Hive)');
    cachedSettings = box!.values.map((e) {
      var setting = SettingModel(
        id: '',
        name: '',
        value: '',
        lastModified: Timestamp.now(),
      );
      setting.id = e['id'];
      setting.name = e['name'];
      setting.value = e['value'];
      setting.lastModified =
          Timestamp.fromDate(DateTime.parse(e['lastModified']));
      return setting;
    }).toList();
    log('Configurações carregadas em cache (Hive): ${cachedSettings.length}');
  }

  Future<Timestamp> getLastUpdated() async {
    boxLastUpdated ??= await Hive.openBox('global_settings_last_updated');
    final lastUpdated = boxLastUpdated!.get('lastUpdated');
    if (lastUpdated == null) {
      final newLastUpdated = Timestamp.now();
      await _setLastUpdated(newLastUpdated);
      return newLastUpdated;
    }
    return Timestamp.fromDate(lastUpdated);
  }

  Future<void> _setLastUpdated(Timestamp timestamp) async {
    boxLastUpdated ??= await Hive.openBox('global_settings_last_updated');
    await boxLastUpdated!.put('lastUpdated', timestamp.toDate());
  }

  @override
  Future<String> getAdminPassword() async {
    _getSettings();
    final adminPassword =
        cachedSettings.firstWhere((setting) => setting.name == 'adminPassword');
    return adminPassword.value;
  }

  @override
  Future<void> setAdminPassword(String password) async {
    final adminPassword =
        cachedSettings.firstWhere((setting) => setting.name == 'adminPassword');
    adminPassword.value = password;
    updateSetting(adminPassword);
  }

  Future<SettingModel> updateSetting(SettingModel setting) async {
    try {
      setting.lastModified = Timestamp.now();
      final index = cachedSettings
          .indexWhere((cachedSetting) => cachedSetting.id == setting.id);
      if (index == -1) {
        throw Exception('Setting não encontrado');
      }
      collection.doc(setting.id).update(setting.toDocument());
      final tempSettings = cachedSettings;
      tempSettings[index] = setting;
      cachedSettings = tempSettings;
      sortCache();
      await saveThisToCache(setting);
      return setting;
    } catch (e) {
      log('Erro ao atualizar setting: $e');
      throw Exception('Erro ao atualizar setting $e');
    }
  }

  Future<void> saveThisToCache(SettingModel settingModel) async {
    box ??= await Hive.openBox('global_settings');
    await box!.put(settingModel.id, settingModel.toMap());
    log('Configuração salva em cache (Hive): ${settingModel.id} - ${settingModel.name}');
  }

  @override
  Future<void> resetCache() async {
    box ??= await Hive.openBox('global_settings');
    await box!.clear();
    cachedSettings = <SettingModel>[];
  }

  @override
  void setInitialFinalDay(int day) {
    boxLocal!.put('initialFinalDay', day);
  }

  @override
  int getInitialFinalDay() {
    return boxLocal!.get('initialFinalDay', defaultValue: 19);
  }

  @override
  Future<void> togleDarkMode() async {
    boxLocal ??= await Hive.openBox('local_settings');
    boxLocal!.put('darkMode', !boxLocal!.get('darkMode', defaultValue: false));
  }

  @override
  Future<bool> getDarkMode() async {
    boxLocal ??= await Hive.openBox('local_settings');
    return boxLocal!.get('darkMode', defaultValue: false);
  }
}
