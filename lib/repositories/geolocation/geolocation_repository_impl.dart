import 'dart:async';
import 'dart:developer';

import 'package:geolocator/geolocator.dart';

import './geolocation_repository.dart';

class GeolocationRepositoryImpl implements GeolocationRepository {
  Position? currentLocation;

  StreamSubscription<Position>? userLocationStream;

  GeolocationRepositoryImpl() {
    init();
  }

  void init() async {
    await verifyPermission();
  }

  @override
  Future<Position?> getCurrentLocation() async {
    if (currentLocation != null) {
      return currentLocation;
    }
    return await Geolocator.getCurrentPosition();
  }

  Future<void> verifyPermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      log('Geolocalização desabilitada.');
      throw ('Geolocalização desabilitada.');
    }
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        log('Permissão de localização negada.');
        return Future.error('Permissão de localização negada.');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      log('Permissão de localização negada permanentemente, não podemos solicitar permissões.');
      return Future.error(
          'Permissão de localização negada permanentemente, não podemos solicitar permissões.');
    }
  }

  @override
  Future<Stream<Position>> getCurrentLocationStream() async {
    return Geolocator.getPositionStream();
  }

  @override
  void updateUserLocation(Position position) {
    currentLocation = position;
  }
}
