import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:fl_app/models/product_model.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:light_flutter_nearby_connections/light_flutter_nearby_connections.dart';
import 'package:permission_handler/permission_handler.dart';

enum DeviceType { advertiser, browser }

class CacheKeys {
  static const String offlineSync = 'offline_sync';
  static const String connectedDevices = 'connectedDevices';
  static const String productsToSync = 'productsToSync';
  static const String ordersToSync = 'ordersToSync';
  static const String clientsToSync = 'clientsToSync';
  static const String isEnable = 'isEnable';
  static const String dayToSync = 'dayToSync';
  static const String deviceType = 'deviceType';
}

class OfflineSyncRepositoryImpl {
  late OrderRepository orderRepository;
  late ClientService clientService;
  late ProductRepository productRepository;

  RxList<Device> devices = <Device>[].obs;
  RxList<Device> connectedDevices = <Device>[].obs;
  RxList<String> savedConnectedDevicesNames = <String>[].obs;
  NearbyService? nearbyService;
  late StreamSubscription subscription;
  late StreamSubscription receivedDataSubscription;

  DateTime dayToSync = DateTime.now();

  RxList<ProductModel> productsToSync = <ProductModel>[].obs;
  RxList<OrderModel> ordersToSync = <OrderModel>[].obs;
  RxList<ClientModel> clientsToSync = <ClientModel>[].obs;

  RxBool isBrowsing = false.obs;
  RxBool isAdvertising = false.obs;

  RxBool isEnable = false.obs;

  DeviceType deviceType = DeviceType.browser;

  Box? box;

  Timer? timerConnectSavedDevices;
  Timer? timerToRestartServer;

  Timer? timerSyncProducts;
  Timer? timerSyncOrders;
  Timer? timerSyncClients;

  String mySyncerName = '';

  OfflineSyncRepositoryImpl() {
    askPermissions();
  }

  Future<void> init() async {
    try {
      box = await Hive.openBox(CacheKeys.offlineSync);
      getEnableFromCache();
      if (isEnable.value) {
        await enableAll();
      } else {
        await disableAll();
      }
      var deviceInfo = await DeviceInfoPlugin().androidInfo;
      mySyncerName = deviceInfo.model;
    } catch (e, stackTrace) {
      log("ERROR in init: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao no "init" da sincronização offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> enableAll() async {
    try {
      await Future.wait([
        getDeviceTypeFromCache(),
        getConnectDevicesFromCache(),
        initNearbyConnections(),
        syncDataFromCache(),
      ]);

      setTimers();

      getServices();
    } catch (e, stackTrace) {
      log("ERROR in enableAll: $e", stackTrace: stackTrace);
      // Get.showSnackbar(const GetSnackBar(
      //   title: 'Erro',
      //   message: 'Ocorreu um erro ao habilitar a sincronização offline.',
      //   duration: Duration(seconds: 1),
      // ));
    }
  }

  Future<void> disableAll() async {
    try {
      await stopAll();
      stopTimers();
      devices.clear();
      connectedDevices.clear();
      savedConnectedDevicesNames.clear();
      productsToSync.clear();
      ordersToSync.clear();
      clientsToSync.clear();
      dayToSync = DateTime.now();
    } catch (e, stackTrace) {
      log("ERROR in disableAll: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao desabilitar a sincronização offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  void stopTimers() {
    timerConnectSavedDevices?.cancel();
    timerSyncProducts?.cancel();
    timerSyncOrders?.cancel();
    timerSyncClients?.cancel();
    timerToRestartServer?.cancel();
  }

  Future<void> getConnectDevicesFromCache() async {
    try {
      var connectedDevicesNames = box!.get(CacheKeys.connectedDevices);
      if (connectedDevicesNames != null) {
        savedConnectedDevicesNames.addAll(List.from(connectedDevicesNames));
        log("OFFLINE SYNC: connectedDevicesNames: $connectedDevicesNames");
      }
    } catch (e, stackTrace) {
      log("ERROR in getConnectDevicesFromCache: $e", stackTrace: stackTrace);
      Get.showSnackbar(
        const GetSnackBar(
          title: 'Erro',
          message: 'Ocorreu um erro ao recuperar dispositivos conectados.',
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  Future<void> getDeviceTypeFromCache() async {
    try {
      var type = box!.get(CacheKeys.deviceType);
      if (type != null) {
        switch (type) {
          case 'advertiser':
            deviceType = DeviceType.advertiser;
            break;
          case 'browser':
            deviceType = DeviceType.browser;
            break;
          default:
            deviceType = DeviceType.browser;
            break;
        }
      }
    } catch (e, stackTrace) {
      log("ERROR in getDeviceTypeFromCache: $e", stackTrace: stackTrace);
      Get.dialog(AlertDialog(
        title: const Text("Erro"),
        content:
            const Text("Ocorreu um erro ao recuperar o tipo de dispositivo."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("OK"),
          ),
        ],
      ));
    }
  }

  Future<void> syncDataFromCache() async {
    try {
      var date = box!.get(CacheKeys.dayToSync);
      if (date != null &&
          DateTimeHelper.getFormattedDate(DateTime.parse(date)) !=
              DateTimeHelper.getFormattedDate(DateTime.now())) {
        await resetAllData();
      } else if (date != null &&
          DateTimeHelper.getFormattedDate(DateTime.parse(date)) ==
              DateTimeHelper.getFormattedDate(DateTime.now())) {
        // Obter produtos, pedidos e clientes salvos
        var products = box!.get(CacheKeys.productsToSync);
        if (products != null) {
          productsToSync.addAll((products as List)
              .map((e) => ProductModel.fromMap(e as Map))
              .toList());
        }
        var orders = box!.get(CacheKeys.ordersToSync);
        if (orders != null) {
          ordersToSync.addAll((orders as List)
              .map((e) => OrderModel.fromMap(e as Map))
              .toList());
        }
        var clients = box!.get(CacheKeys.clientsToSync);
        if (clients != null) {
          clientsToSync.addAll((clients as List)
              .map((e) => ClientModel.fromMap(e as Map))
              .toList());
        }
      } else {
        await resetAllData();
      }
    } catch (e, stackTrace) {
      log("ERROR in syncDataFromCache: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao sincronizar dados offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  DeviceType getDeviceType() {
    return deviceType;
  }

  Future<void> setDeviceType(DeviceType type) async {
    try {
      deviceType = type;
      await box!.put(CacheKeys.deviceType, type.toString().split('.').last);
    } catch (e, stackTrace) {
      log("ERROR in setDeviceType: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao definir o tipo de dispositivo.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> initNearbyConnections() async {
    try {
      nearbyService = NearbyService();
      String devInfo = '';
      await askPermissions();
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      devInfo = androidInfo.model;

      await nearbyService?.init(
          serviceType: 'mpconn',
          deviceName: devInfo,
          strategy: Strategy.P2P_CLUSTER,
          callback: (isRunning) async {
            log("OFFLINE SYNC: isRunning: $isRunning");
            if (isRunning) {
              if (deviceType == DeviceType.browser) {
                log("OFFLINE SYNC: startServer running");
                await stopAll();
                await Future.delayed(const Duration(microseconds: 500));
                await startServer();
              } else {
                log("OFFLINE SYNC: startSender running");
                await stopAll();
                await Future.delayed(const Duration(microseconds: 500));
                await startSender();
              }
            }
          });
      subscription =
          nearbyService!.stateChangedSubscription(callback: (devicesList) {
        if (!isEnable.value) return;

        try {
          for (var element in devicesList) {
            log("OFFLINE SYNC: deviceId: ${element.deviceId} | deviceName: ${element.deviceName} | state: ${element.state}");
            if (element.state == SessionState.connected) {
              addToSavedConnectedDevices(element.deviceName);
            }
            if (element.state == SessionState.connected &&
                !connectedDevices.contains(element)) {
              connectedDevices.add(element);
            }
          }

          devices.assignAll(devicesList);
          connectedDevices.assignAll(devicesList
              .where((d) => d.state == SessionState.connected)
              .toList());
        } catch (e, stackTrace) {
          log("ERROR in stateChangedSubscription callback: $e",
              stackTrace: stackTrace);
          Get.showSnackbar(const GetSnackBar(
            title: 'Erro',
            message: 'Ocorreu um erro ao mudar o estado da conexão offline.',
            duration: Duration(seconds: 1),
          ));
        }
      });

      receivedDataSubscription =
          nearbyService!.dataReceivedSubscription(callback: (data) {
        if (!isEnable.value) return;

        try {
          log("OFFLINE SYNC: received: $data");
          var content = jsonDecode((data as Map)['message']);
          if (content['type'] == 'products') {
            var products = content['data'] as List;
            syncProducts(products);
          } else if (content['type'] == 'orders') {
            var orders = content['data'] as List;
            syncOrders(orders);
          } else if (content['type'] == 'clients') {
            var clients = content['data'] as List;
            syncClients(clients);
          }
        } catch (e, stackTrace) {
          log("ERROR in dataReceivedSubscription callback: $e",
              stackTrace: stackTrace);
          Get.showSnackbar(const GetSnackBar(
            title: 'Erro',
            message: 'Ocorreu um erro ao receber dados offline.',
            duration: Duration(seconds: 1),
          ));
        }
      });
    } catch (e, stackTrace) {
      log("ERROR in initNearbyConnections: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao inicializar as conexões offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> askPermissions() async {
    try {
      final info = await DeviceInfoPlugin().androidInfo;
      var withPermissions = true;
      var errors = [];
      final explicitPermissions = [
        Permission.locationWhenInUse, // Always?
        Permission.location, // Always?
        // Android 12 and higher
        if (info.version.sdkInt >= 31) Permission.bluetoothAdvertise,
        if (info.version.sdkInt >= 31) Permission.bluetoothConnect,
        if (info.version.sdkInt >= 31) Permission.bluetoothScan,
        // Android 13 and higher
        if (info.version.sdkInt >= 33) Permission.nearbyWifiDevices,
      ];
      try {
        if (explicitPermissions.isNotEmpty) {
          final other = await explicitPermissions.request();
          final locationStatus = await Permission.location.status;
          if (!locationStatus.isGranted) {
            errors.add("Location is not enabled");
          }
          final otherPermissions =
              !other.values.any((element) => !element.isGranted);
          withPermissions &= otherPermissions;
          if (!otherPermissions) {
            errors.add("Some permissions weren't given");
          }
          log("requestPermissions granted: $other, withPermissions: $withPermissions");
        }
      } catch (e) {
        errors.add("Error occurred while requesting permissions");
      }
      if (errors.isNotEmpty) {
        log("ERROR: requestPermissions failed: $errors");
        Get.showSnackbar(const GetSnackBar(
          title: 'Erro',
          message: 'Ocorreu um erro ao sincronizar pedidos offline.',
          duration: Duration(seconds: 1),
        ));
      }
    } catch (e, stackTrace) {
      log("ERROR in askPermissions: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao solicitar permissões.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> startServer() async {
    try {
      await stopAll();
      nearbyService?.startBrowsingForPeers();
      isBrowsing.value = true;
      log("OFFLINE SYNC: startServer");
    } catch (e, stackTrace) {
      log("ERROR in startServer: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao iniciar o servidor.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> startSender() async {
    try {
      await stopAll();
      nearbyService?.startAdvertisingPeer();
      isAdvertising.value = true;
      log("OFFLINE SYNC: startSender");
    } catch (e, stackTrace) {
      log("ERROR in startSender: $e", stackTrace: stackTrace);
      Get.dialog(AlertDialog(
        title: const Text("Erro"),
        content: const Text("Ocorreu um erro ao iniciar o emissor."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("OK"),
          ),
        ],
      ));
    }
  }

  Future<void> stopServer() async {
    try {
      if (nearbyService == null) return;
      nearbyService?.stopBrowsingForPeers();
      isBrowsing.value = false;
      log("OFFLINE SYNC: stopServer");
    } catch (e, stackTrace) {
      log("ERROR in stopServer: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao parar o servidor.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> stopSender() async {
    try {
      if (nearbyService == null) return;
      nearbyService?.stopAdvertisingPeer();
      isAdvertising.value = false;
      log("OFFLINE SYNC: stopSender");
    } catch (e, stackTrace) {
      log("ERROR in stopSender: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao parar o emissor.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> stopAll() async {
    try {
      await stopServer();
      await stopSender();
      log("OFFLINE SYNC: stopAll");
    } catch (e, stackTrace) {
      log("ERROR in stopAll: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao parar todas as conexões.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  void addToSavedConnectedDevices(String name) async {
    try {
      if (savedConnectedDevicesNames.contains(name)) {
        return;
      }
      savedConnectedDevicesNames.add(name);
      await box!.put(CacheKeys.connectedDevices, savedConnectedDevicesNames);
    } catch (e, stackTrace) {
      log("ERROR in addToSavedConnectedDevices: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao salvar o dispositivo conectado.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  void setTimers() async {
    try {
      timerConnectSavedDevices?.cancel();
      timerSyncProducts?.cancel();
      timerSyncOrders?.cancel();
      timerSyncClients?.cancel();
      timerToRestartServer?.cancel();

      timerConnectSavedDevices = Timer.periodic(
        const Duration(seconds: 10),
        (timer) {
          if (!isEnable.value) return;

          log("OFFLINE SYNC: tentando conectar com dispositivos salvos");
          if (savedConnectedDevicesNames.isNotEmpty &&
              deviceType == DeviceType.browser) {
            for (var name in savedConnectedDevicesNames) {
              for (var device in devices) {
                if (device.deviceName == name &&
                    device.state == SessionState.notConnected) {
                  nearbyService?.invitePeer(
                    deviceID: device.deviceId,
                    deviceName: device.deviceName,
                  );
                }
              }
            }
          }
        },
      );

      timerSyncProducts = Timer.periodic(
        const Duration(seconds: 30),
        (timer) {
          if (!isEnable.value) return;

          try {
            log("OFFLINE SYNC: timerSyncProducts, productsToSync: ${productsToSync.length}");
            if (productsToSync.isNotEmpty) {
              for (var device in connectedDevices) {
                var message = {
                  'type': 'products',
                  'data': productsToSync.map((e) => e.toMap()).toList(),
                  'syncerName': mySyncerName,
                };
                log("OFFLINE SYNC: sending ${productsToSync.length} products to ${device.deviceName}");
                nearbyService?.sendMessage(
                    device.deviceId, jsonEncode(message));
              }
            }
          } catch (e, stackTrace) {
            log("ERROR in timerSyncProducts: $e", stackTrace: stackTrace);
            Get.showSnackbar(const GetSnackBar(
              title: 'Erro',
              message:
                  'Ocorreu um erro ao sincronizar produtos offline pelo timer.',
              duration: Duration(seconds: 1),
            ));
          }
        },
      );

      timerSyncOrders = Timer.periodic(
        const Duration(seconds: 20),
        (timer) {
          if (!isEnable.value) return;

          try {
            log("OFFLINE SYNC: timerSyncOrders, ordersToSync: ${ordersToSync.length}");
            if (ordersToSync.isNotEmpty) {
              for (var device in connectedDevices) {
                var message = {
                  'type': 'orders',
                  'data': ordersToSync.map((e) => e.toMap()).toList(),
                  'syncerName': mySyncerName,
                };
                log("OFFLINE SYNC: sending ${ordersToSync.length} orders to ${device.deviceName}");
                nearbyService?.sendMessage(
                    device.deviceId, jsonEncode(message));
              }
            }
          } catch (e, stackTrace) {
            log("ERROR in timerSyncOrders: $e", stackTrace: stackTrace);
            Get.showSnackbar(const GetSnackBar(
              title: 'Erro',
              message:
                  'Ocorreu um erro ao sincronizar pedidos offline pelo timer.',
              duration: Duration(seconds: 1),
            ));
          }
        },
      );

      timerSyncClients = Timer.periodic(
        const Duration(seconds: 60),
        (timer) {
          if (!isEnable.value) return;

          try {
            log("OFFLINE SYNC: timerSyncClients, clientsToSync: ${clientsToSync.length}");
            if (clientsToSync.isNotEmpty) {
              for (var device in connectedDevices) {
                var message = {
                  'type': 'clients',
                  'data': clientsToSync.map((e) => e.toMap()).toList(),
                  'syncerName': mySyncerName,
                };
                log("OFFLINE SYNC: sending ${clientsToSync.length} clients to ${device.deviceName}");
                nearbyService?.sendMessage(
                    device.deviceId, jsonEncode(message));
              }
            }
          } catch (e, stackTrace) {
            log("ERROR in timerSyncClients: $e", stackTrace: stackTrace);
            Get.showSnackbar(const GetSnackBar(
              title: 'Erro',
              message:
                  'Ocorreu um erro ao sincronizar clientes offline pelo timer.',
              duration: Duration(seconds: 1),
            ));
          }
        },
      );

      timerToRestartServer = Timer.periodic(
        const Duration(seconds: 30),
        (timer) async {
          if (!isEnable.value) return;

          try {
            log("OFFLINE SYNC: timerToRestartServer");
            if (deviceType == DeviceType.browser) {
              await stopAll();
              await Future.delayed(const Duration(microseconds: 500));
              await startServer();
            } else {
              await stopAll();
              await Future.delayed(const Duration(microseconds: 500));
              await startSender();
            }
          } catch (e, stackTrace) {
            log("ERROR in timerToRestartServer: $e", stackTrace: stackTrace);
            Get.showSnackbar(const GetSnackBar(
              title: 'Erro',
              message: 'Ocorreu um erro ao reiniciar o servidor.',
              duration: Duration(seconds: 1),
            ));
          }
        },
      );
    } catch (e, stackTrace) {
      log("ERROR in setTimers: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message:
            'Ocorreu um erro ao definir os temporizadores de sincronização offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  void addProductToSync(ProductModel product) {
    try {
      var index = productsToSync.indexWhere((p) => p.id == product.id);
      if (index != -1) {
        productsToSync[index] = product;
      } else {
        productsToSync.add(product);
      }
      box!.put(CacheKeys.productsToSync,
          productsToSync.map((e) => e.toMap()).toList());
      log("OFFLINE SYNC: addProductToSync: ${product.id}");
    } catch (e, stackTrace) {
      log("ERROR in addProductToSync: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message:
            'Ocorreu um erro ao adicionar o produto para sincronização offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  void addOrderToSync(OrderModel order) {
    try {
      var index = ordersToSync.indexWhere((o) => o.id == order.id);
      if (index != -1) {
        ordersToSync[index] = order;
      } else {
        ordersToSync.add(order);
      }
      box!.put(
          CacheKeys.ordersToSync, ordersToSync.map((e) => e.toMap()).toList());
      log("OFFLINE SYNC: addOrderToSync: ${order.id}");
    } catch (e, stackTrace) {
      log("ERROR in addOrderToSync: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message:
            'Ocorreu um erro ao adicionar o pedido para sincronização offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  void addClientToSync(ClientModel client) {
    try {
      var index = clientsToSync.indexWhere((c) => c.id == client.id);
      if (index != -1) {
        clientsToSync[index] = client;
      } else {
        clientsToSync.add(client);
      }
      box!.put(CacheKeys.clientsToSync,
          clientsToSync.map((e) => e.toMap()).toList());
      log("OFFLINE SYNC: addClientToSync: ${client.id}");
    } catch (e, stackTrace) {
      log("ERROR in addClientToSync: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message:
            'Ocorreu um erro ao adicionar o cliente para sincronização offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> syncProducts(List products) async {
    try {
      log("OFFLINE SYNC: syncProducts");
      for (var product in products) {
        var newProduct = ProductModel.fromMap(product);
        await productRepository.syncProduct(newProduct);

        var index = productsToSync.indexWhere((p) => p.id == product['id']);
        if (index != -1) {
          var actualProduct = productsToSync[index];
          if (actualProduct.lastModified
              .isBefore(DateTime.parse(product['lastModified']))) {
            productsToSync.removeAt(index);
            productsToSync.add(newProduct);
            await box!.put(CacheKeys.productsToSync,
                productsToSync.map((e) => e.toMap()).toList());
          }
        } else {
          productsToSync.add(newProduct);
          await box!.put(CacheKeys.productsToSync,
              productsToSync.map((e) => e.toMap()).toList());
        }
      }
    } catch (e, stackTrace) {
      log("ERROR in syncProducts: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao sincronizar produtos offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> syncOrders(List orders) async {
    try {
      log("OFFLINE SYNC: syncOrders");
      for (var order in orders) {
        var newOrder = OrderModel.fromMap(order);
        await orderRepository.syncOrder(newOrder);
        var index = ordersToSync.indexWhere((o) => o.id == order['id']);
        if (index != -1) {
          var actualOrder = ordersToSync[index];
          if (actualOrder.lastModified
              .isBefore(DateTime.parse(order['lastModified']))) {
            ordersToSync.removeAt(index);
            ordersToSync.add(newOrder);
            await box!.put(CacheKeys.ordersToSync,
                ordersToSync.map((e) => e.toMap()).toList());
          }
        } else {
          ordersToSync.add(newOrder);
          await box!.put(CacheKeys.ordersToSync,
              ordersToSync.map((e) => e.toMap()).toList());
        }
      }
    } catch (e, stackTrace) {
      log("ERROR in syncOrders: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao sincronizar pedidos offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> syncClients(List clients) async {
    try {
      log("OFFLINE SYNC: syncClients");
      for (var client in clients) {
        var newClient = ClientModel.fromMap(client);
        await clientService.syncClient(newClient);
        var index = clientsToSync.indexWhere((c) => c.id == client['id']);
        if (index != -1) {
          var actualClient = clientsToSync[index];
          if (actualClient.lastModified
              .toDate()
              .isBefore(DateTime.parse(client['lastModified']))) {
            clientsToSync.removeAt(index);
            clientsToSync.add(newClient);
            await box!.put(CacheKeys.clientsToSync,
                clientsToSync.map((e) => e.toMap()).toList());
          }
        } else {
          clientsToSync.add(newClient);
          await box!.put(CacheKeys.clientsToSync,
              clientsToSync.map((e) => e.toMap()).toList());
        }
      }
    } catch (e, stackTrace) {
      log("ERROR in syncClients: $e", stackTrace: stackTrace);
      Get.showSnackbar(const GetSnackBar(
        title: 'Erro',
        message: 'Ocorreu um erro ao sincronizar clientes offline.',
        duration: Duration(seconds: 1),
      ));
    }
  }

  Future<void> getServices() async {
    try {
      log("OFFLINE SYNC: getServices");
      orderRepository = Get.find<OrderRepository>();
      clientService = Get.find<ClientService>();
      productRepository = Get.find<ProductRepository>();
    } catch (e, stackTrace) {
      log("ERROR in getServices: $e", stackTrace: stackTrace);
      Get.dialog(AlertDialog(
        title: const Text("Erro"),
        content:
            const Text("Ocorreu um erro ao obter os serviços necessários."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("OK"),
          ),
        ],
      ));
    }
  }

  void clearSavedConnectedDevices() {
    try {
      savedConnectedDevicesNames.clear();
      box!.put(CacheKeys.connectedDevices, []);
      resetAllData();
    } catch (e, stackTrace) {
      log("ERROR in clearSavedConnectedDevices: $e", stackTrace: stackTrace);
      Get.dialog(AlertDialog(
        title: const Text("Erro"),
        content:
            const Text("Ocorreu um erro ao limpar dispositivos conectados."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("OK"),
          ),
        ],
      ));
    }
  }

  void getEnableFromCache() {
    try {
      var enable = box!.get(CacheKeys.isEnable);
      if (enable != null) {
        isEnable.value = enable;
      }
    } catch (e, stackTrace) {
      log("ERROR in getEnableFromCache: $e", stackTrace: stackTrace);
      Get.dialog(AlertDialog(
        title: const Text("Erro"),
        content: const Text(
            "Ocorreu um erro ao recuperar a configuração de habilitação."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("OK"),
          ),
        ],
      ));
    }
  }

  void setEnable(bool enable) {
    try {
      isEnable.value = enable;
      box!.put(CacheKeys.isEnable, enable);
      if (enable) {
        enableAll();
      } else {
        disableAll();
      }
    } catch (e, stackTrace) {
      log("ERROR in setEnable: $e", stackTrace: stackTrace);
      Get.dialog(AlertDialog(
        title: const Text("Erro"),
        content: const Text("Ocorreu um erro ao definir a habilitação."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("OK"),
          ),
        ],
      ));
    }
  }

  Future<void> resetAllData() async {
    try {
      await box!.put(CacheKeys.dayToSync, DateTime.now().toIso8601String());
      await box!.put(CacheKeys.productsToSync, []);
      await box!.put(CacheKeys.ordersToSync, []);
      await box!.put(CacheKeys.clientsToSync, []);
      dayToSync = DateTime.now();
      productsToSync.clear();
      ordersToSync.clear();
      clientsToSync.clear();
    } catch (e, stackTrace) {
      log("ERROR in resetAllData: $e", stackTrace: stackTrace);
      Get.dialog(AlertDialog(
        title: const Text("Erro"),
        content: const Text("Ocorreu um erro ao redefinir todos os dados."),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text("OK"),
          ),
        ],
      ));
    }
  }

  void dispose() {
    try {
      subscription.cancel();
      receivedDataSubscription.cancel();
      stopTimers();
    } catch (e, stackTrace) {
      log("ERROR in dispose: $e", stackTrace: stackTrace);
    }
  }
}
