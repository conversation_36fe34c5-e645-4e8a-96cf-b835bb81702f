import 'package:fl_app/models/shopping_cart.dart';
import 'package:fl_app/repositories/cart/cart_repository.dart';
import 'package:hive_flutter/hive_flutter.dart';

class CartRepositoryImpl implements CartRepository {
  Box? box;

  CartRepositoryImpl() {
    init();
  }

  Future<void> init() async {
    box = await Hive.openBox('shoppingCarts');
  }

  @override
  Future<ShoppingCart?> getShoppingCart(String id) async {
    box ??= await Hive.openBox('shoppingCarts');
    final shoppingCart = box?.get(id);
    return shoppingCart != null ? ShoppingCart.fromMap(shoppingCart) : null;
  }

  @override
  Future<List<ShoppingCart>> getShoppingCarts() async {
    box ??= await Hive.openBox('shoppingCarts');
    List<ShoppingCart> shoppingCarts = [];
    for (var i = 0; i < box!.length; i++) {
      final shoppingCart = box?.getAt(i);
      if (shoppingCart != null) {
        shoppingCarts.add(await ShoppingCart.fromMap(shoppingCart));
      }
    }
    return shoppingCarts;
  }

  @override
  Future<void> removeShoppingCart(ShoppingCart e) async {
    box ??= await Hive.openBox('shoppingCarts');
    box?.delete(e.id);
  }

  @override
  Future<void> saveShoppingCart(ShoppingCart shoppingCart) async {
    box ??= await Hive.openBox('shoppingCarts');
    box?.put(shoppingCart.id, shoppingCart.toMap());
  }
}
