import 'dart:async';
import 'dart:developer';
import 'package:fl_app/application/auth/auth_service.dart';
import 'package:fl_app/models/sale_route_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

import './sales_routes_repository.dart';

class SalesRoutesRepositoryImpl implements SalesRoutesRepository {
  final firestoreInstance = FirebaseFirestore.instance;
  final CollectionReference collection =
      FirebaseFirestore.instance.collection('sales_routes');

  final DocumentReference countReference =
      FirebaseFirestore.instance.doc('count/sales_routes');

  List<SaleRouteModel> _cachedRoutes = <SaleRouteModel>[];
  List<SaleRouteModel> get cachedRoutes => List.from(_cachedRoutes);
  set cachedRoutes(List<SaleRouteModel> value) => _cachedRoutes = value;

  Box? box;
  Box? boxLastUpdated;

  List<SaleRouteModel> toUpdateLastModified = [];
  Box? toUpdateLastModifiedBox;
  Timer? timerToUpdateLastModified;

  SalesRoutesRepositoryImpl() {
    init();
  }

  Future<void> init() async {
    if (box == null) {
      box = await Hive.openBox('sales_routes');
      await loadCache();
      sortCache();
    }
    await getSalesRoutes();

    if (toUpdateLastModifiedBox == null) {
      toUpdateLastModifiedBox =
          await Hive.openBox('toUpadateLastModified-SalesRoutes');

      await loadToUpdateLastModified();
    }

    timerToUpdateLastModified ??=
        Timer.periodic(const Duration(seconds: 10), (timer) {
      verifySalesRoutesToUpdateLastModified();
    });
  }

  @override
  Future<void> addSaleRoute(SaleRouteModel saleRouteModel) async {
    try {
      final newSaleRef = collection.doc();
      newSaleRef.set(saleRouteModel.toDocument());
      saleRouteModel.id = newSaleRef.id;
      _cachedRoutes.add(saleRouteModel);
      bool hasConnection =
          await Get.find<ConnectionService>().checkConnection();
      if (!hasConnection) {
        addToUpdateLastModified(saleRouteModel);
      }
      sortCache();
      await saveThisToCache(saleRouteModel);
    } catch (e) {
      log('Erro ao adicionar rota de venda: $e');
      throw Exception('Erro ao adicionar rota de venda $e');
    }
  }

  @override
  Future<void> deleteSaleRoute(SaleRouteModel saleRouteModel) async {
    try {
      saleRouteModel.isDeleted = true;
      saleRouteModel.lastModified = Timestamp.now();
      await updateSaleRoute(saleRouteModel);
    } catch (e) {
      log('Erro ao deletar rota de venda: $e');
      throw Exception('Erro ao deletar rota de venda $e');
    }
  }

  @override
  Future<List<SaleRouteModel>> getSalesRoutes() async {
    try {
      Timestamp lastUpdated = await getLastUpdated();
      Timestamp newLastUpdated = Timestamp.now();
      if (box == null) {
        box = await Hive.openBox('sales_routes');
        await loadCache();
      }
      if (cachedRoutes.isNotEmpty) {
        log('Verificando se há rotas de venda mais recentes no Firestore');
        // Se houver dados em cache, verifique se estão atualizados
        final querySnapshot = await collection
            .where('lastModified', isGreaterThan: lastUpdated)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          // Se houver documentos mais recentes no Firestore, atualize apenas os documentos alterados
          final updatedRoutes = querySnapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            var sale = SaleRouteModel.fromMap(data);
            sale.id = doc.id;
            return sale;
          }).toList();
          log('quantidade de rotas em cache: ${cachedRoutes.length}');
          log('quantidade de rotas atualizadas: ${updatedRoutes.length}');
          for (final updatedRoute in updatedRoutes) {
            final index = _cachedRoutes
                .indexWhere((cachedRoute) => cachedRoute.id == updatedRoute.id);
            if (index == -1) {
              log('Rota não encontrada em cache, adicionando');
              _cachedRoutes.add(updatedRoute);
            } else {
              log('Rota encontrada em cache, atualizando');
              _cachedRoutes[index] = updatedRoute;
            }
            await saveThisToCache(updatedRoute);
          }
          sortCache();
          //await saveCache();
        }
        checkIfHasCountMismatch();
      } else {
        log('Buscando rotas de venda no Firestore');
        // Se não houver dados em cache, busque todos os documentos
        await updateCache();
      }
      if (await Get.find<ConnectionService>().checkConnection()) {
        _setLastUpdated(newLastUpdated);
      }
      return cachedRoutes.where((element) => !element.isDeleted).toList();
    } catch (e) {
      log('Erro ao buscar rotas de venda: $e');
      if (await Get.find<UserRepository>().isLogged()) {
        throw Exception('Erro ao buscar rotas de venda $e');
      }
      return cachedRoutes.where((element) => !element.isDeleted).toList();
    }
  }

  @override
  Future<void> updateSaleRoute(SaleRouteModel saleRouteModel) async {
    try {
      saleRouteModel.lastModified = Timestamp.now();
      collection.doc(saleRouteModel.id).update(saleRouteModel.toDocument());
      bool hasConnection =
          await Get.find<ConnectionService>().checkConnection();
      if (!hasConnection) {
        addToUpdateLastModified(saleRouteModel);
      }
      final index = cachedRoutes
          .indexWhere((cachedRoute) => cachedRoute.id == saleRouteModel.id);
      final tempRoutes = cachedRoutes;
      tempRoutes[index] = saleRouteModel;
      cachedRoutes = tempRoutes;
      sortCache();
      await saveThisToCache(saleRouteModel);
    } catch (e) {
      log('Erro ao atualizar rota de venda: $e');
      throw Exception('Erro ao atualizar rota de venda $e');
    }
  }

  void sortCache() {
    _cachedRoutes.sort((a, b) => a.salesDay.compareTo(b.salesDay));
  }

  Future<void> saveCache() async {
    box ??= await Hive.openBox('sales_routes');
    log('Salvando rotas de venda em cache(hive)');
    List<SaleRouteModel> updatedRoutes = [];

    for (final sale in cachedRoutes) {
      await box!.put(sale.id, sale.toMap());
      updatedRoutes.add(sale);
    }
    log('Rotas de venda salvas em cache(hive): ${updatedRoutes.length}');
    //verify deleted routes
    final deletedRoutes = box!.values
        .where((element) =>
            updatedRoutes.indexWhere(
                (updatedRoute) => updatedRoute.id == element['id']) ==
            -1)
        .toList();
    for (final deletedRoute in deletedRoutes) {
      try {
        DocumentSnapshot doc = await collection.doc(deletedRoute['id']).get();
        if (!doc.exists) {
          log('Excluindo rota de venda do cache(hive): ${deletedRoute['id']} - ${deletedRoute['name']} - ${deletedRoute['salesDay']}');
          await box!.delete(deletedRoute['id']);
        }
      } catch (e) {
        log('Erro ao verificar se a rota de venda foi excluída: $e');
      }
    }
  }

  Future<void> loadCache() async {
    log('Carregando rotas de venda em cache(hive)');
    cachedRoutes = box!.values.map((e) {
      var sale =
          SaleRouteModel(name: '', salesDay: 0, lastModified: Timestamp.now());
      sale.name = e['name'];
      sale.salesDay = e['salesDay'];
      sale.lastModified = Timestamp.fromDate(DateTime.parse(e['lastModified']));
      sale.id = e['id'];
      sale.isDeleted = e['isDeleted'];
      return sale;
    }).toList();
    sortCache();
    log('Rotas de venda carregadas em cache(hive): ${cachedRoutes.length}');
  }

  @override
  Future<Timestamp> getLastUpdated() async {
    boxLastUpdated ??= await Hive.openBox('sales_routes_last_updated');
    final lastUpdated = boxLastUpdated!.get('lastUpdated');
    if (lastUpdated == null) {
      final newlastUpdated = Timestamp.now();
      await _setLastUpdated(newlastUpdated);
      return newlastUpdated;
    }
    return Timestamp.fromDate(lastUpdated);
  }

  Future<void> _setLastUpdated(Timestamp timestamp) async {
    boxLastUpdated ??= await Hive.openBox('sales_routes_last_updated');
    await boxLastUpdated!.put('lastUpdated', timestamp.toDate());
  }

  @override
  Future<bool> checkIfHasCountMismatch() async {
    try {
      final countCache = cachedRoutes.length;
      final countDoc = await countReference.get();
      var countFirestore =
          (countDoc.data() as Map<String, dynamic>)['value'] as num;
      if (countCache != countFirestore) {
        log('Quantidade de rotas de venda no Firestore é diferente da quantidade em cache: $countFirestore != $countCache');
        await updateCache();
      }
      return countCache != countFirestore;
    } catch (e) {
      if (e is PlatformException) {
        log('Erro de plataforma ao verificar se há diferença de quantidade de rotas de venda: ${e.message}');
        if ((e).details['code'] == 'permission-denied') {
          log('Sem permissão para acessar rotas de venda');
        } else if ((e).details['code'] == 'unavailable') {
          log('Sem conexão com a internet para acessar rotas de venda');
        }
      } else {
        log('Erro ao verificar se há diferença de quantidade de rotas de venda: $e');
        throw Exception(
            'Erro ao verificar se há diferença de quantidade de rotas de venda $e');
      }
      return false;
    }
  }

  @override
  Future<void> updateCache() async {
    log('Atualizando cache de rotas de venda (Firestore)');
    final querySnapshot = await collection.orderBy('salesDay').get();
    _cachedRoutes = querySnapshot.docs.map((doc) {
      final data = doc.data() as Map<String, dynamic>;
      var sale = SaleRouteModel.fromMap(data);
      sale.id = doc.id;
      return sale;
    }).toList();
    saveCache();
  }

  @override
  List<SaleRouteModel> getSalesRoutesFromCache() {
    return cachedRoutes.where((element) => !element.isDeleted).toList();
  }

  Future<void> addToUpdateLastModified(SaleRouteModel saleRouteModel) async {
    log('Adicionando sale route para atualizar lastModified');
    toUpdateLastModifiedBox ??=
        await Hive.openBox('toUpdateLastModified-SalesRoutes');
    toUpdateLastModifiedBox!.put(saleRouteModel.id, saleRouteModel.toMap());

    toUpdateLastModified.add(saleRouteModel);
  }

  Future<void> verifySalesRoutesToUpdateLastModified() async {
    List<SaleRouteModel> removeList = [];
    for (final saleRoute in toUpdateLastModified) {
      final hasConnection =
          await Get.find<ConnectionService>().checkConnection();
      if (hasConnection) {
        log('Atualizando lastModified do sale route: ${saleRoute.id}');
        await updateSaleRoute(saleRoute);
        toUpdateLastModifiedBox!.delete(saleRoute.id);
        removeList.add(saleRoute);
      }
    }
    toUpdateLastModified.removeWhere((element) => removeList.contains(element));
  }

  loadToUpdateLastModified() {
    toUpdateLastModifiedBox ??= Hive.box('toUpdateLastModified-SalesRoutes');
    toUpdateLastModified = toUpdateLastModifiedBox!.values.map((e) {
      var sale =
          SaleRouteModel(name: '', salesDay: 0, lastModified: Timestamp.now());
      sale.name = e['name'];
      sale.salesDay = e['salesDay'];
      sale.lastModified = Timestamp.fromDate(DateTime.parse(e['lastModified']));
      sale.id = e['id'];
      sale.isDeleted = e['isDeleted'];
      return sale;
    }).toList();
    log('Sale routes para atualizar lastModified carregados: ${toUpdateLastModified.length}');
  }

  Future<void> saveThisToCache(SaleRouteModel saleRouteModel) async {
    box ??= await Hive.openBox('sales_routes');
    await box!.put(saleRouteModel.id, saleRouteModel.toMap());
    log('Rota de venda salva em cache(hive): ${saleRouteModel.id} - ${saleRouteModel.name} - ${saleRouteModel.salesDay}');
  }

  @override
  Future<void> resetCache() async {
    box ??= await Hive.openBox('sales_routes');
    await box!.clear();
    _cachedRoutes = <SaleRouteModel>[];
    log('Cache de rotas de venda resetado');
  }

  @override
  String? getSaleRouteName(String id) {
    final salesRoutesCache = getSalesRoutesFromCache();
    final saleRoute =
        salesRoutesCache.firstWhereOrNull((element) => element.id == id);

    return saleRoute?.name;
  }
}
