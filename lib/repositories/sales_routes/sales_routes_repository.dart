import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/sale_route_model.dart';

abstract class SalesRoutesRepository {
  Future<List<SaleRouteModel>> getSalesRoutes();
  List<SaleRouteModel> getSalesRoutesFromCache();
  Future<void> addSaleRoute(SaleRouteModel saleRouteModel);
  Future<void> updateSaleRoute(SaleRouteModel saleRouteModel);
  Future<void> deleteSaleRoute(SaleRouteModel saleRouteModel);
  Future<Timestamp> getLastUpdated();

  String? getSaleRouteName(String id);

  Future<void> updateCache();
  Future<void> resetCache();
  Future<bool> checkIfHasCountMismatch();
}
