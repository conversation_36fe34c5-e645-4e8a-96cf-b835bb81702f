import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fl_app/models/user_model.dart';

abstract class UserRepository {
  Future<UserCredential> login();
  Future<void> logout();
  Future<List<UserModel>> getUsers();
  Future<UserModel?> getUser(String id);
  Future<UserModel> addUser(UserModel user);
  Future<void> updateUser(UserModel user);
  Future<void> deleteUser(UserModel user);
  Future<bool> isLogged();

  Future<List<UserModel>> getUsersFromCache();
  Future<UserModel?> getUserAuthenticated();

  Future<Timestamp> getLastUpdated();

  Future<UserModel?> reloadUserAuthenticated();
  Future<List<String>> getOtherUsersIds();
}
