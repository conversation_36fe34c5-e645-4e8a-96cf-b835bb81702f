import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_information/device_information.dart';
import 'package:diacritic/diacritic.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hive_flutter/hive_flutter.dart';

class UserRepositoryImpl implements UserRepository {
  final firestoreInstance = FirebaseFirestore.instance;
  final CollectionReference collection =
      FirebaseFirestore.instance.collection('users');

  final DocumentReference countReference =
      FirebaseFirestore.instance.doc('count/users');

  List<UserModel> _cachedUsers = <UserModel>[];
  List<UserModel> get cachedUsers => List.from(_cachedUsers);
  set cachedUsers(List<UserModel> value) => _cachedUsers = value;

  Box? box;
  Box? boxLastUpdated;

  UserModel? _userAuthenticated;

  UserRepositoryImpl() {
    init();
  }

  Future<void> init() async {
    if (box == null) {
      box = await Hive.openBox('users');
      await loadCache();
      sortCache();
    }
    final users = await getUsersFromCache();
    if (users.isNotEmpty) {
      _userAuthenticated = users.firstWhereOrNull(
          (user) => user.id == FirebaseAuth.instance.currentUser?.uid);
      if (_userAuthenticated != null) {
        log('Usuário autenticado: ${_userAuthenticated!.name}');
        Get.put<UserModel>(_userAuthenticated!);
      } else {
        log('Usuário não autenticado');
      }
    }
  }

  @override
  Future<UserModel?> getUserAuthenticated() async {
    if (_userAuthenticated == null) {
      final users = await getUsers();
      _userAuthenticated = users.firstWhereOrNull(
          (user) => user.id == FirebaseAuth.instance.currentUser?.uid);
    }
    return _userAuthenticated;
  }

  @override
  Future<UserModel> addUser(UserModel user) async {
    try {
      final newUserRef = collection.doc(user.id);
      user.id = newUserRef.id;
      newUserRef.set(user.toDocument());
      _cachedUsers.add(user);
      sortCache();
      await saveThisToCache(user);
      return user;
    } catch (e) {
      log('Erro ao adicionar usuário: $e');
      throw Exception('Erro ao adicionar usuário $e');
    }
  }

  @override
  Future<void> deleteUser(UserModel user) async {
    throw Exception('Adicionar soft delete');
  }

  @override
  Future<UserModel> getUser(String id) async {
    if (box == null) {
      box = await Hive.openBox('users');
      await loadCache();
    }
    if (cachedUsers.isEmpty) {
      await updateCache();
    }
    return cachedUsers.firstWhere((user) => user.id == id);
  }

  @override
  Future<List<UserModel>> getUsers() async {
    box ??= await Hive.openBox('users');
    try {
      Timestamp lastUpdated = await getLastUpdated();
      Timestamp newLastUpdated = Timestamp.now();
      if (box == null) {
        box = await Hive.openBox('users');
        await loadCache();
      }
      if (cachedUsers.isNotEmpty) {
        log('Verificando se há usuários mais recentes no Firestore, desde ${lastUpdated.toDate()}');
        final querySnapshot = await collection
            .where('lastModified', isGreaterThan: lastUpdated)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          final updatedUsers = querySnapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            var user = UserModel.fromMap(data);
            user.id = doc.id;
            return user;
          }).toList();
          log('Quantidade de usuários em cache: ${cachedUsers.length}');
          log('Quantidade de usuários atualizados: ${updatedUsers.length}');
          for (final updatedUser in updatedUsers) {
            final index = _cachedUsers
                .indexWhere((cachedUser) => cachedUser.id == updatedUser.id);
            if (index == -1) {
              log('Usuário não encontrado em cache, adicionando');
              _cachedUsers.add(updatedUser);
            } else {
              log('Usuário encontrado em cache, atualizando');
              _cachedUsers[index] = updatedUser;
            }
            if (updatedUser.id == _userAuthenticated?.id) {
              _userAuthenticated = updatedUser;
              Get.put<UserModel>(_userAuthenticated!);
            }
          }
          sortCache();
          await saveCache();
        }
        checkIfHasCountMismatch();
      } else {
        log('Buscando usuários no Firestore');
        await updateCache();
      }
      if (await Get.find<ConnectionService>().checkConnection()) {
        _setLastUpdated(newLastUpdated);
      }
      verifyPhoneModel();
      return cachedUsers;
    } catch (e) {
      if (await Get.find<UserRepository>().isLogged()) {
        log('Erro ao buscar usuários: $e');
        throw Exception('Erro ao buscar usuários $e');
      }
      log('Erro ao buscar usuários: $e');
      return cachedUsers;
    }
  }

  @override
  Future<void> updateUser(UserModel user) async {
    try {
      user.lastModified = Timestamp.now();
      collection.doc(user.id).update(user.toDocument());
      final index =
          cachedUsers.indexWhere((cachedUser) => cachedUser.id == user.id);
      final tempUsers = cachedUsers;
      tempUsers[index] = user;
      cachedUsers = tempUsers;
      sortCache();
      await saveThisToCache(user);
    } catch (e) {
      log('Erro ao atualizar usuário: $e');
      throw Exception('Erro ao atualizar usuário $e');
    }
  }

  void sortCache() {
    _cachedUsers.sort((a, b) => removeDiacritics(a.name.toLowerCase())
        .compareTo(removeDiacritics(b.name.toLowerCase())));
  }

  Future<void> saveCache() async {
    box ??= await Hive.openBox('users');
    log('Salvando usuários em cache(hive)');
    List<UserModel> updatedUsers = [];

    for (final user in cachedUsers) {
      await box!.put(user.id, user.toMap());
      updatedUsers.add(user);
    }
    log('Usuários salvos em cache(hive): ${updatedUsers.length}');
    //verify deleted users
    final deletedUsers = box!.values
        .where((element) =>
            updatedUsers
                .indexWhere((updatedUser) => updatedUser.id == element['id']) ==
            -1)
        .toList();
    for (final deletedUser in deletedUsers) {
      try {
        DocumentSnapshot doc = await collection.doc(deletedUser['id']).get();
        if (!doc.exists) {
          log('Excluindo usuário do cache(hive): ${deletedUser['name']} - ${deletedUser['id']} - ${deletedUser['email']}');
          await box!.delete(deletedUser['id']);
        }
      } catch (e) {
        log('Erro ao verificar se o usuário foi excluído: $e');
      }
    }
  }

  Future<void> loadCache() async {
    log('Carregando usuários em cache(hive)');
    cachedUsers = box!.values.map((e) {
      e['lastModified'] = Timestamp.fromDate(DateTime.parse(e['lastModified']));
      return UserModel.fromMap(e as Map<dynamic, dynamic>);
    }).toList();
    sortCache();
    log('Usuários carregados em cache(hive): ${cachedUsers.length}');
  }

  @override
  Future<Timestamp> getLastUpdated() async {
    boxLastUpdated ??= await Hive.openBox('users_last_updated');
    final lastUpdated = boxLastUpdated!.get('lastUpdated');
    if (lastUpdated == null) {
      final newLastUpdated = Timestamp.now();
      await _setLastUpdated(newLastUpdated);
      return newLastUpdated;
    }
    return Timestamp.fromDate(lastUpdated);
  }

  Future<void> _setLastUpdated(Timestamp timestamp) async {
    boxLastUpdated ??= await Hive.openBox('users_last_updated');
    await boxLastUpdated!.put('lastUpdated', timestamp.toDate());
  }

  @override
  Future<List<UserModel>> getUsersFromCache() async {
    if (box == null) {
      box = await Hive.openBox('users');
      await loadCache();
    }
    getUsers();
    return cachedUsers;
  }

  @override
  Future<bool> isLogged() async {
    return FirebaseAuth.instance.currentUser != null;
  }

  @override
  Future<UserCredential> login() async {
    final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();

    final GoogleSignInAuthentication? googleAuth =
        await googleUser?.authentication;

    if (googleAuth != null) {
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      return await FirebaseAuth.instance.signInWithCredential(credential);
    } else {
      await logout();
      throw Exception('Erro ao fazer login com o google');
    }
  }

  @override
  Future<void> logout() async {
    await GoogleSignIn().signOut();
    FirebaseAuth.instance.signOut();
    _userAuthenticated = null;
    Get.delete<UserModel>();
  }

  Future<void> updateCache() async {
    try {
      log('Atualizando cache de usuários (Firestore)');
      final querySnapshot = await collection.get();
      cachedUsers = querySnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        var user = UserModel.fromMap(data);
        user.id = doc.id;
        return user;
      }).toList();
      sortCache();
      await saveCache();
    } catch (e) {
      log('Erro ao atualizar cache de usuários: $e');
      throw Exception('Erro ao atualizar cache de usuários $e');
    }
  }

  Future<bool> checkIfHasCountMismatch() async {
    try {
      final countCache = cachedUsers.length;
      final countDoc = await countReference.get();
      var countFirestore =
          (countDoc.data() as Map<String, dynamic>)['value'] as num;
      if (countCache != countFirestore) {
        log('Quantidade de usuários no Firestore é diferente da quantidade em cache: $countFirestore != $countCache');
        await updateCache();
      }
      return countCache != countFirestore;
    } catch (e) {
      if (e is PlatformException) {
        log('Erro de plataforma ao verificar se há diferença de quantidade de usuários: ${e.message}');
        if ((e).details['code'] == 'permission-denied') {
          log('Sem permissão para acessar usuários');
        } else if ((e).details['code'] == 'unavailable') {
          log('Sem conexão com a internet para acessar usuários');
        }
      } else {
        log('Erro ao verificar se há diferença de quantidade de usuários: $e');
        throw Exception(
            'Erro ao verificar se há diferença de quantidade de usuários $e');
      }
      return false;
    }
  }

  Future<void> saveThisToCache(UserModel user) async {
    box ??= await Hive.openBox('users');
    await box!.put(user.id, user.toMap());
    log('Usuário salvo em cache(hive): ${user.name}');
  }

  Future<void> verifyPhoneModel() async {
    var user = _userAuthenticated;
    if (user != null && user.phoneModel.isEmpty) {
      user.phoneModel = await DeviceInformation.deviceModel;
      updateUser(user);
    } else if (user != null) {
      final phoneModel = await DeviceInformation.deviceModel;
      if (user.phoneModel != phoneModel) {
        Get.showSnackbar(
          const GetSnackBar(
            title: 'Atenção',
            message:
                'Essa conta está sendo utilizada em outro dispositivo e apenas uma sessão é permitida por vez. Faça login novamente para continuar.',
            duration: Duration(seconds: 5),
          ),
        );
        await Future.delayed(const Duration(seconds: 5));
        await logout();
        Get.offAllNamed('/splash');
      }
    }
  }

  Future<void> updateUserLogged() async {
    _userAuthenticated = cachedUsers.firstWhereOrNull(
        (user) => user.id == FirebaseAuth.instance.currentUser!.uid);
    if (_userAuthenticated != null) {
      log('Usuário autenticado: ${_userAuthenticated!.name}');
      Get.replace<UserModel>(_userAuthenticated!);
    } else {
      log('Usuário não autenticado');
    }
  }

  @override
  Future<UserModel?> reloadUserAuthenticated() async {
    await getUsers();
    await updateUserLogged();
    return _userAuthenticated;
  }

  @override
  Future<List<String>> getOtherUsersIds() async {
    var user = await getUserAuthenticated();
    var users = await getUsersFromCache();
    var usersId = users.map((e) => e.id).toList();
    usersId.removeWhere((element) => element == user!.id);
    return usersId;
  }
}
