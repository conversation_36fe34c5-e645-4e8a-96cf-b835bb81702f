import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/models/salary_advance.dart';
import 'package:fl_app/repositories/user/salary_advance/salary_advance_repository.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

class SalaryAdvanceRepositoryImpl implements SalaryAdvanceRepository {
  final firestoreInstance = FirebaseFirestore.instance;
  final CollectionReference userCollection =
      FirebaseFirestore.instance.collection('users');

  Box? box;

  List<SalaryAdvance> _cachedSalaryAdvances = <SalaryAdvance>[];
  @override
  List<SalaryAdvance> get cachedSalaryAdvances =>
      List.from(_cachedSalaryAdvances);
  set cachedSalaryAdvances(List<SalaryAdvance> value) =>
      _cachedSalaryAdvances = value;

  SalaryAdvanceRepositoryImpl() {
    init();
  }

  void init() async {
    box = await Hive.openBox('salaryAdvances');
    await loadCache();
  }

  @override
  Future<List<SalaryAdvance>> getSalaryAdvancesForUser(String userId) async {
    try {
      if (!(await Get.find<ConnectionService>().checkConnection())) {
        return _cachedSalaryAdvances.where((x) => x.userId == userId).toList();
      }
      final querySnapshot = await userCollection
          .doc(userId.toString())
          .collection('salary_advances')
          .get();
      var result = querySnapshot.docs.map((doc) {
        var salaryAdvance = SalaryAdvance.fromDocument(doc.data());
        salaryAdvance.id = doc.id;
        return salaryAdvance;
      }).toList();
      addAllToCache(result);
      sortCache();
      saveCache(userId);

      return _cachedSalaryAdvances.where((x) => x.userId == userId).toList();
    } catch (e) {
      return _cachedSalaryAdvances.where((x) => x.userId == userId).toList();
    }
  }

  @override
  Future<SalaryAdvance> create(SalaryAdvance salaryAdvance) async {
    try {
      final DocumentReference userDoc =
          userCollection.doc(salaryAdvance.userId);
      final CollectionReference salaryAdvanceCollection =
          userDoc.collection('salary_advances');
      final DocumentReference newSalaryAdvanceRef =
          salaryAdvanceCollection.doc();
      salaryAdvance.id = newSalaryAdvanceRef.id;
      newSalaryAdvanceRef.set(salaryAdvance.toMap());
      await _addToCache(salaryAdvance);
      return salaryAdvance;
    } catch (e) {
      log('Erro ao adicionar adiantamento salarial: $e');
      throw Exception('Erro ao adicionar adiantamento salarial $e');
    }
  }

  @override
  Future<void> delete(SalaryAdvance salaryAdvance) async {
    try {
      final DocumentReference userDoc =
          userCollection.doc(salaryAdvance.userId.toString());
      final CollectionReference salaryAdvanceCollection =
          userDoc.collection('salary_advances');
      salaryAdvanceCollection.doc(salaryAdvance.id).delete();
      _removeFromCache(salaryAdvance);
    } catch (e) {
      log('Erro ao excluir adiantamento salarial: $e');
      throw Exception('Erro ao excluir adiantamento salarial $e');
    }
  }

  Future<void> loadCache() async {
    if (box!.isNotEmpty) {
      _cachedSalaryAdvances =
          box!.values.map((e) => SalaryAdvance.fromMap(e)).toList();
    }
    sortCache();
    log('Cache de adiantamentos salariais carregado com ${_cachedSalaryAdvances.length} registros');
  }

  Future _addToCache(SalaryAdvance salaryAdvance) async {
    box ??= await Hive.openBox('salaryAdvances');
    _cachedSalaryAdvances.add(salaryAdvance);
    box!.put(salaryAdvance.id, salaryAdvance.toMap());
  }

  Future _removeFromCache(SalaryAdvance salaryAdvance) async {
    box ??= await Hive.openBox('salaryAdvances');
    _cachedSalaryAdvances.removeWhere((x) => x.id == salaryAdvance.id);
    box!.delete(salaryAdvance.id);
  }

  void sortCache() {
    _cachedSalaryAdvances.sort((a, b) => a.date.compareTo(b.date));
  }

  void saveCache(String userId) {
    box ??= Hive.box('salaryAdvances');
    for (var element in box!.values) {
      if (element['userId'] == userId) {
        box!.delete(element['id']);
      }
    }
    for (var salaryAdvance in _cachedSalaryAdvances) {
      if (salaryAdvance.userId == userId) {
        box!.put(salaryAdvance.id, salaryAdvance.toMap());
      }
    }
  }

  void addAllToCache(List<SalaryAdvance> result) {
    for (var salaryAdvance in result) {
      var index =
          _cachedSalaryAdvances.indexWhere((x) => x.id == salaryAdvance.id);
      if (index == -1) {
        _cachedSalaryAdvances.add(salaryAdvance);
      } else {
        _cachedSalaryAdvances[index] = salaryAdvance;
      }
    }
  }
}
