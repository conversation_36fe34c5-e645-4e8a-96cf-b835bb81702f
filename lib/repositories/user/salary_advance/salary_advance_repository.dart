import 'package:fl_app/models/salary_advance.dart';

abstract class SalaryAdvanceRepository {
  List<SalaryAdvance> get cachedSalaryAdvances;
  // Obter todos os adiantamentos salariais para um usuário específico
  Future<List<SalaryAdvance>> getSalaryAdvancesForUser(String userId);

  // Criar um novo adiantamento salarial
  Future<SalaryAdvance> create(SalaryAdvance salaryAdvance);

  // Excluir um adiantamento salarial
  Future<void> delete(SalaryAdvance salaryAdvance);
}
