// import 'dart:async';
// import 'dart:developer';

// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:fl_app/data/datasources/local/order/local_order_data_source.dart';
// import 'package:fl_app/data/datasources/remote/order/remote_order_data_source.dart';
// import 'package:fl_app/models/order_model.dart';
// import 'package:fl_app/models/user_model.dart';
// import 'package:fl_app/repositories/user/user_repository.dart';
// import 'package:fl_app/services/user/user_service.dart';
// import 'package:flutter/foundation.dart';
// import 'package:get/get.dart';
// import 'package:hive_flutter/hive_flutter.dart';

// import './order_repository.dart';

// List<OrderModel> processBatch(List<Map<String, dynamic>> docsData) {
//   return docsData.map((data) {
//     return OrderModel.fromDocumentMap(data);
//   }).toList();
// }

// // Função para processar dados em cache no isolate
// List<OrderModel> processCachedData(List<Map<dynamic, dynamic>> data) {
//   return data.map((e) => OrderModel.fromCache(e)).toList();
// }

// class OrderRepositoryImpl implements OrderRepository {
//   final RemoteOrderDataSource remoteDataSource;
//   final LocalOrderDataSource localDataSource;
//   final UserRepository userRepository;

//   UserModel? user;

//   OrderRepositoryImpl({
//     required this.remoteDataSource,
//     required this.localDataSource,
//     required this.userRepository,
//   });

//   @override
//   Future<void> initialize() async {
//     user = await userRepository.getUserAuthenticated();
//     await localDataSource.initialize();
//     getOrders();
//   }

//   @override
//   Future<OrderModel> addOrder(OrderModel order) async {
//     try {
//       user ??= await Get.find<UserService>().getUserAuthenticated();
//       var usersId = await userRepository.getOtherUsersIds();

//       order.toReceivedBy = [...usersId];

//       final id = remoteDataSource.addOrder(order);
//       order.id = id;
//       await localDataSource.saveOrder(order);
//       return order;
//     } catch (e) {
//       log('Erro ao adicionar pedido: $e');
//       throw Exception('Erro ao adicionar pedido $e');
//     }
//   }

//   @override
//   Future<void> deleteOrder(OrderModel order) async {
//     try {
//       order.isDeleted = true;
//       order.deletedBy = user!.name;
//       order.deletedById = user!.id;
//       order.deletedAt = DateTime.now();
//       order.lastModified = Timestamp.now();
//       await updateOrder(order);
//     } catch (e) {
//       log('Erro ao excluir pedido: $e');
//       throw Exception('Erro ao excluir pedido $e');
//     }
//   }

//   @override
//   Future<OrderModel> restoreOrder(OrderModel order) async {
//     try {
//       order.isDeleted = false;
//       order.deletedBy = null;
//       order.deletedById = null;
//       order.deletedAt = null;

//       order.lastModified = Timestamp.now();
//       await updateOrder(order);
//       return order;
//     } catch (e) {
//       log('Erro ao restaurar pedido: $e');
//       throw Exception('Erro ao restaurar pedido $e');
//     }
//   }

//   @override
//   Future<OrderModel> getOrder(String id) async {
//     return cachedOrders[id]!;
//   }

//   @override
//   Future<List<OrderModel>> getOrders() async {
//     try {
//       if (box == null) {
//         box = await Hive.openBox('orders');
//         await loadCache();
//       }
//       user ??= await Get.find<UserService>().getUserAuthenticated();
//       if (cachedOrders.isNotEmpty) {
//         log('Buscando pedidos em cache e verificando se tem algum toReceivedBy');
//         // buscar pedidos onde receivedBy não contém o id do usuário
//         final querySnapshot = await collection
//             .where('toReceivedBy', arrayContains: user!.id)
//             .get();

//         if (querySnapshot.docs.isNotEmpty) {
//           // Se houver documentos mais recentes no Firestore, atualize apenas os documentos alterados
//           final updatedOrders = querySnapshot.docs.map((doc) {
//             var order = OrderModel.fromDocument(doc);
//             order.id = doc.id;
//             return order;
//           }).toList();
//           log('quantidade de pedidos em cache: ${cachedOrders.length}');
//           log('quantidade de pedidos atualizadas: ${updatedOrders.length}');
//           for (final updatedOrder in updatedOrders) {
//             _cachedOrders[updatedOrder.id!] = updatedOrder;
//             await updateOrderReceivedBy(updatedOrder);
//           }
//         }
//       } else {
//         log('Buscando pedidos em no Firestore');
//         await updateCache();
//       }

//       var orders =
//           cachedOrders.values.where((element) => !element.isDeleted).toList();
//       orders.sort((a, b) => a.date.compareTo(b.date) * -1);
//       return orders;
//     } catch (e) {
//       log('Erro ao obter pedidos: $e');
//       throw Exception('Erro ao obter pedidos $e');
//     }
//   }

//   @override
//   Future<OrderModel> updateOrder(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();

//     order.lastModified = Timestamp.now();

//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocument());
//     _cachedOrders[order.id!] = order;
//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> updateClientInfo(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentClientInfo());
//     _cachedOrders[order.id!] = order;
//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> updatePayments(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentPayments());
//     _cachedOrders[order.id!] = order;

//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> markAsDelivered(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.toDelivery = false;
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update({
//       'toDelivery': false,
//       'lastModified': order.lastModified,
//       'toReceivedBy': order.toReceivedBy,
//     });
//     _cachedOrders[order.id!] = order;
//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> markAsJoined(OrderModel order,
//       {String? orderJoinedId}) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.isPaid = false;
//     order.isJoined = true;
//     order.joinedAt = DateTime.now();
//     order.joinedBy = user!.name;
//     order.joinedById = user!.id;
//     order.joinedInOrderId = orderJoinedId;

//     for (var e in order.dayMarkingItems) {
//       e.active = false;
//     }
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentPayments());
//     _cachedOrders[order.id!] = order;
//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> markAsUnjoined(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.isJoined = false;
//     order.joinedAt = null;
//     order.joinedBy = null;
//     order.joinedById = null;
//     order.joinedInOrderId = null;

//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentPayments());
//     _cachedOrders[order.id!] = order;

//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> markAsPaid(
//       OrderModel order, PaymentMethod paymentMethod, DateTime date) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.isPaid = true;
//     if (order.getTotalPending() > 0) {
//       order.payments.add(Payment(
//         date: date,
//         value: order.getTotalPending(),
//         paymentMethod: paymentMethod,
//         userId: user!.id,
//         userName: user!.name,
//       ));
//     }
//     order.isJoined = false;
//     for (var e in order.dayMarkingItems) {
//       e.active = false;
//     }
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentPayments());
//     _cachedOrders[order.id!] = order;
//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> markAsUnpaid(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.isPaid = false;
//     //verificar se a forma de pagamento é das antigas
//     if (order.payments.isNotEmpty && order.payments.last.userId != null) {
//       order.payments.removeLast();
//     }
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentPayments());
//     _cachedOrders[order.id!] = order;
//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> updateDayMarking(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentDayMarking());
//     _cachedOrders[order.id!] = order;

//     await saveThisToCache(order);
//     return order;
//   }

//   @override
//   Future<OrderModel> markProductsAsDelivered(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();

//     for (var element in order.products) {
//       element['quantityToDelivery'] = 0;
//     }
//     order.lastModified = Timestamp.now();
//     var users = await userRepositoryImpl.getUsersFromCache();
//     var usersId = users.map((e) => e.id).toList();
//     usersId.removeWhere((element) => element == user!.id);
//     order.toReceivedBy = [...usersId];

//     collection.doc(order.id).update(order.toDocumentProducts());
//     _cachedOrders[order.id!] = order;

//     await saveThisToCache(order);
//     return order;
//   }

//   Future<OrderModel> updateOrderReceivedBy(OrderModel order) async {
//     user ??= await Get.find<UserService>().getUserAuthenticated();

//     order.toReceivedBy =
//         order.toReceivedBy.where((element) => element != user!.id).toList();

//     _cachedOrders[order.id!] = order;
//     await saveThisToCache(order);
//     collection.doc(order.id).update(order.toDocumentReceivedBy());
//     return order;
//   }

//   @override
//   Future<void> updateCache() async {
//     log('Atualizando cache de pedidos (Firestore)');
//     DateTime now = DateTime.now();
//     DateTime sixMonthsAgo = now.subtract(const Duration(days: 180));
//     DateTime fourMonthsAgo = now.subtract(const Duration(days: 120));

//     int batchSize = 3000; // Define the size of each batch
//     Query query = collection
//         .where(
//           Filter.or(
//             // Pedidos pendentes
//             Filter.and(
//               Filter("isPaid", isEqualTo: false),
//               Filter("isJoined", isEqualTo: false),
//               Filter("isDeleted", isEqualTo: false),
//             ),
//             // Pedidos pagos há mais de 6 meses
//             Filter.and(
//               Filter("isPaid", isEqualTo: true),
//               Filter("lastModified", isGreaterThan: sixMonthsAgo),
//             ),
//             // Pedidos deletados há mais de 4 meses
//             Filter.and(
//               Filter("isDeleted", isEqualTo: true),
//               Filter("lastModified", isGreaterThan: fourMonthsAgo),
//             ),
//             // Pedidos juntados há mais de 6 meses
//             Filter.and(
//               Filter("isJoined", isEqualTo: true),
//               Filter("lastModified", isGreaterThan: sixMonthsAgo),
//             ),
//           ),
//         )
//         .orderBy('lastModified')
//         .limit(batchSize);

//     List<OrderModel> allOrders = [];

//     DocumentSnapshot? lastDocument;

//     while (true) {
//       Query currentQuery = query;
//       if (lastDocument != null) {
//         currentQuery = currentQuery.startAfterDocument(lastDocument);
//       }

//       QuerySnapshot querySnapshot = await currentQuery.get();

//       if (querySnapshot.docs.isEmpty) {
//         break;
//       }

//       // Extract data to pass to isolate
//       List<Map<String, dynamic>> docsData = querySnapshot.docs.map((doc) {
//         var data = doc.data() as Map<String, dynamic>;
//         data['id'] = doc.id;
//         return data;
//       }).toList();

//       List<OrderModel> orders;
//       try {
//         orders = await compute(processBatch, docsData);
//       } catch (e) {
//         log('Erro ao processar lote no isolate: $e');
//         throw Exception('Erro ao processar lote no isolate: $e');
//       }

//       // Add to allOrders
//       allOrders.addAll(orders);

//       // Update lastDocument
//       lastDocument = querySnapshot.docs.last;

//       // Verifica se o número de documentos retornados é menor que o tamanho do lote
//       if (querySnapshot.docs.length < batchSize) {
//         break;
//       }
//     }

//     // Update the cache
//     _cachedOrders =
//         allOrders.asMap().map((key, value) => MapEntry(value.id!, value));

//     box ??= await Hive.openBox('orders');
//     await box!.clear();
//     await saveCache();

//     log('Pedidos atualizados em cache (Firestore): ${_cachedOrders.length}');
//   }

//   @override
//   Future<void> updateCacheByRoutes(List<String> routesIds) async {
//     log('Atualizando cache de pedidos (Firestore) nas rotas: $routesIds');

//     final querySnapshot = await collection
//         .where(Filter.or(
//           Filter("routeSaleId", whereIn: routesIds),
//           Filter("routeId", whereIn: routesIds),
//         ))
//         .get();

//     var tempOrders = querySnapshot.docs.map((doc) {
//       var order = OrderModel.fromDocument(doc);
//       order.id = doc.id;
//       return order;
//     }).toList();

//     // Atualiza o cache
//     for (final order in tempOrders) {
//       _cachedOrders[order.id!] = order;
//     }

//     // Salva os pedidos em cache em lote
//     await saveOrdersToCache(tempOrders);
//   }

//   Future<void> saveCache() async {
//     box ??= await Hive.openBox('orders');
//     log('Salvando pedidos em cache (Hive), oldlength: ${box!.length}');

//     Map<String, Map<String, dynamic>> ordersMap = {};
//     try {
//       for (final order in _cachedOrders.values) {
//         ordersMap[order.id!] = order.toMap();
//       }
//       await box!.putAll(ordersMap);
//     } catch (e) {
//       log('Erro ao salvar pedidos em cache (Hive): $e');
//     }

//     log('Pedidos salvos em cache (Hive): ${ordersMap.length}');
//   }

//   @override
//   Future<void> loadCache() async {
//     log('Carregando pedidos em cache (Hive)');

//     // Verifica se a caixa está aberta
//     if (box == null || !box!.isOpen) {
//       box = await Hive.openBox('orders');
//     }

//     // Obtém todos os dados da caixa Hive como List<Map<String, dynamic>>
//     List<Map<String, dynamic>> cachedData =
//         box!.values.map((e) => Map<String, dynamic>.from(e as Map)).toList();

//     // Processa os dados em um isolate
//     List<OrderModel> orders;
//     try {
//       orders = await compute(processCachedData, cachedData);
//     } catch (e) {
//       log('Erro ao processar dados em cache no isolate: $e');
//       throw Exception('Erro ao processar dados em cache no isolate: $e');
//     }

//     // Atualiza o cache na memória
//     _cachedOrders =
//         orders.asMap().map((key, value) => MapEntry(value.id!, value));

//     log('Pedidos carregados em cache (Hive): ${cachedOrders.length}');

//     // Remove pedidos antigos do cache
//     removeOldPaidOrJoinedOrdersFromCache();
//     removeOldDeletedOrdersFromCache();
//     removeOldJoinedOrdersFromCache();
//   }

//   @override
//   void removeOldPaidOrJoinedOrdersFromCache() {
//     final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
//     final oldPaidOrders = _cachedOrders.values
//         .where((order) =>
//             (order.isPaid &&
//                 order.lastModified.toDate().isBefore(sixMonthsAgo)) ||
//             (order.isJoined &&
//                 order.lastModified.toDate().isBefore(sixMonthsAgo)))
//         .toList();

//     for (final order in oldPaidOrders) {
//       _cachedOrders.remove(order.id);
//       box?.delete(order.id);
//     }

//     log('Pedidos pagos ou juntados sem data de quando foi há mais de 6 meses removidos do cache: ${oldPaidOrders.length}');
//   }

//   @override
//   void removeOldDeletedOrdersFromCache() {
//     final fourMonthsAgo = DateTime.now().subtract(const Duration(days: 120));
//     final oldDeletedOrders = _cachedOrders.values
//         .where((order) =>
//             (order.isDeleted &&
//                 order.deletedAt != null &&
//                 order.deletedAt!.isBefore(fourMonthsAgo) &&
//                 order.lastModified.toDate().isBefore(fourMonthsAgo)) ||
//             (order.isDeleted &&
//                 order.deletedAt == null &&
//                 order.lastModified.toDate().isBefore(fourMonthsAgo)))
//         .toList();

//     for (final order in oldDeletedOrders) {
//       _cachedOrders.remove(order.id);
//       box?.delete(order.id);
//     }

//     log('Pedidos deletados há mais de 3 meses removidos do cache: ${oldDeletedOrders.length}');
//   }

//   @override
//   void removeOldJoinedOrdersFromCache() {
//     final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
//     final oldJoinedOrders = _cachedOrders.values
//         .where((order) =>
//             order.isJoined &&
//             order.joinedAt != null &&
//             order.joinedAt!.isBefore(sixMonthsAgo) &&
//             order.lastModified.toDate().isBefore(sixMonthsAgo))
//         .toList();

//     for (final order in oldJoinedOrders) {
//       _cachedOrders.remove(order.id);
//       box?.delete(order.id);
//     }

//     log('Pedidos juntados há mais de 6 meses removidos do cache: ${oldJoinedOrders.length}');
//   }

//   @override
//   Future<List<OrderModel>> getOrdersFromCache() async {
//     if (box == null) {
//       box = await Hive.openBox('orders');
//       await loadCache();
//     }

//     getOrders();
//     var orders = cachedOrders.values
//         .toList()
//         .where((element) => !element.isDeleted)
//         .toList();
//     orders.sort((a, b) => a.date.compareTo(b.date) * -1);
//     return orders;
//   }

//   @override
//   Future<List<OrderModel>> getDeletedOrdersFromCache({String? routeId}) async {
//     if (routeId != null && routeId.isNotEmpty) {
//       return cachedOrders.values
//           .toList()
//           .where((element) => element.isDeleted && element.routeId == routeId)
//           .toList();
//     }
//     return cachedOrders.values
//         .toList()
//         .where((element) => element.isDeleted)
//         .toList()
//       ..sort((a, b) => a.date.compareTo(b.date) * -1);
//   }

//   @override
//   Future<List<OrderModel>> getOrdersDelivery() async {
//     if (cachedOrders.isEmpty) {
//       await getOrders();
//     } else {
//       getOrders();
//     }
//     var orders = await getOrdersFromCache();

//     return orders
//         .where((order) =>
//             order.toDelivery ||
//             order.products.any((product) =>
//                 product['quantityToDelivery'] != null &&
//                 product['quantityToDelivery'] > 0))
//         .toList();
//   }

//   Future<void> saveOrdersToCache(List<OrderModel> orders) async {
//     box ??= await Hive.openBox('orders');
//     Map<String, Map<String, dynamic>> ordersMap = {};
//     for (var order in orders) {
//       ordersMap[order.id!] = order.toMap();
//     }
//     await box!.putAll(ordersMap);
//     log('Pedidos salvos em cache (Hive): ${orders.length}');
//   }

//   Future<void> saveThisToCache(OrderModel order) async {
//     box ??= await Hive.openBox('orders');
//     await box!.put(order.id, order.toMap());
//     log('Pedido salvo em cache (Hive): ${order.id}');
//   }

//   @override
//   Future<void> resetCache() async {
//     box ??= await Hive.openBox('orders');
//     await box!.clear();
//     _cachedOrders = {};
//     log('Cache de pedidos resetado');
//   }

//   @override
//   Future<void> syncOrder(OrderModel orderModel) async {
//     try {
//       bool orderExists = _cachedOrders[orderModel.id] != null ? true : false;
//       if (orderExists) {
//         var order = _cachedOrders[orderModel.id]!;
//         if (order.lastModified
//             .toDate()
//             .isBefore(orderModel.lastModified.toDate())) {
//           _cachedOrders[orderModel.id!] = orderModel;
//           await saveThisToCache(orderModel);
//         }
//       } else {
//         _cachedOrders[orderModel.id!] = orderModel;
//         await saveThisToCache(orderModel);
//       }
//     } catch (e) {
//       log('Erro ao sincronizar pedido: $e');
//       throw Exception('Erro ao sincronizar pedido $e');
//     }
//   }

//   @override
//   Future<List<OrderModel>> getIncorrectOrders(String name) async {
//     var orders = await getOrders();
//     var incorrectOrders = orders
//         .where((order) => order.products.any((product) =>
//             product['name'] == name && product['temComissao'] == true))
//         .toList();
//     return incorrectOrders;
//   }

//   @override
//   Future<void> removeComissionFromOrders(
//       List<OrderModel> list, String name) async {
//     for (var order in list) {
//       bool atualizar = false;
//       for (var product in order.products) {
//         if (product['name'] == name && product['temComissao'] == true) {
//           product['temComissao'] = false;
//           atualizar = true;
//         }
//       }
//       if (atualizar) {
//         log('Atualizando pedido: ${order.id}');
//         await updateOrder(order);
//       }
//     }
//   }

//   // Future<int> getFirestoreOrdersOffline() async {
//   //   final querySnapshot =
//   //       await collection.get(const GetOptions(source: Source.cache));
//   //   int count = querySnapshot.docs.where((doc) {
//   //     return doc.metadata.hasPendingWrites;
//   //   }).length;
//   //   print("Offline Pending Orders: $count");
//   //   return count;
//   // }
// }
