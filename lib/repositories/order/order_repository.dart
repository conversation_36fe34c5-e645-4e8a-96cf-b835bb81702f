// import 'package:fl_app/models/order_model.dart';

// abstract class OrderRepository {
//   Future<List<OrderModel>> getOrders();

//   Future<List<OrderModel>> getOrdersFromCache();
//   Future<List<OrderModel>> getDeletedOrdersFromCache({String? routeId});

//   Future<OrderModel> getOrder(String id);

//   Future<OrderModel> addOrder(OrderModel order);

//   Future<OrderModel> updateOrder(OrderModel order);

//   Future<void> deleteOrder(OrderModel order);

//   Future<OrderModel> restoreOrder(OrderModel order);

//   Future<void> updateCache();

//   Future<void> resetCache();

//   Future<List<OrderModel>> getOrdersDelivery();

//   Future<OrderModel> updateClientInfo(OrderModel order);

//   Future<OrderModel> updatePayments(OrderModel order);

//   Future<OrderModel> markAsDelivered(OrderModel order);

//   Future<OrderModel> markAsJoined(OrderModel order, {String? orderJoinedId});
//   Future<OrderModel> markAsUnjoined(OrderModel order);

//   Future<OrderModel> markAsPaid(
//       OrderModel order, PaymentMethod paymentMethod, DateTime date);
//   Future<OrderModel> markAsUnpaid(OrderModel order);
//   Future<OrderModel> updateDayMarking(OrderModel value);

//   Future<OrderModel> markProductsAsDelivered(OrderModel value);

//   Future<void> syncOrder(OrderModel orderModel);

//   Future<void> updateCacheByRoutes(List<String> routesIds);

//   Future<List<OrderModel>> getIncorrectOrders(String name);
//   Future<void> removeComissionFromOrders(List<OrderModel> list, String name);

//   removeOldDeletedOrdersFromCache();
//   removeOldJoinedOrdersFromCache();
//   removeOldPaidOrJoinedOrdersFromCache();

//   Future<void> initialize();
// }
