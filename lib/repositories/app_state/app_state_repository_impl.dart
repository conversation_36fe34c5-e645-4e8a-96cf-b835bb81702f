import 'dart:developer';

import 'package:fl_app/repositories/app_state/app_state_repository.dart';
import 'package:hive_flutter/hive_flutter.dart';

class AppStateRepositoryImpl implements AppStateRepository {
  AppState appState = AppState();

  Box? box;

  AppStateRepositoryImpl() {
    init();
  }

  Future<void> init() async {
    if (box == null) {
      await Hive.initFlutter();
      box = await Hive.openBox('sale');
      await loadCache();
    }
  }

  @override
  Future<void> startSaleInRoute(String routeId) async {
    appState.isSelling = true;
    appState.isSellingRouteId = routeId;
    appState.isCobrando = false;
    appState.cobrancaRouteIds = null;
    appState.secondRouteId = null;
    appState.secondRouteDate = null;

    await saveCache();
    log('Venda iniciada na rota $routeId');
  }

  @override
  Future<void> finishSaleInRoute() async {
    appState.isSelling = false;
    appState.isSellingRouteId = null;

    await saveCache();
    log('Venda finalizada');
  }

  @override
  AppState getAppState() {
    return appState;
  }

  @override
  Future<void> startCobrancaInRoutes(List<String> routes) async {
    appState.cobrancaRouteIds = routes;
    appState.isCobrando = true;
    appState.isSelling = false;
    appState.isSellingRouteId = null;
    appState.secondRouteId = null;
    appState.secondRouteDate = null;

    await saveCache();
    log('Cobrança iniciada nas rotas $routes');
  }

  @override
  Future<void> finishCobrancaInRoute() async {
    appState.cobrancaRouteIds = null;
    appState.isCobrando = false;

    await saveCache();
    log('Cobrança finalizada');
  }

  @override
  Future<void> enableSecondRoute(String routeId, DateTime date) async {
    appState.secondRouteId = routeId;
    appState.secondRouteDate = date;
    await saveCache();
    log('Segunda rota habilitada: $routeId');
  }

  @override
  Future<void> disableSecondRoute() async {
    appState.secondRouteId = null;
    appState.secondRouteDate = null;
    await saveCache();
    log('Segunda rota desabilitada');
  }

  @override
  List<String>? getCobrancaRouteIds() {
    return appState.cobrancaRouteIds;
  }

  Future<void> saveCache() async {
    log('Salvando app state: $appState');
    await box!.put('appState', appState.toMap());
  }

  Future<void> loadCache() async {
    var appStateCache = box!.get('appState');
    if (appStateCache == null) {
      await box!.put('appState', appState.toMap());
      appStateCache = box!.get('appState');
    }

    appState = AppState.fromMap(appStateCache);
    log('App State carregado: $appState');
  }

  @override
  bool isKeepAlive() {
    return appState.isKeepAlive;
  }

  @override
  void setKeepAlive(bool value) {
    appState.isKeepAlive = value;
    saveCache();
  }
}

class AppState {
  bool isSelling;
  bool isCobrando;
  String? isSellingRouteId;
  String? secondRouteId;
  DateTime? secondRouteDate;
  List<String>? cobrancaRouteIds;
  bool isKeepAlive;

  AppState({
    this.isSelling = false,
    this.isCobrando = false,
    this.isSellingRouteId,
    this.secondRouteId,
    this.secondRouteDate,
    this.cobrancaRouteIds,
    this.isKeepAlive = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'isSelling': isSelling,
      'isCobrando': isCobrando,
      'isSellingRouteId': isSellingRouteId,
      'cobrancaRouteIds': cobrancaRouteIds,
      'secondRouteId': secondRouteId,
      'secondRouteDate': secondRouteDate,
      'isKeepAlive': isKeepAlive,
    };
  }

  factory AppState.fromMap(Map<dynamic, dynamic> map) {
    return AppState(
      isSelling: map['isSelling'] ?? false,
      isCobrando: map['isCobrando'] ?? false,
      isSellingRouteId: map['isSellingRouteId'],
      cobrancaRouteIds: map['cobrancaRouteIds'],
      secondRouteId: map['secondRouteId'],
      secondRouteDate: map['secondRouteDate'],
      isKeepAlive: map['isKeepAlive'] ?? false,
    );
  }

  @override
  String toString() {
    return 'AppState(isSelling: $isSelling, isCobrando: $isCobrando, isSellingRouteId: $isSellingRouteId, cobrancaRouteIds: $cobrancaRouteIds, secondRouteId: $secondRouteId, secondRouteDate: $secondRouteDate, isKeepAlive: $isKeepAlive)';
  }
}
