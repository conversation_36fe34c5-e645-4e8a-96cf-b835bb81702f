import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';

abstract class AppStateRepository {
  AppState getAppState();

  Future<void> startSaleInRoute(String routeId);
  Future<void> finishSaleInRoute();

  Future<void> enableSecondRoute(String routeId, DateTime date);
  Future<void> disableSecondRoute();

  Future<void> startCobrancaInRoutes(List<String> routes);
  Future<void> finishCobrancaInRoute();
  List<String>? getCobrancaRouteIds();

  bool isKeepAlive();

  void setKeepAlive(bool value);
}
