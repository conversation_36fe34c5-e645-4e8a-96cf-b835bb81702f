import 'dart:io';

import 'package:firebase_storage/firebase_storage.dart';

import './image_repository.dart';

class ImageRepositoryImpl implements ImageRepository {
  FirebaseStorage firebaseStorage = FirebaseStorage.instance;

  @override
  Future<String> uploadImage(String path, String nameCloud) async {
    final ref = firebaseStorage.ref().child(nameCloud);
    final uploadTask = ref.putFile(File(path));
    final snapshot = await uploadTask.whenComplete(() => null);
    final url = await snapshot.ref.getDownloadURL();
    return url;
  }

  @override
  Future<void> deleteImage(String path) async {
    final ref = firebaseStorage.refFromURL(path);
    await ref.delete();
  }
}
