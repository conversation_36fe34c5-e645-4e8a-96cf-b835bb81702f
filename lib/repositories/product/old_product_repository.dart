// import 'dart:async';
// import 'dart:developer';

// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:diacritic/diacritic.dart';
// import 'package:fl_app/data/repositories/product_repository.dart';
// import 'package:fl_app/models/product_model.dart';
// import 'package:fl_app/models/user_model.dart';
// import 'package:fl_app/repositories/user/user_repository.dart';
// import 'package:fl_app/services/connection/connection_service.dart';
// import 'package:fl_app/services/user/user_service.dart';
// import 'package:get/get.dart';
// import 'package:hive_flutter/hive_flutter.dart';

// class ProductRepositoryImpl implements ProductRepository {
//   final UserRepository _userRepository;

//   ProductRepositoryImpl({required UserRepository userRepository})
//       : _userRepository = userRepository;

//   final firestoreInstance = FirebaseFirestore.instance;
//   final CollectionReference collection =
//       FirebaseFirestore.instance.collection('products');

//   List<ProductModel> _cachedProducts = <ProductModel>[];
//   List<ProductModel> get cachedProducts => List.from(_cachedProducts);
//   set cachedProducts(List<ProductModel> value) => _cachedProducts = value;

//   Box? box;
//   Box? boxLastUpdated;

//   List<ProductModel> toUpdateLastModified = [];
//   Box? toUpdateLastModifiedBox;
//   Timer? timerToUpdateLastModified;

//   UserRepository userRepositoryImpl = Get.find<UserRepository>();
//   UserModel? user;

//   @override
//   Future<void> initialize() async {
//     await init();
//   }

//   Future<void> init() async {
//     final userTemp = await _userRepository.getUserAuthenticated();
//     if (userTemp != null) {
//       user = userTemp;
//     }
//     if (box == null) {
//       box = await Hive.openBox('products');
//       await loadCache();
//       sortCache();
//     }
//     await getProducts();

//     if (toUpdateLastModifiedBox == null) {
//       toUpdateLastModifiedBox =
//           await Hive.openBox('toUpdateLastModified-Products');

//       await loadToUpdateLastModified();
//     }

//     timerToUpdateLastModified ??=
//         Timer.periodic(const Duration(seconds: 10), (timer) {
//       verifyProductsToUpdateLastModified();
//     });
//   }

//   @override
//   Future<ProductModel> addProduct(ProductModel product) async {
//     try {
//       user ??= await _userRepository.getUserAuthenticated();
//       final newProductRef = collection.doc();
//       product.id = newProductRef.id;

//       var users = await userRepositoryImpl.getUsersFromCache();
//       var usersId = users.map((e) => e.id).toList();
//       usersId.removeWhere((element) => element == user!.id);
//       product.toReceivedBy = [...usersId];

//       newProductRef.set(product.toDocument());
//       _cachedProducts.add(product);
//       bool hasConnection =
//           await Get.find<ConnectionService>().checkConnection();
//       if (!hasConnection) {
//         addToUpdateLastModified(product);
//       }
//       sortCache();
//       await saveThisToCache(product);
//       return product;
//     } catch (e) {
//       log('Erro ao adicionar produto: $e');
//       throw Exception('Erro ao adicionar produto $e');
//     }
//   }

//   @override
//   Future<ProductModel> deleteProduct(ProductModel product) async {
//     try {
//       user ??= await _userRepository.getUserAuthenticated();
//       product.isDeleted = true;
//       product.deletedById = user!.id;
//       product.deletedByName = user!.name;
//       product.lastModified = Timestamp.now();

//       var users = await userRepositoryImpl.getUsersFromCache();
//       var usersId = users.map((e) => e.id).toList();
//       usersId.removeWhere((element) => element == user!.id);
//       product.toReceivedBy = [...usersId];

//       return await updateProduct(product);
//     } catch (e) {
//       log('Erro ao excluir produto: $e');
//       throw Exception('Erro ao excluir produto $e');
//     }
//   }

//   Future<ProductModel> updateProductReceivedBy(ProductModel product) async {
//     user ??= await _userRepository.getUserAuthenticated();

//     product.toReceivedBy =
//         product.toReceivedBy.where((element) => element != user!.id).toList();

//     final index = cachedProducts
//         .indexWhere((cachedProduct) => cachedProduct.id == product.id);
//     if (index != -1) {
//       _cachedProducts[index] = product;
//       await saveThisToCache(product);
//     }
//     collection.doc(product.id).update({
//       'toReceivedBy': product.toReceivedBy,
//     });
//     return product;
//   }

//   @override
//   Future<ProductModel> getProduct(String id) async {
//     return cachedProducts.firstWhere((product) => product.id == id);
//   }

//   @override
//   Future<List<ProductModel>> getProducts() async {
//     try {
//       user ??= await _userRepository.getUserAuthenticated();
//       if (box == null) {
//         box = await Hive.openBox('products');
//         await loadCache();
//         sortCache();
//       }
//       if (cachedProducts.isNotEmpty) {
//         log('Buscando produtos em cache e verificando se estão atualizados');

//         // Fetch products where toReceivedBy contains current user's id
//         final querySnapshot = await collection
//             .where('toReceivedBy', arrayContains: user!.id)
//             .get();

//         if (querySnapshot.docs.isNotEmpty) {
//           // Update only the products that have changed
//           final updatedProducts = querySnapshot.docs.map((doc) {
//             var product =
//                 ProductModel.fromDocument(doc.data() as Map<String, dynamic>);
//             product.id = doc.id;
//             return product;
//           }).toList();
//           log('quantidade de produtos em cache: ${cachedProducts.length}');
//           log('quantidade de produtos atualizadas: ${updatedProducts.length}');
//           for (final updatedProduct in updatedProducts) {
//             final index = _cachedProducts.indexWhere(
//                 (cachedProduct) => cachedProduct.id == updatedProduct.id);
//             if (index == -1) {
//               log('Produto não encontrado em cache, adicionando');
//               _cachedProducts.add(updatedProduct);
//             } else {
//               log('Produto encontrado em cache, atualizando');
//               _cachedProducts[index] = updatedProduct;
//             }
//             await updateProductReceivedBy(updatedProduct);
//           }
//           sortCache();
//         }
//         await verifyProductsToUpdateLastModified();
//       } else {
//         log('Buscando produtos no Firestore');
//         await updateCache();
//       }
//       sortCache();

//       return cachedProducts;
//     } catch (e) {
//       log('Erro ao obter produtos: $e');
//       throw Exception('Erro ao obter produtos $e');
//     }
//   }

//   @override
//   Future<void> updateCache() async {
//     log('Atualizando cache de produtos (Firestore)');
//     final querySnapshot = await collection.orderBy('nome').get();
//     cachedProducts = querySnapshot.docs.map((doc) {
//       var product =
//           ProductModel.fromDocument(doc.data() as Map<String, dynamic>);
//       product.id = doc.id;
//       return product;
//     }).toList();
//     await saveCache();
//   }

//   @override
//   Future<ProductModel> updateProduct(ProductModel product) async {
//     try {
//       user ??= await Get.find<UserService>().getUserAuthenticated();
//       product.lastModified = Timestamp.now();

//       var users = await userRepositoryImpl.getUsersFromCache();
//       var usersId = users.map((e) => e.id).toList();
//       usersId.removeWhere((element) => element == user!.id);
//       product.toReceivedBy = [...usersId];

//       final index = cachedProducts
//           .indexWhere((cachedProduct) => cachedProduct.id == product.id);
//       if (index == -1) {
//         throw Exception('Produto não encontrado');
//       }
//       collection.doc(product.id).update(product.toDocument());
//       bool hasConnection =
//           await Get.find<ConnectionService>().checkConnection();
//       if (!hasConnection) {
//         addToUpdateLastModified(product);
//       }
//       final tempProducts = cachedProducts;
//       tempProducts[index] = product;
//       cachedProducts = tempProducts;
//       sortCache();
//       await saveThisToCache(product);
//       return product;
//     } catch (e) {
//       log('Erro ao atualizar produto: $e');
//       throw Exception('Erro ao atualizar produto $e');
//     }
//   }

//   void sortCache() {
//     _cachedProducts.sort((a, b) {
//       // Se ambos os produtos são favoritos ou não favoritos, classifique por nome
//       if ((a.isFavorite && b.isFavorite) || (!a.isFavorite && !b.isFavorite)) {
//         return removeDiacritics(a.nome)
//             .toLowerCase()
//             .compareTo(removeDiacritics(b.nome).toLowerCase());
//       }
//       // Se 'a' é favorito e 'b' não é, 'a' deve vir primeiro
//       if (a.isFavorite && !b.isFavorite) {
//         return -1;
//       }
//       // Se 'b' é favorito e 'a' não é, 'b' deve vir primeiro
//       if (!a.isFavorite && b.isFavorite) {
//         return 1;
//       }
//       return 0;
//     });
//   }

//   Future<void> saveCache() async {
//     box ??= await Hive.openBox('products');
//     log('Salvando produtos em cache (Hive)');
//     List<ProductModel> updatedProducts = [];
//     for (var product in cachedProducts) {
//       await box!.put(product.id, product.toMap());
//       updatedProducts.add(product);
//     }
//     log('Produtos salvos em cache (Hive): ${updatedProducts.length}');
//     //verify deleted products
//     final deletedProducts = box!.values
//         .where((element) =>
//             updatedProducts.indexWhere((p) => p.id == element['id']) == -1)
//         .toList();
//     for (final deletedProduct in deletedProducts) {
//       try {
//         DocumentSnapshot doc = await collection.doc(deletedProduct['id']).get();
//         if (!doc.exists) {
//           log('Excluindo produto do cache (Hive): ${deletedProduct['id']} - ${deletedProduct['nome']}');
//           await box!.delete(deletedProduct['id']);
//         }
//       } catch (e) {
//         log('Erro ao verificar se o produto foi excluído: $e');
//       }
//     }
//   }

//   @override
//   Future<void> loadCache() async {
//     box ??= await Hive.openBox('products');
//     log('Carregando produtos em cache (Hive)');
//     cachedProducts = box!.values.map((e) {
//       var product = ProductModel(
//         id: '',
//         nome: '',
//         imagem: null,
//         valor: 0,
//         temComissao: false,
//         lastModified: Timestamp.now(),
//       );
//       product.id = e['id'];
//       product.nome = e['nome'];
//       product.imagem = e['imagem'];
//       product.valor = e['valor'];
//       product.temComissao = e['temComissao'];
//       product.isDeleted = e['isDeleted'];
//       product.deletedById = e['deletedById'];
//       product.deletedByName = e['deletedByName'];
//       product.isFavorite = e['isFavorite'] ?? false;
//       product.lastModified =
//           Timestamp.fromDate(DateTime.parse(e['lastModified']));
//       product.toReceivedBy = List<String>.from(e['toReceivedBy'] ?? <String>[]);
//       return product;
//     }).toList();
//     log('Produtos carregados em cache (Hive): ${cachedProducts.length}');
//   }

//   @override
//   Future<Timestamp> getLastUpdated() async {
//     boxLastUpdated ??= await Hive.openBox('products_last_updated');
//     final lastUpdated = boxLastUpdated!.get('lastUpdated');
//     if (lastUpdated == null) {
//       final newLastUpdated = Timestamp.now();
//       await _setLastUpdated(newLastUpdated);
//       return newLastUpdated;
//     }
//     return Timestamp.fromDate(lastUpdated);
//   }

//   Future<void> _setLastUpdated(Timestamp timestamp) async {
//     boxLastUpdated ??= await Hive.openBox('products_last_updated');
//     await boxLastUpdated!.put('lastUpdated', timestamp.toDate());
//   }

//   @override
//   Future<List<ProductModel>> getProductsFromCache() async {
//     if (box == null) {
//       box = await Hive.openBox('products');
//       await loadCache();
//       sortCache();
//     }
//     sortCache();
//     getProducts();
//     return cachedProducts.where((product) => !product.isDeleted).toList();
//   }

//   @override
//   Future<List<ProductModel>> getDeletedProductsFromCache() async {
//     if (box == null) {
//       box = await Hive.openBox('products');
//       await loadCache();
//       sortCache();
//     }
//     sortCache();
//     getProducts();
//     return cachedProducts.where((product) => product.isDeleted).toList();
//   }

//   Future<void> addToUpdateLastModified(ProductModel product) async {
//     log('Adicionando pedido para atualizar lastModified');
//     toUpdateLastModifiedBox ??=
//         await Hive.openBox('toUpdateLastModified-Products');
//     toUpdateLastModifiedBox!.put(product.id, product.toMap());

//     toUpdateLastModified.add(product);
//   }

//   Future<void> verifyProductsToUpdateLastModified() async {
//     List<ProductModel> removeList = [];
//     for (final product in toUpdateLastModified) {
//       final hasConnection =
//           await Get.find<ConnectionService>().checkConnection();
//       if (hasConnection) {
//         log('Atualizando lastModified do produto: ${product.id}');
//         await updateProduct(product);
//         toUpdateLastModifiedBox!.delete(product.id);
//         removeList.add(product);
//       }
//     }
//     toUpdateLastModified.removeWhere((element) => removeList.contains(element));
//   }

//   loadToUpdateLastModified() {
//     toUpdateLastModifiedBox ??= Hive.box('toUpdateLastModified-Products');
//     toUpdateLastModified = toUpdateLastModifiedBox!.values.map((e) {
//       var product = ProductModel(
//         id: '',
//         nome: '',
//         imagem: null,
//         valor: 0,
//         temComissao: false,
//         lastModified: Timestamp.now(),
//       );
//       product.id = e['id'];
//       product.nome = e['nome'];
//       product.imagem = e['imagem'];
//       product.valor = e['valor'];
//       product.temComissao = e['temComissao'];
//       product.isDeleted = e['isDeleted'];
//       product.isFavorite = e['isFavorite'] ?? false;
//       product.lastModified =
//           Timestamp.fromDate(DateTime.parse(e['lastModified']));
//       return product;
//     }).toList();
//     log('Produtos para atualizar lastModified carregados: ${toUpdateLastModified.length}');
//   }

//   Future<void> saveThisToCache(ProductModel product) async {
//     box ??= await Hive.openBox('products');
//     await box!.put(product.id, product.toMap());
//     log('Produto salvo em cache (Hive): ${product.id} - ${product.nome}');
//   }

//   @override
//   Future<void> resetCache() async {
//     box ??= await Hive.openBox('products');
//     await box!.clear();
//     _cachedProducts = <ProductModel>[];
//     log('Cache de produtos resetado');
//   }

//   @override
//   Future<void> syncProduct(ProductModel productModel) async {
//     try {
//       bool productExists =
//           cachedProducts.indexWhere((p) => p.id == productModel.id) != -1;
//       var tempProducts = cachedProducts;
//       if (productExists) {
//         var product = cachedProducts.firstWhere((p) => p.id == productModel.id);
//         if (product.lastModified
//             .toDate()
//             .isBefore(productModel.lastModified.toDate())) {
//           var index = cachedProducts.indexWhere((p) => p.id == productModel.id);
//           tempProducts[index] = productModel;
//           cachedProducts = tempProducts;
//           await saveThisToCache(productModel);
//         }
//       } else {
//         tempProducts.add(productModel);
//         cachedProducts = tempProducts;
//         await saveThisToCache(productModel);
//       }
//     } catch (e) {
//       log('Erro ao sincronizar produto: $e');
//       throw Exception('Erro ao sincronizar produto $e');
//     }
//   }

//   @override
//   Future<ProductModel> restoreProduct(ProductModel product) async {
//     try {
//       product.isDeleted = false;
//       product.deletedById = null;
//       product.deletedByName = null;
//       product.lastModified = Timestamp.now();

//       var users = await userRepositoryImpl.getUsersFromCache();
//       var usersId = users.map((e) => e.id).toList();
//       usersId.removeWhere((element) => element == user!.id);
//       product.toReceivedBy = [...usersId];

//       return await updateProduct(product);
//     } catch (e) {
//       log('Erro ao restaurar produto: $e');
//       throw Exception('Erro ao restaurar produto $e');
//     }
//   }
// }
