import 'dart:async';
import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:diacritic/diacritic.dart';
import 'package:fl_app/models/client_model.dart';
import 'package:fl_app/models/user_model.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

List<ClientModel> processClientBatch(List<Map<String, dynamic>> docsData) {
  return docsData.map((data) {
    var client = ClientModel.fromDocument(data);
    return client;
  }).toList();
}

List<ClientModel> processCachedClients(List<Map<String, dynamic>> data) {
  return data.map((e) => ClientModel.fromCache(e)).toList();
}

List<ClientModel> processCachedClientsPartition(
    List<Map<String, dynamic>> data) {
  return data.map((e) => ClientModel.fromCache(e)).toList();
}

class ClientRepositoryImpl implements ClientRepository {
  static const int _batchSize = 500; // Tamanho do lote para paginação

  UserRepository userRepositoryImpl = Get.find<UserRepository>();

  final firestoreInstance = FirebaseFirestore.instance;
  final CollectionReference collection =
      FirebaseFirestore.instance.collection('clients');

  Map<String, ClientModel> _cachedClients = {};
  Map<String, ClientModel> get cachedClients => Map.from(_cachedClients);

  Box? box;

  UserModel? user;

  ClientRepositoryImpl();

  @override
  Future<void> initialize() async {
    await init();
  }

  Future<void> init() async {
    final userTemp = await Get.find<UserService>().getUserAuthenticated();
    if (userTemp != null) {
      user = userTemp;
    }
    if (box == null) {
      box = await Hive.openBox('clients');
      await loadCache();
    }

    getClients(null);
  }

  @override
  Future<ClientModel> create(ClientModel client) async {
    try {
      user ??= await Get.find<UserService>().getUserAuthenticated();
      final newClientRef = collection.doc();
      client.id = newClientRef.id;

      var users = await userRepositoryImpl.getUsersFromCache();
      var usersId = users.map((e) => e.id).toList();
      usersId.removeWhere((element) => element == user!.id);
      client.toReceivedBy = [...usersId];

      newClientRef.set(client.toDocument());
      _cachedClients[client.id!] = client;
      await saveThisToCache(client);
      return client;
    } catch (e) {
      log('Erro ao adicionar cliente: $e');
      throw Exception('Erro ao adicionar cliente $e');
    }
  }

  @override
  Future<void> delete(ClientModel client) async {
    try {
      client.isDeleted = true;
      client.lastModified = Timestamp.now();
      update(client);
    } catch (e) {
      log('Erro ao excluir cliente: $e');
      throw Exception('Erro ao excluir cliente $e');
    }
  }

  @override
  Future<ClientModel?> getById(String id) async {
    if (cachedClients.isEmpty) {
      await getClients(null);
    }
    return cachedClients[id];
  }

  @override
  Future<List<ClientModel>> getClients(String? rotaId) async {
    try {
      if (box == null) {
        box = await Hive.openBox('clients');
        await loadCache();
      }
      user ??= await Get.find<UserService>().getUserAuthenticated();
      if (cachedClients.isNotEmpty) {
        log('Buscando clientes em cache e verificando se tem algum toReceivedBy');
        // Se houver dados em cache, verifique se estão atualizados
        final querySnapshot = await collection
            .where('toReceivedBy', arrayContains: user!.id)
            .get();

        if (querySnapshot.docs.isNotEmpty) {
          // Se houver documentos mais recentes no Firestore, atualize apenas os documentos alterados
          final updatedClients = querySnapshot.docs.map((doc) {
            var client =
                ClientModel.fromDocument(doc.data() as Map<String, dynamic>);
            client.id = doc.id;
            return client;
          }).toList();
          log('quantidade de clientes em cache: ${cachedClients.length}');
          log('quantidade de clientes atualizados: ${updatedClients.length}');
          for (final updatedClient in updatedClients) {
            _cachedClients[updatedClient.id!] = updatedClient;
            await updateClientReceivedBy(updatedClient);
          }
        }
      } else {
        log('Buscando clientes no Firestore');
        await updateCache();
      }
      if (rotaId != null) {
        var clients = cachedClients.values
            .where((element) => element.routeId == rotaId && !element.isDeleted)
            .toList();
        clients.sort((a, b) => removeDiacritics(a.name.toLowerCase())
            .compareTo(removeDiacritics(b.name.toLowerCase())));
        return clients;
      }
      var clients =
          cachedClients.values.where((element) => !element.isDeleted).toList();
      clients.sort((a, b) => removeDiacritics(a.name.toLowerCase())
          .compareTo(removeDiacritics(b.name.toLowerCase())));
      return clients;
    } catch (e) {
      log('Erro ao obter clientes: $e');
      throw Exception('Erro ao obter clientes $e');
    }
  }

  @override
  Future<void> updateCache() async {
    log('Atualizando cache de clientes (Firestore)');
    box ??= await Hive.openBox('clients');

    List<ClientModel> allClients = [];
    Query query = collection.orderBy('name').limit(_batchSize);
    DocumentSnapshot? lastDocument;

    while (true) {
      Query currentQuery = query;
      if (lastDocument != null) {
        currentQuery = currentQuery.startAfterDocument(lastDocument);
      }

      QuerySnapshot querySnapshot;
      try {
        querySnapshot = await currentQuery.get();
      } catch (e) {
        log('Erro ao executar a consulta Firestore: $e');
        throw Exception('Erro ao executar a consulta Firestore: $e');
      }

      if (querySnapshot.docs.isEmpty) {
        break;
      }

      // Extrai os dados dos documentos
      List<Map<String, dynamic>> docsData = querySnapshot.docs.map((doc) {
        var data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();

      // Processa o lote em um isolate
      List<ClientModel> clients;
      try {
        clients = await compute(processClientBatch, docsData);
      } catch (e) {
        log('Erro ao processar lote no isolate: $e');
        throw Exception('Erro ao processar lote no isolate: $e');
      }

      // Adiciona os clientes processados à lista completa
      allClients.addAll(clients);

      // Atualiza o último documento para a próxima página
      lastDocument = querySnapshot.docs.last;

      // Verifica se o número de documentos retornados é menor que o tamanho do lote
      if (querySnapshot.docs.length < _batchSize) {
        break;
      }
    }

    // Atualiza o cache local
    _cachedClients =
        allClients.asMap().map((key, value) => MapEntry(value.id!, value));

    await box!.clear();
    await saveCache();

    log('Clientes atualizados em cache (Firestore): ${cachedClients.length}');
  }

  @override
  Future<ClientModel> update(ClientModel client) async {
    try {
      user ??= await Get.find<UserService>().getUserAuthenticated();
      client.lastModified = Timestamp.now();

      var users = await userRepositoryImpl.getUsersFromCache();
      var usersId = users.map((e) => e.id).toList();
      usersId.removeWhere((element) => element == user!.id);
      client.toReceivedBy = [...usersId];

      collection.doc(client.id).update(client.toDocument());
      _cachedClients[client.id!] = client;
      await saveThisToCache(client);
      return client;
    } catch (e) {
      log('Erro ao atualizar cliente: $e');
      throw Exception('Erro ao atualizar cliente $e');
    }
  }

  Future<void> saveCache() async {
    box ??= await Hive.openBox('clients');
    log('Salvando clientes em cache (Hive)');
    List<ClientModel> updatedClients = [];
    for (final client in _cachedClients.values) {
      await box!.put(client.id, client.toMap());
      updatedClients.add(client);
    }
    log('Clientes salvos em cache (Hive): ${updatedClients.length}');
    //verify deleted clients
    final deletedClients = box!.values
        .where((element) =>
            updatedClients.indexWhere((client) => client.id == element['id']) ==
            -1)
        .toList();
    for (final deletedClient in deletedClients) {
      try {
        DocumentSnapshot doc = await collection.doc(deletedClient['id']).get();
        if (!doc.exists) {
          log('Excluindo cliente do cache (Hive): ${deletedClient['id']} - ${deletedClient['name']}');
          await box!.delete(deletedClient['id']);
        }
      } catch (e) {
        log('Erro ao verificar se cliente foi excluído: $e');
      }
    }
  }

  @override
  Future<void> loadCache() async {
    log('Carregando clientes em cache (Hive)');
    box ??= await Hive.openBox('clients');

    List<Map<String, dynamic>> cachedData =
        box!.values.map((e) => Map<String, dynamic>.from(e as Map)).toList();

    if (cachedData.isEmpty) {
      log('Nenhum cliente em cache');
      return;
    }

    // Divide os dados em partes para paralelismo
    int numChunks =
        4; // Define o número de partes (pode ser ajustado conforme o tamanho)
    int chunkSize = (cachedData.length / numChunks).ceil();
    List<List<Map<String, dynamic>>> chunks = [];

    for (int i = 0; i < cachedData.length; i += chunkSize) {
      chunks.add(cachedData.sublist(
          i,
          i + chunkSize > cachedData.length
              ? cachedData.length
              : i + chunkSize));
    }

    // Processa as partes em paralelo
    List<Future<List<ClientModel>>> tasks = chunks.map((chunk) {
      return compute(processCachedClientsPartition, chunk);
    }).toList();

    // Aguarda os resultados e combina
    List<List<ClientModel>> results = await Future.wait(tasks);
    List<ClientModel> clients = results.expand((list) => list).toList();

    // Atualiza o cache na memória
    _cachedClients =
        clients.asMap().map((_, value) => MapEntry(value.id!, value));
    log('Clientes carregados em cache (Hive): ${_cachedClients.length}');
  }

  @override
  Future<List<ClientModel>> getClientsFromCache(String? rotaId) async {
    if (box == null) {
      box = await Hive.openBox('clients');
      await loadCache();
    }
    if (rotaId != null) {
      var clients = cachedClients.values
          .where((element) => element.routeId == rotaId && !element.isDeleted)
          .toList();
      clients.sort((a, b) => removeDiacritics(a.name.toLowerCase())
          .compareTo(removeDiacritics(b.name.toLowerCase())));
      return clients;
    }

    getClients(null);
    var clients =
        cachedClients.values.where((element) => !element.isDeleted).toList();
    clients.sort((a, b) => removeDiacritics(a.name.toLowerCase())
        .compareTo(removeDiacritics(b.name.toLowerCase())));
    return clients;
  }

  @override
  ClientModel? getClientFromCache(String id) {
    return _cachedClients[id];
  }

  Future<void> saveThisToCache(ClientModel client) async {
    box ??= await Hive.openBox('clients');
    await box!.put(client.id, client.toMap());
    log('Cliente salvo em cache (Hive): ${client.id} - ${client.name}');
  }

  @override
  Future<void> resetCache() async {
    box ??= await Hive.openBox('clients');
    await box!.clear();
    _cachedClients = {};
    log('Cache de clientes resetado');
  }

  Future<ClientModel> updateClientReceivedBy(ClientModel updatedClient) async {
    user ??= await Get.find<UserService>().getUserAuthenticated();

    updatedClient.toReceivedBy = updatedClient.toReceivedBy
        .where((element) => element != user!.id)
        .toList();

    _cachedClients[updatedClient.id!] = updatedClient;

    await saveThisToCache(updatedClient);
    collection.doc(updatedClient.id).update({
      'toReceivedBy': updatedClient.toReceivedBy,
    });
    return updatedClient;
  }

  @override
  Future<void> syncClient(ClientModel clientModel) async {
    try {
      bool clientExists =
          _cachedClients.keys.contains(clientModel.id) ? true : false;

      if (clientExists) {
        var client = _cachedClients[clientModel.id]!;
        if (client.lastModified
            .toDate()
            .isBefore(clientModel.lastModified.toDate())) {
          _cachedClients[clientModel.id!] = clientModel;
          await saveThisToCache(clientModel);
        }
      } else {
        _cachedClients[clientModel.id!] = clientModel;
        await saveThisToCache(clientModel);
      }
    } catch (e) {
      log('Erro ao sincronizar cliente: $e');
      throw Exception('Erro ao sincronizar cliente $e');
    }
  }
}
