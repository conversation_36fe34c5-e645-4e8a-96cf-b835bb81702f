import 'package:fl_app/models/client_model.dart';

abstract class ClientRepository {
  Future<List<ClientModel>> getClients(String? rotaId);
  Future<List<ClientModel>> getClientsFromCache(String? rotaId);
  Future<ClientModel?> getById(String id);
  Future<ClientModel> create(ClientModel client);
  Future<ClientModel> update(ClientModel client);
  Future<void> delete(ClientModel client);

  Future<void> updateCache();
  Future<void> resetCache();

  ClientModel? getClientFromCache(String id);

  Future<void> syncClient(ClientModel clientModel);

  Future<void> initialize();
  Future<void> loadCache();
}
