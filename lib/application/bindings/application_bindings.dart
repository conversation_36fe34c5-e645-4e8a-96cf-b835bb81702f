import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fl_app/application/auth/auth_service.dart';
import 'package:fl_app/application/helpers/sales_routes_helper.dart';
import 'package:fl_app/data/datasources/local/order/hive_order_data_source.dart';
import 'package:fl_app/data/datasources/local/product/hive_product_data_source.dart';
import 'package:fl_app/data/datasources/local/product/local_product_data_source.dart';
import 'package:fl_app/data/datasources/remote/order/firestore_order_data_source.dart';
import 'package:fl_app/data/datasources/remote/product/firestore_product_data_source.dart';
import 'package:fl_app/data/datasources/remote/image/firebase_image_data_source.dart';
import 'package:fl_app/data/datasources/remote/image/remote_image_data_source.dart';
import 'package:fl_app/data/datasources/remote/product/remote_product_data_source.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/data/repositories/order/order_repository_impl.dart';
import 'package:fl_app/repositories/cart/cart_repository.dart';
import 'package:fl_app/repositories/cart/cart_repository_impl.dart';
import 'package:fl_app/repositories/client/client_repository.dart';
import 'package:fl_app/repositories/client/client_repository_impl.dart';
import 'package:fl_app/repositories/geolocation/geolocation_repository.dart';
import 'package:fl_app/repositories/geolocation/geolocation_repository_impl.dart';
import 'package:fl_app/repositories/offline_sync/offline_sync_repository_impl.dart';
import 'package:fl_app/data/repositories/product/product_repository.dart';
import 'package:fl_app/data/repositories/product/product_repository_impl.dart';
import 'package:fl_app/repositories/app_state/app_state_repository.dart';
import 'package:fl_app/repositories/app_state/app_state_repository_impl.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';
import 'package:fl_app/repositories/sales_routes/sales_routes_repository_impl.dart';
import 'package:fl_app/repositories/settings/settings_repository.dart';
import 'package:fl_app/repositories/settings/settings_repository_impl.dart';
import 'package:fl_app/repositories/user/salary_advance/salary_advance_repository.dart';
import 'package:fl_app/repositories/user/salary_advance/salary_advance_repository_impl.dart';
import 'package:fl_app/repositories/user/user_repository.dart';
import 'package:fl_app/repositories/user/user_repository_impl.dart';
import 'package:fl_app/services/app_state/app_state_service.dart';
import 'package:fl_app/services/app_state/app_state_service_impl.dart';
import 'package:fl_app/services/bluetooth/bluetooth_service.dart';
import 'package:fl_app/services/bluetooth/bluetooth_service_impl.dart';
import 'package:fl_app/services/cart/cart_service.dart';
import 'package:fl_app/services/cart/cart_service_impl.dart';
import 'package:fl_app/services/client/client_service.dart';
import 'package:fl_app/services/client/client_service_impl.dart';
import 'package:fl_app/services/connection/connection_service.dart';
import 'package:fl_app/services/connection/connection_service_impl.dart';
import 'package:fl_app/services/geolocation/geolocation_service.dart';
import 'package:fl_app/services/geolocation/geolocation_service_impl.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service.dart';
import 'package:fl_app/services/sales_routes/sales_routes_service_impl.dart';
import 'package:fl_app/services/settings/settings_service.dart';
import 'package:fl_app/services/settings/settings_service_impl.dart';
import 'package:fl_app/services/user/salary_advance/salary_advance_service.dart';
import 'package:fl_app/services/user/salary_advance/salary_advance_service_impl.dart';
import 'package:fl_app/services/user/user_service.dart';
import 'package:fl_app/services/user/user_service_impl.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

class ApplicationBindings implements Bindings {
  @override
  void dependencies() {
    // User
    Get.put<UserRepository>(UserRepositoryImpl());
    Get.put<UserService>(UserServiceImpl(userRepository: Get.find()));

    // Internet
    Get.put<ConnectionService>(ConnectionServiceImpl());

    // SalesRoutes
    Get.put<SalesRoutesRepository>(SalesRoutesRepositoryImpl(),
        permanent: true);
    Get.put<SalesRoutesService>(
        SalesRoutesServiceImpl(salesRoutesRepository: Get.find()),
        permanent: true);

    SalesRoutesHelper.init(salesRoutesRepository: Get.find());

    // Sales
    Get.put<AppStateRepository>(AppStateRepositoryImpl(), permanent: true);
    Get.put<AppStateService>(
        AppStateServiceImpl(appStateRepository: Get.find()),
        permanent: true);

    // Geolocation
    Get.lazyPut<GeolocationRepository>(() => GeolocationRepositoryImpl(),
        fenix: true);
    Get.lazyPut<GeolocationService>(
        () => GeolocationServiceImpl(geolocationRepository: Get.find()),
        fenix: true);

    // Image
    Get.lazyPut<RemoteImageDataSource>(() => FirebaseImageDataSource(
          connectionService: Get.find(),
        ));
    // Cart
    Get.lazyPut<CartRepository>(() => CartRepositoryImpl(), fenix: true);
    Get.put<CartService>(CartServiceImpl(cartRepository: Get.find()));

    OfflineSyncRepositoryImpl offlineSyncRepositoryImpl =
        Get.put<OfflineSyncRepositoryImpl>(OfflineSyncRepositoryImpl());

    initProducts();
    initOrders();
    initClients();

    offlineSyncRepositoryImpl.init();

    // Settings
    Get.put<SettingsRepository>(SettingsRepositoryImpl(), permanent: true);
    Get.put<SettingsService>(SettingsServiceImpl(Get.find()), permanent: true);
    Get.find<SettingsService>().getDarkMode();

    // Auth
    Get.put(AuthService()).init();

    Get.lazyPut<SalaryAdvanceRepository>(() => SalaryAdvanceRepositoryImpl(),
        fenix: true);
    Get.lazyPut<SalaryAdvanceService>(
        () => SalaryAdvanceServiceImpl(salaryAdvanceRepository: Get.find()),
        fenix: true);

    // Bluetooth
    Get.put<BluetoothService>(BluetoothServiceImpl());
  }

  static void initProducts() async {
    Get.put<RemoteProductDataSource>(
        FirestoreProductDataSource(firestore: FirebaseFirestore.instance));

    var box = await Hive.openBox('products');
    Get.put<LocalProductDataSource>(HiveProductDataSource(
      box: box,
    ));

    // Product
    Get.put<ProductRepository>(
        ProductRepositoryImpl(
          userRepository: Get.find(),
          remoteDataSource: Get.find(),
          localDataSource: Get.find(),
          connectionService: Get.find(),
          imageDataSource: Get.find(),
          offlineSyncRepository: Get.find(),
        ),
        permanent: true);
  }

  static void initOrders() {
    // Order
    Get.put<OrderRepository>(
        OrderRepositoryImpl(
          localDataSource: HiveOrderDataSource(),
          remoteDataSource: FirestoreOrderDataSource(
            firestore: FirebaseFirestore.instance,
          ),
          salesRoutesRepository: Get.find(),
          userRepository: Get.find(),
          offlineSyncRepositoryImpl: Get.find(),
        ),
        permanent: true);
  }

  static void initClients() {
    // Client
    Get.put<ClientRepository>(ClientRepositoryImpl(), permanent: true);
    Get.put<ClientService>(
        ClientServiceImpl(
          clientRepository: Get.find(),
          geolocationService: Get.find(),
          orderRepository: Get.find(),
          offlineSyncRepository: Get.find(),
        ),
        permanent: true);
  }
}
