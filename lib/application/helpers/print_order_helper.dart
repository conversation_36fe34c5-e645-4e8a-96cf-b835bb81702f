import 'package:crclib/catalog.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:crclib/crclib.dart';

class PrintOrderHelper {
  static Future<List<int>> _getHeaderPrint() async {
    List<int> bytes = [];
    final CapabilityProfile profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm58, profile);

    // bytes += await getIconPrint();
    bytes += generator.text(
      "FL Mult Limpo".toUpperCase(),
      styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size2,
          width: PosTextSize.size2,
          bold: true),
      linesAfter: 0,
    );
    bytes += generator.text(
      "Francisco Produtos de Limpeza",
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size1,
      ),
      linesAfter: 0,
    );

    bytes += generator.text("CNPJ: 45.549.759/0001-66",
        styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size1,
        ),
        linesAfter: 1);

    bytes += generator.text("Praca Padre Nelson, 26".toUpperCase(),
        styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size1,
        ));
    bytes += generator.text('Centro, Aguas Belas-PE 55340-000'.toUpperCase(),
        styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size1,
        ));
    bytes += generator.text('Tel: 87 99616-9235',
        styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size1,
        ));

    bytes += generator.hr();
    return bytes;
  }

  static List<String> _generateClientInfo(OrderModel order) {
    var enderecoList =
        "${order.clientName} - ${order.clientAddress}, ${order.clientLocalDescription}"
            .split(' ');
    const limiteLetras = 32;
    List<String> linhas = [];

    for (var i = 0; i < enderecoList.length; i++) {
      if (linhas.isEmpty) {
        linhas.add(TextHelper.removeAcento(enderecoList[i]));
      } else {
        if (('${linhas.last} ${enderecoList[i]}').length <= limiteLetras) {
          linhas.last += ' ${enderecoList[i]}';
        } else {
          linhas.add(TextHelper.removeAcento(enderecoList[i]));
        }
      }
    }
    return linhas;
  }

  static Future<List<int>> _getItemsPrint(OrderModel order) async {
    List<int> bytes = [];
    CapabilityProfile profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm58, profile);
    int pos = 1;

    for (final item in order.products) {
      bytes += generator.row([
        PosColumn(
            text: "$pos",
            width: 2,
            styles: const PosStyles(
              align: PosAlign.left,
              height: PosTextSize.size1,
            )),
        PosColumn(
            text: TextHelper.removeAcento(item.name),
            width: 10,
            styles: const PosStyles(
              align: PosAlign.left,
              height: PosTextSize.size1,
            )),
      ]);
      bytes += generator.row([
        PosColumn(
            text:
                "${item.quantity} x ${item.customValue > 0 ? item.customValue : item.price}",
            width: 7,
            styles: const PosStyles(
              align: PosAlign.right,
              height: PosTextSize.size1,
            )),
        PosColumn(
            text: item.total.toStringAsFixed(2).replaceAll('.', ','),
            width: 5,
            styles: const PosStyles(
              align: PosAlign.right,
              height: PosTextSize.size1,
            )),
      ]);
      pos++;
    }

    bytes += generator.hr();
    return bytes;
  }

  static Future<List<int>> getPix({OrderModel? order, double? value}) async {
    List<int> bytes = [];
    final CapabilityProfile profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm58, profile);

    bytes += generator.text(
      "FL Mult Limpo".toUpperCase(),
      styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size2,
          width: PosTextSize.size2,
          bold: true),
      linesAfter: 0,
    );

    bytes += generator.text(
      "Francisco Produtos de Limpeza",
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size1,
      ),
      linesAfter: 0,
    );

    bytes += generator.hr();

    bytes += generator.text(
      "Chave PIX: (87) 99616-9235",
      styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size1,
          width: PosTextSize.size1,
          bold: true),
      linesAfter: 0,
    );

    bytes += generator.text(
      "Nome: Francisco Lopes da Silva",
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size1,
      ),
      linesAfter: 0,
    );

    bytes += generator.text(
      "Banco: Banco Bradesco S.A.",
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size1,
      ),
      linesAfter: 0,
    );

    // Parâmetros do Pix
    String telefone = '+5587996169235';
    String nome = 'Francisco Lopes da Silva';
    String cidade = 'Aguas Belas-PE';
    double valor = value ?? 0.0;
    String identificador = order?.id ?? '';

    // Gerando o payload Pix
    String pixPayload = generatePixPayload(
      pixKey: telefone,
      beneficiaryName: nome,
      beneficiaryCity: cidade,
      amount: valor.toStringAsFixed(2),
      transactionId: identificador,
    );

    bytes += generator.feed(1);

    bytes += generator.qrcode(pixPayload, size: QRSize.size4);

    bytes += generator.feed(1);

    if (valor > 0.0) {
      bytes += generator.text(
        "Valor: ${NumberFormatHelper.format(valor)}",
        styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size1,
        ),
        linesAfter: 0,
      );
    }

    // bytes += generator.text(
    //   "Identificador: $identificador",
    //   styles: const PosStyles(
    //     align: PosAlign.center,
    //     height: PosTextSize.size1,
    //   ),
    //   linesAfter: 0,
    // );

    bytes += generator.feed(1);

    bytes += generator.text(
      "Apos o pagamento, envie \no comprovante para o \nWhatsApp (87) 99616-9235",
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size1,
      ),
      linesAfter: 0,
    );

    bytes += generator.feed(3);

    return bytes;
  }

  /// Função para gerar o payload Pix
  /// [pixKey] é a chave Pix do recebedor
  /// [beneficiaryName] é o nome do recebedor
  /// [beneficiaryCity] é a cidade do recebedor
  /// [amount] é o valor da transação
  /// [transactionId] é o identificador da transação
  /// Retorno é o payload Pix completo
  /// Exemplo de uso:
  /// ```dart
  /// String payload = generatePixPayload(
  ///  pixKey: 'telefone',
  /// beneficiaryName: 'Francisco Lopes da Silva',
  /// beneficiaryCity: 'Aguas Belas-PE',
  /// amount: '12.00',
  /// transactionId: 'PAGAMENTO123',
  /// );
  /// ```
  static String generatePixPayload({
    required String pixKey,
    required String beneficiaryName,
    required String beneficiaryCity,
    String? amount,
    String? transactionId,
  }) {
    // Função auxiliar para formatar os campos
    String formatValue(int id, String value) {
      String idStr = id.toString().padLeft(2, '0');
      String lengthStr = value.length.toString().padLeft(2, '0');
      return '$idStr$lengthStr$value';
    }

    // Campos do Merchant Account Information (GUI, chave, info adicional)
    String gui = formatValue(00, 'br.gov.bcb.pix');
    String key = formatValue(01, pixKey);
    String additionalDataField =
        transactionId != null ? formatValue(02, transactionId) : '';
    String merchantAccountInformation = formatValue(
      26,
      gui + key + additionalDataField,
    );

    // Campos do Additional Data Field Template (ID 62)
    String txid = transactionId != null ? formatValue(05, transactionId) : '';
    String additionalDataFieldTemplate = formatValue(62, txid);

    // Montagem do payload
    String payload =
        '${formatValue(00, '01')}$merchantAccountInformation${formatValue(52, '0000')}${formatValue(53, '986')}${amount != null ? formatValue(54, amount) : ''}${formatValue(58, 'BR')}${formatValue(59, beneficiaryName)}${formatValue(60, beneficiaryCity)}${additionalDataFieldTemplate}6304'; // CRC16

    // Calcular o CRC16 do payload
    String crc = _calculateCRC16(payload);

    // Retornar o payload completo com o CRC16
    return payload + crc;
  }

  /// Função para calcular o CRC16 usando o pacote crc
  static String _calculateCRC16(String str) {
    // Converter a string em bytes
    List<int> bytes = str.codeUnits;

    // Criar a instância do CRC16-CCITT-FALSE
    CrcValue crc = Crc16CcittFalse().convert(bytes);

    // Formatar o resultado em hexadecimal com 4 dígitos
    String crcStr = crc.toRadixString(16).toUpperCase().padLeft(4, '0');

    return crcStr;
  }

  static Future<List<int>> getOrderBytesToPrint(OrderModel order) async {
    List<int> bytes = [];
    CapabilityProfile profile = await CapabilityProfile.load();
    final generator = Generator(PaperSize.mm58, profile);

    bytes += await _getHeaderPrint();

    bytes += generator.row([
      PosColumn(
        text: 'Vendedor:',
        width: 4,
        styles: const PosStyles(
          align: PosAlign.left,
          height: PosTextSize.size1,
        ),
      ),
      PosColumn(
        text: order.sellerName,
        width: 8,
        styles: const PosStyles(
          align: PosAlign.right,
          height: PosTextSize.size1,
        ),
      ),
    ]);

    bytes += generator.hr();

    var linhas = _generateClientInfo(order);

    for (var i = 0; i < linhas.length; i++) {
      bytes += generator.text(
        TextHelper.removeAcento(linhas[i]),
        styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size1,
        ),
      );
    }

    bytes += generator.hr();

    bytes += await _getItemsPrint(order);

    if (order.remaining > 0) {
      bytes += generator.row([
        PosColumn(
          text: 'Restante',
          width: 6,
          styles: const PosStyles(
            align: PosAlign.left,
            height: PosTextSize.size1,
          ),
        ),
        PosColumn(
          text: order.remaining.toStringAsFixed(2).replaceAll('.', ','),
          width: 6,
          styles: const PosStyles(
            align: PosAlign.right,
            height: PosTextSize.size1,
          ),
        ),
      ]);
    }

    bytes += generator.row([
      PosColumn(
        text: 'Total',
        width: 6,
        styles: const PosStyles(
          align: PosAlign.left,
          height: PosTextSize.size1,
        ),
      ),
      PosColumn(
        text: order.calculateTotal().toStringAsFixed(2).replaceAll('.', ','),
        width: 6,
        styles: const PosStyles(
          align: PosAlign.right,
          height: PosTextSize.size1,
        ),
      ),
    ]);
    if (order.payments.isNotEmpty) {
      bytes += generator.row([
        PosColumn(
          text: '',
          width: 6,
          styles: const PosStyles(
            align: PosAlign.left,
            height: PosTextSize.size1,
          ),
        ),
        PosColumn(
          text:
              '- ${order.payments.fold(0.0, (p, c) => p + c.value).toStringAsFixed(2).replaceAll('.', ',')}',
          width: 6,
          styles: const PosStyles(
            align: PosAlign.right,
            height: PosTextSize.size1,
          ),
        ),
      ]);
      bytes += generator.row([
        PosColumn(
          text: 'A Pagar',
          width: 6,
          styles: const PosStyles(
            align: PosAlign.left,
            height: PosTextSize.size1,
          ),
        ),
        PosColumn(
          text: (order.calculateTotal() -
                  order.payments.fold(0.0, (p, c) => p + c.value))
              .toStringAsFixed(2)
              .replaceAll('.', ','),
          width: 6,
          styles: const PosStyles(
            align: PosAlign.right,
            height: PosTextSize.size1,
          ),
        ),
      ]);
    }

    bytes += generator.hr(ch: '=', linesAfter: 1);
    bytes += generator.text(
      'Obrigado pela preferencia!',
      styles: const PosStyles(
        align: PosAlign.center,
        bold: true,
        height: PosTextSize.size1,
      ),
    );
    bytes += generator.text(
      "${DateTimeHelper.getFormattedDate(order.date)} ${DateTimeHelper.getFormattedTime(order.date)}",
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size1,
      ),
    );
    bytes += generator.text(
      order.id!,
      styles: const PosStyles(
        align: PosAlign.center,
        height: PosTextSize.size1,
      ),
    );

    bytes += generator.text('\n');
    return bytes;
  }

  static String getOrderTextToShare(OrderModel value) {
    String text = '';
    text += 'FL Mult Limpo - Produtos de Limpeza\n';
    text += '------------------------------------------------\n';
    text += 'Pedido ${DateTimeHelper.getFormattedDate(value.date)}\n';
    text += '------------------------------------------------\n\n';

    text += 'Cliente: ${value.clientName}\n\n';
    text +=
        'Endereço: ${value.clientAddress} ${value.clientLocalDescription}\n\n';
    text += 'Vendedor: ${value.sellerName}\n\n';

    text += '------------------------------------------------\n\n';
    text += 'Produtos:\n\n';
    for (var item in value.products) {
      text +=
          '${item.quantity} x ${item.name} = R\$ ${item.total.toStringAsFixed(2)}\n';
    }
    text += '\n';
    if (value.remaining > 0) {
      text += 'Restante: R\$ ${value.remaining.toStringAsFixed(2)}\n';
    }
    text += '------------------------------------------------\n';
    text += 'Total: R\$ ${value.calculateTotal().toStringAsFixed(2)}\n';
    return text;
  }
}
