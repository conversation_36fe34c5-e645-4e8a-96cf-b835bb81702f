import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

class DateTimeHelper {
  static final timeFormat = DateFormat('HH:mm');
  static String getFormattedDate(DateTime dateTime) {
    return DateFormat('dd/MM/yyyy').format(dateTime);
  }

  static DateTime getDateTimeFromStringWithDate(String date) {
    return DateFormat('dd/MM/yyyy').parse(date);
  }

  static DateTime getDateTimeFromStringWithTime(String time) {
    return DateFormat('HH:mm').parse(time);
  }

  static String getFormattedTime(DateTime dateTime) {
    return DateFormat('HH:mm').format(dateTime);
  }

  static bool isSameDateTimestamp(DateTime date, Timestamp timestamp) {
    final timestampDate = timestamp.toDate();
    return date.year == timestampDate.year &&
        date.month == timestampDate.month &&
        date.day == timestampDate.day &&
        date.hour == timestampDate.hour &&
        date.minute == timestampDate.minute;
  }

  static bool isSameDateDateTime(DateTime date, DateTime dateTime) {
    return date.year == dateTime.year &&
        date.month == dateTime.month &&
        date.day == dateTime.day &&
        date.hour == dateTime.hour &&
        date.minute == dateTime.minute;
  }

  static bool isSameDate(DateTime date, DateTime value) {
    return date.year == value.year &&
        date.month == value.month &&
        date.day == value.day;
  }

  static bool isSameDay(DateTime date, DateTime value) {
    return date.year == value.year &&
        date.month == value.month &&
        date.day == value.day;
  }

  static bool isBeforeWith0Time(DateTime date, DateTime value) {
    return date.year <= value.year &&
        date.month <= value.month &&
        date.day < value.day;
  }
}
