import 'package:fl_app/repositories/sales_routes/sales_routes_repository.dart';

class SalesRoutesHelper {
  static late SalesRoutesRepository _salesRoutesRepository;

  static void init({required SalesRoutesRepository salesRoutesRepository}) {
    _salesRoutesRepository = salesRoutesRepository;
  }

  static String? getSaleRouteName(String routeId) {
    final routeName = _salesRoutesRepository.getSaleRouteName(routeId);
    return routeName;
  }
}
