import 'dart:io';

import 'package:fl_app/application/helpers/text_helper.dart';
import 'package:fl_app/data/repositories/order/order_repository.dart';
import 'package:fl_app/modules/reports/reports_by_saler/reports_by_saler_controller.dart';
import 'package:fl_app/modules/reports/reports_geral/reports_geral_controller.dart';
import 'package:flutter/services.dart';

import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class GeneratePdfReport {
  static Future<File> generatePdfReport({
    required DateTime initialDate,
    required DateTime finalDate,
    required OrderRepository orderRepository,
    required ReportsGeralData reportsGeralData,
    required List<ReportsBySalerData> reportsBySalerData,
    //required ExportDelegate exportDelegate,
  }) async {
    //ReportsGeralData
    // final List<OrderModel> orders;
    // final int pv;
    // final int quantityVendas;
    // final double valorVendido;
    // final double valorRestante;
    // final double valorTotal;
    // final List<Map<String, dynamic>> productsSold;
    // final List<MapEntry<SaleRouteModel, Map<String, dynamic>>> salesByRoutes;

    // salesByRoutes =>
    // MapEntry(route, {
    //   'orders': ordersRoute,
    //   'quantityVendas': ordersRoute.length,
    //   'productsSold': productsSoldInRoute,
    //   'valorTotal': valorTotalNaRota,
    //   'valorRestante': valorRestanteNaRota,
    //   'valorVendido': valorVendidoNaRota,
    //   'productsSoldPrazo': productsSoldPrazo,
    //   'productsSoldAvista': productsSoldAvista,
    //   'sallerSales': sallerSales,
    // }));

    final pdf = pw.Document();
    var image = (await rootBundle.load('assets/images/logo_azul.png'))
        .buffer
        .asUint8List();
    var header = await createHeader(initialDate, finalDate, image);
    var productsSold = reportsGeralData.productsSold;
    var productsSoldComission = productsSold
        .where((element) => element['temComissao'] == true)
        .toList();
    var productsSoldNoComission = productsSold
        .where((element) => element['temComissao'] == false)
        .toList();
    productsSoldComission.sort((a, b) =>
        TextHelper.removeAcento(a['productName'].toString().toLowerCase())
            .compareTo(TextHelper.removeAcento(
                b['productName'].toString().toLowerCase())));
    productsSoldNoComission.sort((a, b) =>
        TextHelper.removeAcento(a['productName'].toString().toLowerCase())
            .compareTo(TextHelper.removeAcento(
                b['productName'].toString().toLowerCase())));

    List<pw.Widget> produtosVendidosGeralTabela = [
      pw.TableHelper.fromTextArray(
        headers: ['Produto', 'PV', 'AV'],
        data: [
          ...productsSoldComission
              .map((e) => [
                    e['productName'],
                    e['quantity_prazo'],
                    e['quantity_avista'],
                  ])
              .toList(),
        ],
        headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        headerDecoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        cellAlignments: {
          0: pw.Alignment.centerLeft,
          1: pw.Alignment.center,
          2: pw.Alignment.center,
        },
        cellPadding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4),
        columnWidths: {
          0: const pw.FlexColumnWidth(4),
          1: const pw.FlexColumnWidth(1),
          2: const pw.FlexColumnWidth(1),
        },
        cellStyle: pw.TextStyle(
          color: PdfColors.blue800,
          fontWeight: pw.FontWeight.bold,
        ),
      ),
      pw.TableHelper.fromTextArray(
        data: [
          ...productsSoldNoComission
              .map((e) => [
                    e['productName'],
                    e['quantity_prazo'],
                    e['quantity_avista'],
                  ])
              .toList(),
        ],
        cellAlignments: {
          0: pw.Alignment.centerLeft,
          1: pw.Alignment.center,
          2: pw.Alignment.center,
        },
        cellPadding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4),
        columnWidths: {
          0: const pw.FlexColumnWidth(4),
          1: const pw.FlexColumnWidth(1),
          2: const pw.FlexColumnWidth(1),
        },
      ),
    ];

    List<pw.Widget> vendasPorRotaResumos = [];
    List<pw.Widget> vendasPorRotaTabelas = [];
    for (var saleRoute in reportsGeralData.salesByRoutes) {
      var productsSoldRoute = saleRoute.value['productsSold'];
      var productsSoldComissionRoute = productsSoldRoute
          .where((element) => element['temComissao'] == true)
          .toList();
      var productsSoldNoComissionRoute = productsSoldRoute
          .where((element) => element['temComissao'] == false)
          .toList();
      productsSoldComissionRoute.sort((a, b) =>
          TextHelper.removeAcento(a['productName'].toString().toLowerCase())
              .compareTo(TextHelper.removeAcento(
                  b['productName'].toString().toLowerCase())));
      productsSoldNoComissionRoute.sort((a, b) =>
          TextHelper.removeAcento(a['productName'].toString().toLowerCase())
              .compareTo(TextHelper.removeAcento(
                  b['productName'].toString().toLowerCase())));
      List<pw.Widget> rotasWidgetsResumos = [];
      List<pw.Widget> rotasWidgetsTabelas = [];
      rotasWidgetsResumos.add(pw.SizedBox(height: 8));
      rotasWidgetsResumos.add(
        pw.Text(
          saleRoute.key.name,
          style: pw.TextStyle(
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.indigo,
          ),
        ),
      );
      rotasWidgetsResumos.add(pw.SizedBox(height: 8));
      // rotasWidgets.add(pw.Text(
      //     'Quantidade de Vendas: ${saleRoute.value['quantityVendas']}'));
      // rotasWidgets.add(pw.SizedBox(height: 8));
      // rotasWidgets.add(pw.Text(
      //     'Valor Vendido: R\$ ${saleRoute.value['valorVendido'].toStringAsFixed(2)}'));
      // rotasWidgets.add(pw.Text(
      //     'Valor Restante: R\$ ${saleRoute.value['valorRestante'].toStringAsFixed(2)}'));
      // rotasWidgets.add(pw.Container(
      //   height: 1,
      //   width: 140,
      //   color: PdfColors.black,
      // ));
      // rotasWidgets.add(pw.Text(
      //     'Valor Total: R\$ ${saleRoute.value['valorTotal'].toStringAsFixed(2)}'));
      // rotasWidgets.add(pw.SizedBox(height: 8));
      // rotasWidgets.add(pw.Text('P.V: ${saleRoute.value['productsSoldPrazo']}'));
      // rotasWidgets.add(pw.Text('AV: ${saleRoute.value['productsSoldAvista']}'));
      // rotasWidgets.add(pw.SizedBox(height: 8));

      rotasWidgetsResumos.add(pw.Center(
          child: pw.TableHelper.fromTextArray(
        headers: [],
        data: [
          ['Quantidade de pedidos', saleRoute.value['quantityVendas']],
          [
            'Valor Vendido',
            'R\$ ${saleRoute.value['valorVendido'].toStringAsFixed(2)}'
          ],
          [
            'Valor Restante',
            'R\$ ${saleRoute.value['valorRestante'].toStringAsFixed(2)}'
          ],
          [
            'Valor Total',
            'R\$ ${saleRoute.value['valorTotal'].toStringAsFixed(2)}'
          ],
          ['P.V', saleRoute.value['productsSoldPrazo']],
          ['A.V', saleRoute.value['productsSoldAvista']],
        ],
        tableWidth: pw.TableWidth.min,
      )));
      rotasWidgetsResumos.add(pw.SizedBox(height: 8));

      // vendas por vendedor
      rotasWidgetsResumos.add(pw.Text('P.V por vendedor',
          style: pw.TextStyle(fontWeight: pw.FontWeight.bold)));
      var sallerSales = saleRoute.value['sallerSales'];
      for (var sallerSale in sallerSales.entries) {
        rotasWidgetsResumos
            .add(pw.Text('${sallerSale.key}: ${sallerSale.value}'));
      }
      rotasWidgetsResumos.add(pw.SizedBox(height: 8));

      rotasWidgetsTabelas.add(pw.SizedBox(height: 16));
      rotasWidgetsTabelas.add(pw.Container(
        height: 1,
        width: double.infinity,
        color: PdfColors.indigo,
      ));
      rotasWidgetsTabelas.add(pw.SizedBox(height: 8));
      rotasWidgetsTabelas.add(
        pw.Text(
          'Produtos Vendidos - ${saleRoute.key.name}',
          style: pw.TextStyle(
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.indigo,
          ),
        ),
      );
      rotasWidgetsTabelas.add(pw.SizedBox(height: 8));
      rotasWidgetsTabelas.add(pw.TableHelper.fromTextArray(
        headers: ['Produto', 'PV', 'AV'],
        data: [
          ...productsSoldComissionRoute
              .map((e) => [
                    e['productName'],
                    e['quantity_prazo'],
                    e['quantity_avista'],
                  ])
              .toList(),
        ],
        headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
        headerDecoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        cellAlignments: {
          0: pw.Alignment.centerLeft,
          1: pw.Alignment.center,
          2: pw.Alignment.center,
        },
        cellPadding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4),
        columnWidths: {
          0: const pw.FlexColumnWidth(4),
          1: const pw.FlexColumnWidth(1),
          2: const pw.FlexColumnWidth(1),
        },
        cellStyle: pw.TextStyle(
          color: PdfColors.blue800,
          fontWeight: pw.FontWeight.bold,
        ),
      ));
      rotasWidgetsTabelas.add(pw.TableHelper.fromTextArray(
        data: [
          ...productsSoldNoComissionRoute
              .map((e) => [
                    e['productName'],
                    e['quantity_prazo'],
                    e['quantity_avista'],
                  ])
              .toList(),
        ],
        cellAlignments: {
          0: pw.Alignment.centerLeft,
          1: pw.Alignment.center,
          2: pw.Alignment.center,
        },
        cellPadding: const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4),
        columnWidths: {
          0: const pw.FlexColumnWidth(4),
          1: const pw.FlexColumnWidth(1),
          2: const pw.FlexColumnWidth(1),
        },
      ));
      rotasWidgetsTabelas.add(pw.SizedBox(height: 8));

      vendasPorRotaResumos.add(
        //line between rotas
        pw.Column(children: [
          pw.Container(
            height: 1,
            width: 240,
            color: PdfColors.indigo,
          ),
          ...rotasWidgetsResumos,
        ]),
      );
      vendasPorRotaTabelas.addAll([
        //line between rotas
        pw.Container(
          height: 1,
          width: double.infinity,
          color: PdfColors.black,
        ),
        ...rotasWidgetsTabelas,
      ]);
    }

    List<pw.Widget> vendasPorVendedorResumos = [];

    for (var salerData in reportsBySalerData) {
      var productsSoldSaler = salerData.productsSold;
      var productsSoldComission = productsSoldSaler
          .where((element) => element['temComissao'] == true)
          .toList();
      var productsSoldNoComission = productsSoldSaler
          .where((element) => element['temComissao'] == false)
          .toList();
      productsSoldComission.sort((a, b) =>
          TextHelper.removeAcento(a['productName'].toString().toLowerCase())
              .compareTo(TextHelper.removeAcento(
                  b['productName'].toString().toLowerCase())));
      productsSoldNoComission.sort((a, b) =>
          TextHelper.removeAcento(a['productName'].toString().toLowerCase())
              .compareTo(TextHelper.removeAcento(
                  b['productName'].toString().toLowerCase())));
      vendasPorVendedorResumos.add(
        pw.Column(
          children: [
            pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Container(
                    height: 1,
                    width: 240,
                    color: PdfColors.indigo,
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text(
                    'Vendas por vendedor - ${salerData.saler.name}',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.indigo,
                    ),
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 16),
            pw.Text('Produtos Vendidos: ${salerData.pv}'),
            pw.Text('Pedidos: ${salerData.quantityVendas}'),
            pw.SizedBox(height: 16),
            // pw.SizedBox(height: 20),
            // pw.TableHelper.fromTextArray(
            //   headers: ['Produto', 'PV', 'AV'],
            //   data: [
            //     ...productsSoldComission
            //         .map((e) => [
            //               e['productName'],
            //               e['quantity_prazo'],
            //               e['quantity_avista'],
            //             ])
            //         .toList(),
            //   ],
            //   headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
            //   headerDecoration: const pw.BoxDecoration(
            //     color: PdfColors.grey300,
            //   ),
            //   cellAlignments: {
            //     0: pw.Alignment.centerLeft,
            //     1: pw.Alignment.center,
            //     2: pw.Alignment.center,
            //   },
            //   cellPadding:
            //       const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4),
            //   columnWidths: {
            //     0: const pw.FlexColumnWidth(4),
            //     1: const pw.FlexColumnWidth(1),
            //     2: const pw.FlexColumnWidth(1),
            //   },
            //   cellStyle: pw.TextStyle(
            //     color: PdfColors.blue800,
            //     fontWeight: pw.FontWeight.bold,
            //   ),
            // ),
            // pw.TableHelper.fromTextArray(
            //   data: [
            //     ...productsSoldNoComission
            //         .map((e) => [
            //               e['productName'],
            //               e['quantity_prazo'],
            //               e['quantity_avista'],
            //             ])
            //         .toList(),
            //   ],
            //   cellAlignments: {
            //     0: pw.Alignment.centerLeft,
            //     1: pw.Alignment.center,
            //     2: pw.Alignment.center,
            //   },
            //   cellPadding:
            //       const pw.EdgeInsets.symmetric(vertical: 2, horizontal: 4),
            //   columnWidths: {
            //     0: const pw.FlexColumnWidth(4),
            //     1: const pw.FlexColumnWidth(1),
            //     2: const pw.FlexColumnWidth(1),
            //   },
            // ),
          ],
        ),
      );
    }

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) => [
          header,
          pw.Center(
            child: pw.Container(
              child: pw.Column(
                children: [
                  pw.Container(
                    height: 1,
                    width: 240,
                    color: PdfColors.indigo,
                  ),
                  pw.SizedBox(height: 8),
                  pw.Text('Geral',
                      style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          color: PdfColors.indigo)),
                  pw.SizedBox(height: 8),
                  pw.TableHelper.fromTextArray(
                    headers: [],
                    data: [
                      [
                        'Quantidade de pedidos',
                        reportsGeralData.quantityVendas
                      ],
                      [
                        'Valor Vendido',
                        'R\$ ${reportsGeralData.valorVendido.toStringAsFixed(2)}'
                      ],
                      [
                        'Valor Restante',
                        'R\$ ${reportsGeralData.valorRestante.toStringAsFixed(2)}'
                      ],
                      [
                        'Valor Total',
                        'R\$ ${reportsGeralData.valorTotal.toStringAsFixed(2)}'
                      ],
                      ['P.V', reportsGeralData.pv],
                    ],
                    tableWidth: pw.TableWidth.min,
                  ),
                  pw.SizedBox(height: 8),
                ],
              ),
              color: PdfColors.grey50,
            ),
          ),
          pw.SizedBox(height: 16),
          for (var i = 0; i < vendasPorRotaResumos.length; i += 2)
            pw.Column(children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Container(
                    width: 240,
                    color: PdfColors.grey50,
                    child: vendasPorRotaResumos[i],
                  ),
                  if (i + 1 < vendasPorRotaResumos.length)
                    pw.Container(
                      width: 240,
                      color: PdfColors.grey50,
                      child: vendasPorRotaResumos[i + 1],
                    ),
                ],
              ),
              pw.SizedBox(height: 8),
            ]),
          pw.SizedBox(height: 16),
          pw.Center(
            child: pw.Column(
              children: [
                pw.Container(
                  height: 1,
                  width: double.infinity,
                  color: PdfColors.indigo,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'Vendas por Vendedor',
                  style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold, color: PdfColors.indigo),
                ),
                pw.SizedBox(height: 8),
              ],
            ),
          ),
          for (var i = 0; i < vendasPorVendedorResumos.length; i += 2)
            pw.Column(
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Container(
                      width: 240,
                      color: PdfColors.grey50,
                      child: vendasPorVendedorResumos[i],
                    ),
                    if (i + 1 < vendasPorVendedorResumos.length)
                      pw.Container(
                        width: 240,
                        color: PdfColors.grey50,
                        child: vendasPorVendedorResumos[i + 1],
                      ),
                  ],
                ),
                pw.SizedBox(height: 8),
              ],
            ),
          pw.SizedBox(height: 16),
          pw.Center(
            child: pw.Column(
              children: [
                pw.Container(
                  height: 1,
                  width: double.infinity,
                  color: PdfColors.indigo,
                ),
                pw.SizedBox(height: 8),
                pw.Text(
                  'Produtos Vendidos - Geral',
                  style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold, color: PdfColors.indigo),
                ),
              ],
            ),
          ),
          pw.SizedBox(height: 16),
          ...produtosVendidosGeralTabela,
          ...vendasPorRotaTabelas,
        ],
      ),
    );

    // final widget = await exportDelegate.exportToPdfWidget('test');

    // pdf.addPage(
    //   pw.Page(
    //     build: (pw.Context context) {
    //       return pw.Center(
    //         child: pw.Container(
    //           child: pw.Column(
    //             children: [
    //               pw.Text('Hello World'),
    //               widget,
    //             ],
    //           ),
    //         ),
    //       );
    //     },
    //   ),
    // );

    final output = await getTemporaryDirectory();
    final file = File('${output.path}/example.pdf');
    await file.writeAsBytes(await pdf.save());
    return file;
  }

  static Future<pw.Header> createHeader(
      DateTime initialDate, DateTime finalDate, Uint8List image) async {
    return pw.Header(
      level: 0,
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [
          pw.Column(
            children: [
              pw.Image(
                pw.MemoryImage(image),
                width: 150,
                height: 80,
              ),
              pw.Text(
                'Relatório de Vendas - ${getMonth(finalDate.month)}/${initialDate.year} - ${initialDate.day.toString().padLeft(2, '0')}/${initialDate.month.toString().padLeft(2, '0')} a ${finalDate.day.toString().padLeft(2, '0')}/${finalDate.month.toString().padLeft(2, '0')}',
                style: const pw.TextStyle(
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static getMonth(int month) {
    switch (month) {
      case 1:
        return 'Janeiro';
      case 2:
        return 'Fevereiro';
      case 3:
        return 'Março';
      case 4:
        return 'Abril';
      case 5:
        return 'Maio';
      case 6:
        return 'Junho';
      case 7:
        return 'Julho';
      case 8:
        return 'Agosto';
      case 9:
        return 'Setembro';
      case 10:
        return 'Outubro';
      case 11:
        return 'Novembro';
      case 12:
        return 'Dezembro';
      default:
        return '';
    }
  }

  static PdfPageTemplateElement createFooterTemplate() {
    final PdfPageTemplateElement footerTemplate =
        PdfPageTemplateElement(const Rect.fromLTWH(0, 0, 515, 50));
    footerTemplate.graphics.drawString(
        'This is page footer', PdfStandardFont(PdfFontFamily.helvetica, 12),
        bounds: const Rect.fromLTWH(0, 15, 200, 20));
    return footerTemplate;
  }
}
