import 'package:fl_app/models/product_model.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class ProductGridItem extends StatelessWidget {
  final ProductModel product;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool showComission;
  const ProductGridItem({
    super.key,
    required this.product,
    this.onTap,
    this.onLongPress,
    this.showComission = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        clipBehavior:
            Clip.hardEdge, // Garante que o InkWell seja cortado pelo Card
        child: InkWell(
          onTap: onTap,
          onLongPress: onLongPress,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Hero(
                        tag: product.id!,
                        child: product.imagem != null
                            ? CachedNetworkImage(
                                imageUrl: product.imagem!,
                                fit: BoxFit.cover,
                                memCacheHeight: 500,
                                memCacheWidth: 500,
                                progressIndicatorBuilder:
                                    (context, url, downloadProgress) => Center(
                                  child: CircularProgressIndicator(
                                    value: downloadProgress.progress,
                                    color: Get.isDarkMode
                                        ? Colors.indigo[400]
                                        : Colors.indigo[900],
                                    strokeCap: StrokeCap.round,
                                  ),
                                ),
                                useOldImageOnUrlChange: true,
                                errorWidget: (context, url, error) =>
                                    GestureDetector(
                                  onTap: onTap,
                                  child: Column(
                                    children: [
                                      Expanded(
                                        child: Container(
                                          width: double.maxFinite,
                                          height: double.maxFinite,
                                          color: Colors.grey[300],
                                          child: Center(
                                            child: FaIcon(
                                              FontAwesomeIcons.image,
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              )
                            : Container(
                                color: Theme.of(context).colorScheme.surface,
                                child: Center(
                                  child: FaIcon(
                                    FontAwesomeIcons.image,
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                              ),
                      ),
                    ),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product.nome,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context).textTheme.titleMedium,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                'R\$ ${product.valor.toStringAsFixed(2)}',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color!
                                          .withOpacity(0.8),
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )
                ],
              ),
              if (product.isFavorite)
                Positioned(
                  top: 5,
                  right: 5,
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor:
                        Get.isDarkMode ? Colors.black : Colors.grey[300],
                    child: Icon(
                      Icons.star,
                      color: Colors.yellow[700],
                      size: 24,
                    ),
                  ),
                ),
              if (product.temComissao && showComission)
                Positioned(
                  top: 5,
                  left: 5,
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor:
                        Get.isDarkMode ? Colors.black : Colors.grey[300],
                    child: Icon(
                      FontAwesomeIcons.dollarSign,
                      color: Colors.green[700],
                      size: 24,
                    ),
                  ),
                ),
              if (product.isDeleted)
                Positioned(
                  top: 5,
                  right: 5,
                  child: CircleAvatar(
                    radius: 15,
                    backgroundColor:
                        Get.isDarkMode ? Colors.black : Colors.grey[300],
                    child: Icon(
                      Icons.delete,
                      color: Colors.red[700],
                      size: 24,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
