import 'package:fl_app/models/order_model.dart';
import 'package:flutter/material.dart';

class OrderAttentionIcons extends StatelessWidget {
  const OrderAttentionIcons({
    super.key,
    required this.order,
  });

  final OrderModel order;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (_hasPendingDelivery)
          _buildIconContainer(
            icon: Icons.delivery_dining,
            color: Colors.indigo,
          ),
        if (_hasMissingLocation)
          _buildIconContainer(
            icon: Icons.location_off,
            color: Colors.red[300]!,
          ),
      ],
    );
  }

  bool get _hasPendingDelivery =>
      order.toDelivery ||
      order.products.any((product) => product.quantityToDelivery > 0);

  bool get _hasMissingLocation =>
      order.clientLatitude == null || order.clientLongitude == null;

  Widget _buildIconContainer({required IconData icon, required Color color}) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(5),
      ),
      padding: const EdgeInsets.all(2),
      margin: const EdgeInsets.only(top: 4),
      child: Icon(icon, color: color, size: 20),
    );
  }
}
