import 'package:fl_app/models/order_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrderDaysFromNow extends StatelessWidget {
  const OrderDaysFromNow({
    super.key,
    required this.order,
  });

  final OrderModel order;

  int get daysFromNow => calculateDaysFromNow();

  @override
  Widget build(BuildContext context) {
    final label = _getLabel();
    final color = _getColor();

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(5),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Text(
        label,
        style: TextStyle(color: color, fontSize: 12),
        textAlign: TextAlign.center,
      ),
    );
  }

  int calculateDaysFromNow() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day, 23, 59, 59);
    final orderDate =
        DateTime(order.date.year, order.date.month, order.date.day);
    return today.difference(orderDate).inDays;
  }

  String _getLabel() {
    if (daysFromNow == 0) {
      return 'Hoje';
    } else if (daysFromNow == 1) {
      return 'Ontem';
    } else {
      return '$daysFromNow dias';
    }
  }

  Color _getColor() {
    if (daysFromNow == 0) {
      return Colors.green;
    } else if (daysFromNow < 30) {
      return Get.isDarkMode ? Colors.grey[300]! : Colors.grey[800]!;
    } else if (daysFromNow < 60) {
      return Get.isDarkMode ? Colors.orange[100]! : Colors.orange[800]!;
    } else {
      return Get.isDarkMode ? Colors.red[100]! : Colors.red;
    }
  }
}
