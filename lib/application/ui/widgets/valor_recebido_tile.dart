import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

class PaymentDetailsModal extends StatelessWidget {
  final String title;
  final double total;
  final Color color;
  final IconData icon;
  final List<OrderModel> orders;
  final PaymentMethod paymentType;
  final double Function(PaymentMethod, OrderModel) onGetValue;

  const PaymentDetailsModal({
    super.key,
    required this.title,
    required this.total,
    required this.color,
    required this.icon,
    required this.orders,
    required this.paymentType,
    required this.onGetValue,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: color, width: 2),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: Get.height * 0.8,
          maxWidth: Get.width * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(icon, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 18,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          NumberFormatHelper.format(total),
                          style: const TextStyle(
                            fontSize: 22,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            Flexible(
              child: ListView.builder(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: orders.length,
                itemBuilder: (context, index) {
                  final order = orders[index];
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      InkWell(
                        onTap: () {
                          Get.back();
                          Get.toNamed('/view-order', arguments: order);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      order.clientName,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      order.clientAddress,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: color.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      '+ ${NumberFormatHelper.format(
                                        onGetValue(paymentType, order),
                                      )}',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: color,
                                      ),
                                    ),
                                    Text(
                                      order.payments
                                          .where((p) =>
                                              p.paymentMethod == paymentType)
                                          .where((p) =>
                                              DateTimeHelper.isSameDay(
                                                  p.date, DateTime.now()))
                                          .map((p) => DateTimeHelper.timeFormat
                                              .format(p.date))
                                          .join(', '),
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (index < orders.length - 1) const Divider(height: 1),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class PaymentButton extends StatelessWidget {
  final Color color;
  final IconData icon;
  final double value;
  final VoidCallback? onTap;

  const PaymentButton({
    super.key,
    required this.color,
    required this.icon,
    required this.value,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: Column(
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(height: 4),
              Text(
                NumberFormatHelper.format(value),
                style: const TextStyle(fontSize: 16, color: Colors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ValorRecebidoTile extends StatelessWidget {
  const ValorRecebidoTile({
    super.key,
    required this.receivedValue,
    required this.receivedValueDinheiro,
    required this.receivedValueCartao,
    required this.receivedValuePix,
    this.ordersDinheiro = const [],
    this.ordersCartao = const [],
    this.ordersPix = const [],
    this.title = 'Recebido Hoje',
  });

  final double receivedValue;
  final double receivedValueDinheiro;
  final double receivedValueCartao;
  final double receivedValuePix;

  final List<OrderModel> ordersDinheiro;
  final List<OrderModel> ordersCartao;
  final List<OrderModel> ordersPix;

  final String title;

  double receivedValueInType(PaymentMethod type, OrderModel order) {
    double value = 0.0;
    switch (type) {
      case PaymentMethod.dinheiro:
        for (final payment in order.payments) {
          if (payment.paymentMethod == PaymentMethod.dinheiro &&
              DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
            value += payment.value;
          }
        }
        return value;
      case PaymentMethod.cartao:
        for (final payment in order.payments) {
          if (payment.paymentMethod == PaymentMethod.cartao &&
              DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
            value += payment.value;
          }
        }
        return value;
      case PaymentMethod.pix:
        for (final payment in order.payments) {
          if (payment.paymentMethod == PaymentMethod.pix &&
              DateTimeHelper.isSameDay(payment.date, DateTime.now())) {
            value += payment.value;
          }
        }
        return value;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Theme(
        data: Get.theme.copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          childrenPadding: const EdgeInsets.only(bottom: 16),
          dense: true,
          title: Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: '$title: ',
                  style: const TextStyle(fontSize: 16),
                ),
                TextSpan(
                  text: NumberFormatHelper.format(receivedValue),
                  style: const TextStyle(
                      fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  PaymentButton(
                    color: Colors.green[700]!,
                    icon: FontAwesomeIcons.moneyBillWave,
                    value: receivedValueDinheiro,
                    onTap: ordersDinheiro.isEmpty
                        ? null
                        : () {
                            Get.dialog(
                              PaymentDetailsModal(
                                title: 'Total em dinheiro',
                                total: receivedValueDinheiro,
                                color: Colors.green[700]!,
                                icon: FontAwesomeIcons.moneyBillWave,
                                orders: ordersDinheiro,
                                paymentType: PaymentMethod.dinheiro,
                                onGetValue: receivedValueInType,
                              ),
                            );
                          },
                  ),
                  const SizedBox(width: 8),
                  PaymentButton(
                    color: Colors.blue[700]!,
                    icon: FontAwesomeIcons.creditCard,
                    value: receivedValueCartao,
                    onTap: ordersCartao.isEmpty
                        ? null
                        : () {
                            Get.dialog(
                              PaymentDetailsModal(
                                title: 'Total em cartão',
                                total: receivedValueCartao,
                                color: Colors.blue[700]!,
                                icon: FontAwesomeIcons.creditCard,
                                orders: ordersCartao,
                                paymentType: PaymentMethod.cartao,
                                onGetValue: receivedValueInType,
                              ),
                            );
                          },
                  ),
                  const SizedBox(width: 8),
                  PaymentButton(
                    color: Colors.deepPurple[700]!,
                    icon: FontAwesomeIcons.pix,
                    value: receivedValuePix,
                    onTap: ordersPix.isEmpty
                        ? null
                        : () {
                            Get.dialog(
                              PaymentDetailsModal(
                                title: 'Total em pix',
                                total: receivedValuePix,
                                color: Colors.deepPurple[700]!,
                                icon: FontAwesomeIcons.pix,
                                orders: ordersPix,
                                paymentType: PaymentMethod.pix,
                                onGetValue: receivedValueInType,
                              ),
                            );
                          },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
