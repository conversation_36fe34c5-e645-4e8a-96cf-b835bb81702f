import 'package:fl_app/application/helpers/date_time_helper.dart';
import 'package:fl_app/application/helpers/number_format_helper.dart';
import 'package:fl_app/application/ui/widgets/order_attention_icons.dart';
import 'package:fl_app/application/ui/widgets/order_days_from_now.dart';
import 'package:fl_app/models/day_making_item.dart';
import 'package:fl_app/models/order_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OrderListCard extends StatelessWidget {
  const OrderListCard({
    super.key,
    required this.order,
    required this.onTap,
    this.showDaysFromNowLeading = true,
    this.showDaysFromNowBottom = false,
  });

  final bool showDaysFromNowLeading;
  final bool showDaysFromNowBottom;
  final OrderModel order;
  final void Function(OrderModel order) onTap;

  @override
  Widget build(BuildContext context) {
    final DayMarkingItem? activeDayMarkingItem = _getActiveDayMarkingItem();

    return Card(
      color: _getCardColor(),
      child: ListTile(
        leading: _buildLeading(),
        title: _buildTitle(),
        subtitle: _buildSubtitle(activeDayMarkingItem),
        trailing: _buildTrailing(),
        onTap: () => onTap(order),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      order.clientName,
      style: const TextStyle(fontWeight: FontWeight.bold),
    );
  }

  Widget _buildSubtitle(DayMarkingItem? activeDayMarkingItem) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${order.clientAddress} - ${order.routeName ?? 'Rota não encontrada'}',
        ),
        if (activeDayMarkingItem != null && showDaysFromNowBottom) ...[
          const SizedBox(height: 4),
          OrderDaysFromNow(order: order),
        ],
      ],
    );
  }

  Widget? _buildLeading() {
    final bool needsAttention = _orderNeedsAttention();

    if (!showDaysFromNowLeading && !needsAttention) {
      return null;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showDaysFromNowLeading) OrderDaysFromNow(order: order),
        if (needsAttention) OrderAttentionIcons(order: order),
      ],
    );
  }

  Widget _buildTrailing() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          NumberFormatHelper.format(order.getTotalPending()),
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: Get.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        Text(
          '${DateTimeHelper.getFormattedDate(order.date)} ${DateTimeHelper.getFormattedTime(order.date)}',
        ),
      ],
    );
  }

  bool _orderNeedsAttention() {
    final hasPendingDelivery = order.toDelivery ||
        order.products.any((product) => product.quantityToDelivery > 0);
    final missingLocation =
        order.clientLatitude == null || order.clientLongitude == null;
    return hasPendingDelivery || missingLocation;
  }

  Color _getCardColor() {
    if (order.isPaid || order.isJoined) {
      return Get.isDarkMode ? Colors.green[800]! : Colors.green[100]!;
    }

    if (order.dayMarkingItems.any((item) => item.active)) {
      return Get.isDarkMode ? Colors.indigo[700]! : Colors.indigo[100]!;
    }

    return Get.isDarkMode ? Colors.grey[900]! : Colors.white;
  }

  DayMarkingItem? _getActiveDayMarkingItem() {
    return order.dayMarkingItems.where((item) => item.active).firstOrNull;
  }
}
