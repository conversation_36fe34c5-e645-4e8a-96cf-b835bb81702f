import 'package:flutter/material.dart';
import 'package:get/get.dart';

mixin LoaderMixin on GetxController {
  void loaderListener(RxBool isLoading, {bool barrierDismissible = false}) {
    ever<bool>(isLoading, (loading) async {
      if (loading) {
        await Get.dialog(
          const Center(
            child: CircularProgressIndicator(
              strokeCap: StrokeCap.round,
            ),
          ),
          barrierDismissible: barrierDismissible,
        );
      } else {
        Get.back();
      }
    });
  }
}
