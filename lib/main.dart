import 'package:move_to_background/move_to_background.dart';
import 'package:move_to_background/move_to_background.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:fl_app/application/bindings/application_bindings.dart';
import 'package:fl_app/application/ui/ui_config.dart';
import 'package:fl_app/modules/admin/admin_module.dart';
import 'package:fl_app/modules/billing/billing_module.dart';
import 'package:fl_app/modules/bluetooth/bluetooth_module.dart';
import 'package:fl_app/modules/cart/cart_module.dart';
import 'package:fl_app/modules/client/client_module.dart';
import 'package:fl_app/modules/home/<USER>';
import 'package:fl_app/modules/location/location_module.dart';
import 'package:fl_app/modules/login/login_module.dart';
import 'package:fl_app/modules/non_cobrador_billing/non_cobrador_billing_module.dart';
import 'package:fl_app/modules/order/order_module.dart';
import 'package:fl_app/modules/product/product_module.dart';
import 'package:fl_app/modules/reports/reports_module.dart';
import 'package:fl_app/modules/sale_stock/sale_stock_module.dart';
import 'package:fl_app/modules/sales_routes/sales_routes_module.dart';
import 'package:fl_app/modules/select_location/select_location_module.dart';
import 'package:fl_app/modules/settings/settings_module.dart';
import 'package:fl_app/modules/splash/download_status_model.dart';
import 'package:fl_app/modules/splash/splash_module.dart';
import 'package:fl_app/modules/users/users_module.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';
import 'package:shorebird_code_push/shorebird_code_push.dart';
import 'firebase_options.dart';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';

final ShorebirdUpdater shorebirdUpdater = ShorebirdUpdater();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
  await Hive.initFlutter();
  var downloadStatusBox = await Hive.openBox('download_status');
  print('downloadStatusBox: ${downloadStatusBox.toMap()}');
  if (!downloadStatusBox.containsKey('status')) {
    await downloadStatusBox.put('status', DownloadStatusModel().toMap());
  }
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    Intl.defaultLocale = 'pt_BR';
    return GetMaterialApp(
      builder: (context, child) => MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!),
      localizationsDelegates: const [...GlobalMaterialLocalizations.delegates],
      supportedLocales: const [Locale('pt', 'BR')],
      title: UiConfig.title,
      debugShowCheckedModeBanner: false,
      theme: UiConfig.theme,
      darkTheme: UiConfig.darkTheme,
      initialBinding: ApplicationBindings(),
      getPages: [
        ...SplashModule().routers,
        ...LoginModule().routers,
        ...HomeModule().routers,
        ...SalesRoutesModule().routers,
        ...ProductModule().routers,
        ...ClientModule().routers,
        ...SelectLocationModule().routers,
        ...CartModule().routers,
        ...OrderModule().routers,
        ...BluetoothModule().routers,
        ...UsersModule().routers,
        ...LocationModule().routers,
        ...SettingsModule().routers,
        ...BillingModule().routers,
        ...NonCobradorBillingModule().routers,
        ...ReportsModule().routers,
        ...AdminModule().routers,
        ...SaleStockModule().routers,
      ],
    );
  }
}
