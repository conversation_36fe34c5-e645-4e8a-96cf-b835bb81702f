name: fl_app
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.4.3+1

environment:
  sdk: '>=3.5.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  firebase_core: ^3.9.0
  cloud_firestore: ^5.6.0
  firebase_storage: ^12.3.7
  firebase_crashlytics: ^4.2.0

  # AUTH
  firebase_auth: ^5.3.4
  google_sign_in: ^6.1.2
  sign_in_button: ^3.2.0

  diacritic: ^0.1.3
  intl: ^0.19.0
  get: ^4.6.6
  cached_network_image: ^3.2.3
  image_picker: ^1.1.2
  image_cropper: ^5.0.1
  extended_image: ^9.0.9
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  geolocator: ^13.0.2
  google_maps_flutter: ^2.2.8
  action_slider: ^0.7.0
  flutter_slidable: ^3.0.0
  mask_text_input_formatter: ^2.7.0
  font_awesome_flutter: ^10.6.0
  print_bluetooth_thermal: ^1.0.9
  esc_pos_utils_plus: ^2.0.3
  internet_connection_checker_plus: ^2.1.0
  widgets_to_image: ^1.0.0
  share_plus: ^10.1.3
  path_provider: ^2.1.2
  url_launcher: ^6.3.1
  restart_app: ^1.2.1
  uuid: ^4.3.3

  pdf: ^3.10.8
  syncfusion_flutter_pdf: ^28.1.37
  #flutter_to_pdf: ^0.2.0
  
  weekly_date_picker: ^1.4.2
  device_information: ^0.0.4
  multi_select_flutter: ^4.1.3
  easy_pdf_viewer: ^1.0.8

  once: ^1.6.1

  permission_handler: ^11.3.1
  light_flutter_nearby_connections: ^0.0.4
  device_info_plus: ^11.2.0
  flutter_carousel_widget: ^3.1.0
  shorebird_code_push: ^2.0.2

  crclib: ^3.0.0
  qr_flutter: ^4.1.0
  move_to_background:

dev_dependencies:
  flutter_test:
    sdk: flutter
  #icons_launcher: ^2.1.7

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - shorebird.yaml
    - pubspec.yaml
    - assets/json/map_dark.json
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
      - family: Roboto
        fonts:
          - asset: assets/fonts/Roboto-Regular.ttf
          - asset: assets/fonts/Roboto-Medium.ttf
            weight: 500
          - asset: assets/fonts/Roboto-Bold.ttf
            weight: 700
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
icons_launcher:
  image_path: "assets/icon/icon2.png"
  platforms:
    android:
      enable: true
#dart run icons_launcher:create