# FL_APP

## Cloud Functions

Escrever as funções que serão executadas no Firebase Cloud Functions na pasta `functions/`.
no arquivo index.js.

- [x] firebase init (firebase login --reauth se der erro)
- [x] firebase deploy toda vez que alterar algo

# Configuração keystore

- Java 17
- Android Studio Hedgehog | 2023.1.1 Patch 2
- cd /home/<USER>/.android
  - validar se o arquivo `debug.keystore` existe
  - se não existir, executar `keytool -genkey -v -keystore debug.keystore -alias androiddebugkey -keyalg RSA -keysize 2048 -validity 10000`
- se existir, executar `keytool -list -v -keystore debug.keystore`
- adicionar o SHA-1 ao aplicativo do firebase e na google cloud console-> API & Services -> Credentials -> Android key (auto created by Firebase)